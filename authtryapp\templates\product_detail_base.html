{% extends 'base_new.html' %}
{% load static %}

{% block title %}{{ product.name }} - iTechStore{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/sales_products.css' %}">
<style>
    /* Additional styles for product detail page */
    .product-detail-container {
        padding: 60px 0;
    }

    .breadcrumb {
        display: flex;
        margin-bottom: 30px;
        font-size: 14px;
    }

    .breadcrumb a {
        color: #777;
        text-decoration: none;
    }

    .breadcrumb a:hover {
        color: #4dabf7;
    }

    .breadcrumb .separator {
        margin: 0 10px;
        color: #ddd;
    }

    .breadcrumb .current {
        color: #333;
        font-weight: 500;
    }

    .product-detail {
        display: flex;
        gap: 40px;
        margin-bottom: 60px;
    }

    .product-gallery {
        width: 50%;
    }

    .product-info {
        width: 50%;
    }

    .main-image {
        width: 100%;
        height: 400px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: white;
        border-radius: 10px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        overflow: hidden;
    }

    .main-image img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }

    .thumbnail-gallery {
        display: flex;
        gap: 10px;
    }

    .thumbnail {
        width: 80px;
        height: 80px;
        border-radius: 5px;
        overflow: hidden;
        cursor: pointer;
        border: 2px solid transparent;
        transition: all 0.3s;
    }

    .thumbnail.active {
        border-color: #4dabf7;
    }

    .thumbnail:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .thumbnail img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .product-title {
        font-size: 32px;
        margin-bottom: 10px;
        color: #333;
    }

    .product-category {
        color: #777;
        margin-bottom: 20px;
        font-size: 14px;
    }

    .product-category a {
        color: #4dabf7;
        text-decoration: none;
    }

    .product-category a:hover {
        text-decoration: underline;
    }

    .product-price {
        font-size: 28px;
        font-weight: 600;
        color: #333;
        margin-bottom: 20px;
    }

    .stock-status {
        display: inline-block;
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 600;
        text-transform: uppercase;
        margin-bottom: 20px;
    }

    .stock-status.available {
        background-color: #e6f7ee;
        color: #2e7d32;
    }

    .stock-status.preorder {
        background-color: #fff8e1;
        color: #ff8f00;
    }

    .stock-status.notavailable {
        background-color: #ffebee;
        color: #c62828;
    }

    .product-description {
        margin-bottom: 30px;
        line-height: 1.6;
        color: #555;
    }

    .quantity-control {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
    }

    .quantity-btn {
        width: 40px;
        height: 40px;
        background-color: #f5f5f5;
        border: none;
        border-radius: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 18px;
        transition: background-color 0.3s;
    }

    .quantity-btn:hover {
        background-color: #e0e0e0;
    }

    .quantity-input {
        width: 60px;
        height: 40px;
        text-align: center;
        border: 1px solid #ddd;
        border-radius: 5px;
        margin: 0 10px;
        font-size: 16px;
    }

    .add-to-cart-btn {
        width: 100%;
        padding: 15px;
        background: linear-gradient(to right, #4dabf7, #0c8599);
        color: white;
        border: none;
        border-radius: 5px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: opacity 0.3s;
        margin-bottom: 20px;
    }

    .add-to-cart-btn:hover {
        opacity: 0.9;
    }

    .add-to-cart-btn.disabled {
        background: #ccc;
        cursor: not-allowed;
    }

    .product-meta {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #eee;
    }

    .meta-item {
        display: flex;
        margin-bottom: 10px;
        font-size: 14px;
    }

    .meta-label {
        width: 120px;
        color: #777;
    }

    .meta-value {
        color: #333;
    }

    .product-tabs {
        margin-bottom: 60px;
    }

    .tab-buttons {
        display: flex;
        border-bottom: 1px solid #eee;
        margin-bottom: 30px;
    }

    .tab-button {
        padding: 15px 30px;
        background: none;
        border: none;
        border-bottom: 3px solid transparent;
        font-size: 16px;
        font-weight: 600;
        color: #777;
        cursor: pointer;
        transition: all 0.3s;
    }

    .tab-button.active {
        color: #4dabf7;
        border-bottom-color: #4dabf7;
    }

    .tab-button:hover {
        color: #4dabf7;
    }

    .tab-content {
        display: none;
        line-height: 1.6;
        color: #555;
    }

    .tab-content.active {
        display: block;
    }

    .related-products {
        margin-bottom: 60px;
    }

    .related-products h2 {
        font-size: 24px;
        margin-bottom: 30px;
        color: #333;
    }

    .product-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 30px;
    }

    @media (max-width: 992px) {
        .product-grid {
            grid-template-columns: repeat(3, 1fr);
        }
    }

    @media (max-width: 768px) {
        .product-detail {
            flex-direction: column;
        }

        .product-gallery,
        .product-info {
            width: 100%;
        }

        .product-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 576px) {
        .product-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Product Detail Section -->
<section class="product-detail-container">
    <div class="container">
        <!-- Breadcrumb -->
        <div class="breadcrumb">
            <a href="{% url 'home' %}">Home</a>
            <span class="separator">/</span>
            {% if product.category %}
                <a href="{% url 'category_view' product.category.slug %}">{{ product.category.name }}</a>
                <span class="separator">/</span>
            {% endif %}
            <span class="current">{{ product.name }}</span>
        </div>

        <!-- Product Detail -->
        <div class="product-detail">
            <div class="product-gallery">
                <div class="main-image">
                    {% if product.image %}
                        <img src="{{ product.image.url }}" alt="{{ product.name }}" id="main-product-image">
                    {% else %}
                        <img src="https://via.placeholder.com/500x400?text=Product+Image" alt="{{ product.name }}" id="main-product-image">
                    {% endif %}
                </div>
                <div class="thumbnail-gallery">
                    <!-- Main image thumbnail -->
                    <div class="thumbnail active" data-image="{% if product.image %}{{ product.image.url }}{% else %}https://via.placeholder.com/500x400?text=Product+Image{% endif %}">
                        <img src="{% if product.image %}{{ product.image.url }}{% else %}https://via.placeholder.com/500x400?text=Product+Image{% endif %}" alt="{{ product.name }}">
                    </div>
                    <!-- Additional thumbnails (placeholder) -->
                    <div class="thumbnail" data-image="https://via.placeholder.com/500x400?text=Image+2">
                        <img src="https://via.placeholder.com/80x80?text=Image+2" alt="{{ product.name }}">
                    </div>
                    <div class="thumbnail" data-image="https://via.placeholder.com/500x400?text=Image+3">
                        <img src="https://via.placeholder.com/80x80?text=Image+3" alt="{{ product.name }}">
                    </div>
                    <div class="thumbnail" data-image="https://via.placeholder.com/500x400?text=Image+4">
                        <img src="https://via.placeholder.com/80x80?text=Image+4" alt="{{ product.name }}">
                    </div>
                </div>
            </div>

            <div class="product-info">
                <h1 class="product-title">{{ product.name }}</h1>

                {% if product.brand %}
                    <div class="product-brand" style="font-size: 16px; margin-bottom: 10px; color: #666; font-weight: 600;">
                        {{ product.brand.name }}
                    </div>
                {% endif %}

                {% if product.category %}
                    <div class="product-category">
                        Category: <a href="{% url 'category_view' product.category.slug %}">{{ product.category.name }}</a>
                    </div>
                {% endif %}

                <!-- Enhanced Price Section with Sales Features -->
                <div class="product-price-section" style="margin-bottom: 20px;">
                    {% if product.is_on_sale %}
                        <div class="price-row" style="display: flex; align-items: center; gap: 15px; margin-bottom: 8px;">
                            <span class="current-price" style="font-size: 32px; font-weight: 700; color: #dc3545;">₨. {{ product.display_price }}</span>
                            <span class="original-price" style="font-size: 24px; color: #999; text-decoration: line-through;">₨. {{ product.original_price }}</span>
                        </div>
                        <div class="savings-info" style="display: flex; align-items: center; gap: 15px;">
                            <span class="discount-percentage" style="background: #dc3545; color: white; padding: 4px 8px; border-radius: 4px; font-size: 14px; font-weight: 600;">{{ product.discount_percentage|floatformat:0 }}% OFF</span>
                            <span class="savings-amount" style="font-size: 16px; color: #28a745; font-weight: 600;">Save ₨. {{ product.discount_amount|floatformat:0 }}</span>
                        </div>
                    {% else %}
                        <div class="current-price" style="font-size: 32px; font-weight: 700; color: #333;">₨. {{ product.display_price }}</div>
                    {% endif %}
                </div>

                <!-- Product Rating -->
                {% if product.rating > 0 %}
                <div class="product-rating" style="display: flex; align-items: center; gap: 10px; margin-bottom: 15px;">
                    <div class="rating-stars" style="display: flex; gap: 2px;">
                        {% for star in product.rating_stars_range %}
                            {% if star <= product.rating %}
                                <i class="fas fa-star" style="color: #ffc107; font-size: 16px;"></i>
                            {% elif star <= product.rating|add:0.5 %}
                                <i class="fas fa-star-half-alt" style="color: #ffc107; font-size: 16px;"></i>
                            {% else %}
                                <i class="far fa-star" style="color: #ddd; font-size: 16px;"></i>
                            {% endif %}
                        {% endfor %}
                    </div>
                    <span class="rating-text" style="font-weight: 600; color: #333;">{{ product.rating_display }}</span>
                    {% if product.review_count > 0 %}
                        <span class="review-count" style="color: #666;">({{ product.review_count }} Review{{ product.review_count|pluralize }})</span>
                    {% endif %}
                </div>
                {% endif %}

                <!-- Product Badges -->
                <div class="product-badges" style="display: flex; gap: 8px; margin-bottom: 15px;">
                    {% if product.is_sponsored %}
                        <span class="badge sponsored-badge" style="background: #ff6b35; color: white; padding: 4px 8px; border-radius: 4px; font-size: 11px; font-weight: 600; text-transform: uppercase;">Sponsored</span>
                    {% endif %}
                    {% if product.has_gift %}
                        <span class="badge gift-badge" style="background: #28a745; color: white; padding: 4px 8px; border-radius: 4px; font-size: 11px; font-weight: 600; text-transform: uppercase;">Gift With Purchase</span>
                    {% endif %}
                </div>

                {% if product.stock_status == 'available' %}
                    <div class="stock-status available">In Stock</div>
                {% elif product.stock_status == 'preorder' %}
                    <div class="stock-status preorder">Pre-order</div>
                {% elif product.stock_status == 'notavailable' %}
                    <div class="stock-status notavailable">Out of Stock</div>
                {% endif %}

                <!-- Short Specifications -->
                {% if product.short_specs %}
                <div class="product-short-specs" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                    <h4 style="margin: 0 0 8px 0; font-size: 16px; color: #333;">Key Specifications</h4>
                    <p style="margin: 0; color: #666; line-height: 1.5;">{{ product.short_specs }}</p>
                </div>
                {% endif %}

                <!-- Gift Description -->
                {% if product.has_gift and product.gift_description %}
                <div class="gift-description" style="display: flex; align-items: center; gap: 8px; padding: 12px 15px; background: #f8f9fa; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #28a745;">
                    <i class="fas fa-gift" style="color: #28a745; font-size: 18px;"></i>
                    <span style="font-weight: 600; color: #28a745;">Free Gift:</span>
                    <span style="color: #333;">{{ product.gift_description }}</span>
                </div>
                {% endif %}

                <div class="product-description">
                    {{ product.description|linebreaks }}
                </div>

                <form method="post" action="{% url 'add_to_cart' product.id %}" class="add-to-cart-form">
                    {% csrf_token %}
                    <div class="quantity-control">
                        <button type="button" class="quantity-btn decrease-quantity">-</button>
                        <input type="number" name="quantity" class="quantity-input" value="1" min="1" max="10">
                        <button type="button" class="quantity-btn increase-quantity">+</button>
                    </div>

                    {% if product.stock_status == 'notavailable' %}
                        <button type="button" class="add-to-cart-btn disabled" disabled>Out of Stock</button>
                    {% elif product.stock_status == 'preorder' %}
                        <button type="submit" class="add-to-cart-btn">Pre-order Now</button>
                    {% else %}
                        <button type="submit" class="add-to-cart-btn">Add to Cart</button>
                    {% endif %}
                </form>

                <div class="product-meta">
                    <div class="meta-item">
                        <div class="meta-label">SKU:</div>
                        <div class="meta-value">{{ product.id }}</div>
                    </div>
                    {% if product.brand %}
                        <div class="meta-item">
                            <div class="meta-label">Brand:</div>
                            <div class="meta-value">
                                <a href="{% url 'brand_detail' product.brand.slug %}">{{ product.brand.name }}</a>
                            </div>
                        </div>
                    {% endif %}
                    {% if product.category %}
                        <div class="meta-item">
                            <div class="meta-label">Category:</div>
                            <div class="meta-value">{{ product.category.name }}</div>
                        </div>
                    {% endif %}
                    <div class="meta-item">
                        <div class="meta-label">Share:</div>
                        <div class="meta-value">
                            <a href="#" title="Share on Facebook"><i class="fab fa-facebook-f"></i></a>
                            <a href="#" title="Share on Twitter"><i class="fab fa-twitter"></i></a>
                            <a href="#" title="Share on Pinterest"><i class="fab fa-pinterest-p"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Product Tabs -->
        <div class="product-tabs">
            <div class="tab-buttons">
                <button class="tab-button active" data-tab="description">Description</button>
                <button class="tab-button" data-tab="specifications">Specifications</button>
                <button class="tab-button" data-tab="reviews">Reviews</button>
            </div>

            <div class="tab-content active" id="description-tab">
                <p>{{ product.description|linebreaks }}</p>
            </div>

            <div class="tab-content" id="specifications-tab">
                <p>Detailed specifications will be displayed here.</p>
            </div>

            <div class="tab-content" id="reviews-tab">
                <p>Product reviews will be displayed here.</p>
            </div>
        </div>

        <!-- Related Products -->
        <div class="related-products">
            <h2>Related Products</h2>
            <div class="product-grid">
                <!-- Sample related products - replace with actual related products -->
                <div class="product-card">
                    <div class="product-image">
                        <img src="https://via.placeholder.com/300x200?text=Related+Product" alt="Related Product">
                        <div class="stock-status available">IN STOCK</div>
                    </div>
                    <div class="product-info">
                        <h3>Related Product 1</h3>
                        <p class="price">₨. 49,999</p>
                        <div class="product-buttons">
                            <button class="add-to-cart">Add to Cart</button>
                            <a href="#" class="view-cart-btn">View Cart</a>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">
                        <img src="https://via.placeholder.com/300x200?text=Related+Product" alt="Related Product">
                        <div class="stock-status preorder">PRE-ORDER</div>
                    </div>
                    <div class="product-info">
                        <h3>Related Product 2</h3>
                        <p class="price">₨. 59,999</p>
                        <div class="product-buttons">
                            <button class="add-to-cart preorder-btn">Pre-order Now</button>
                            <a href="#" class="view-cart-btn">View Cart</a>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">
                        <img src="https://via.placeholder.com/300x200?text=Related+Product" alt="Related Product">
                        <div class="stock-status available">IN STOCK</div>
                    </div>
                    <div class="product-info">
                        <h3>Related Product 3</h3>
                        <p class="price">₨. 69,999</p>
                        <div class="product-buttons">
                            <button class="add-to-cart">Add to Cart</button>
                            <a href="#" class="view-cart-btn">View Cart</a>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">
                        <img src="https://via.placeholder.com/300x200?text=Related+Product" alt="Related Product">
                        <div class="stock-status notavailable">OUT OF STOCK</div>
                    </div>
                    <div class="product-info">
                        <h3>Related Product 4</h3>
                        <p class="price">₨. 79,999</p>
                        <div class="product-buttons">
                            <button class="add-to-cart disabled" disabled>Out of Stock</button>
                            <a href="#" class="view-cart-btn">View Cart</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Thumbnail gallery functionality
        const thumbnails = document.querySelectorAll('.thumbnail');
        const mainImage = document.getElementById('main-product-image');

        thumbnails.forEach(thumbnail => {
            thumbnail.addEventListener('click', function() {
                // Remove active class from all thumbnails
                thumbnails.forEach(t => t.classList.remove('active'));

                // Add active class to clicked thumbnail
                this.classList.add('active');

                // Update main image
                const imageUrl = this.dataset.image;
                mainImage.src = imageUrl;
            });
        });

        // Quantity control functionality
        const decreaseBtn = document.querySelector('.decrease-quantity');
        const increaseBtn = document.querySelector('.increase-quantity');
        const quantityInput = document.querySelector('.quantity-input');

        decreaseBtn.addEventListener('click', function() {
            let value = parseInt(quantityInput.value);
            if (value > 1) {
                quantityInput.value = value - 1;
            }
        });

        increaseBtn.addEventListener('click', function() {
            let value = parseInt(quantityInput.value);
            if (value < 10) {
                quantityInput.value = value + 1;
            }
        });

        // Tab functionality
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons and contents
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));

                // Add active class to clicked button
                this.classList.add('active');

                // Show corresponding content
                const tabId = `${this.dataset.tab}-tab`;
                document.getElementById(tabId).classList.add('active');
            });
        });
    });
</script>
{% endblock %}
{% endblock %}
