{% comment %}
    Reusable product carousel template
    Usage: {% include 'includes/product_carousel.html' with carousel_id='featured-products' title=section.title products=products %}
{% endcomment %}

<section class="product-carousel-section">
    <div class="container">
        <div class="section-heading">
            <div class="section-title-wrapper">
                <h2 class="section-title">{{ title }}</h2>
                {% if subtitle %}
                <p class="section-subtitle">{{ subtitle }}</p>
                {% endif %}
            </div>
            <div class="carousel-controls">
                <button class="carousel-control" data-carousel-prev="{{ carousel_id }}">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="carousel-control" data-carousel-next="{{ carousel_id }}">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>

        <div class="product-carousel" id="{{ carousel_id }}">
            <div class="product-carousel-inner">
                {% for product in products %}
                <div class="product-slide">
                    {% include 'includes/sales_product_card.html' with product=product %}
                </div>
                {% empty %}
                <!-- Default products if no products in database -->
                <div class="product-slide">
                    <div class="sales-product-card" data-product-id="demo-carousel">
                        <div class="sales-product-image-container">
                            <img src="https://via.placeholder.com/300x200?text=Product+Image" alt="Product" class="sales-product-image">
                            <div class="sales-product-badges">
                                <span class="availability-badge available">Available</span>
                            </div>
                        </div>
                        <div class="sales-product-info">
                            <div class="sales-product-title-section">
                                <h3 class="sales-product-title">Product Name</h3>
                                <p class="sales-product-specs">BRAND</p>
                            </div>
                            <div class="sales-product-pricing">
                                <div class="sales-product-price">₨. 199.99</div>
                            </div>
                            <div class="sales-product-actions">
                                <button class="add-to-cart-btn primary" data-product-id="demo-carousel">
                                    <i class="fas fa-shopping-cart"></i>
                                    Add to Cart
                                </button>
                                <a href="#" class="quick-view-btn" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <div class="carousel-dots" data-carousel-dots="{{ carousel_id }}"></div>
    </div>
</section>
