from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils.text import slugify
from decimal import Decimal
from authtryapp.models import Product


class SaleCategory(models.Model):
    """Enhanced model for different types of sales and promotions"""

    SALE_TYPE_CHOICES = [
        ('percentage', 'Percentage Discount'),
        ('fixed_amount', 'Fixed Amount Off'),
        ('free_item', 'Free Item with Purchase'),
        ('buy_x_get_y', 'Buy X Get Y Free'),
        ('bundle', 'Bundle Deal'),
        ('clearance', 'Clearance Sale'),
        ('flash_sale', 'Flash Sale'),
        ('seasonal', 'Seasonal Sale'),
        ('gift_with_purchase', 'Gift with Purchase'),
        ('free_shipping', 'Free Shipping'),
        ('loyalty_discount', 'Loyalty Member Discount'),
        ('student_discount', 'Student Discount'),
        ('bulk_discount', 'Bulk Purchase Discount'),
        ('referral_bonus', 'Referral Bonus'),
        ('first_time_buyer', 'First Time Buyer Discount'),
    ]

    BADGE_COLOR_CHOICES = [
        ('#dc3545', 'Red - Urgent/Hot Deals'),
        ('#fd7e14', 'Orange - Special Offers'),
        ('#ffc107', 'Yellow - Attention'),
        ('#28a745', 'Green - Free Items/Eco'),
        ('#007bff', 'Blue - Trust/Premium'),
        ('#6f42c1', 'Purple - Exclusive/VIP'),
        ('#e83e8c', 'Pink - Flash/Limited'),
        ('#17a2b8', 'Teal - Fresh/New'),
        ('#6c757d', 'Gray - Clearance'),
        ('#343a40', 'Dark - Premium/Luxury'),
        ('#20c997', 'Mint - Fresh Deals'),
        ('#fd79a8', 'Rose - Valentine/Special'),
    ]

    PRIORITY_CHOICES = [
        (1, 'Low Priority'),
        (2, 'Normal Priority'),
        (3, 'High Priority'),
        (4, 'Urgent Priority'),
        (5, 'Critical Priority'),
    ]

    # Basic Information
    name = models.CharField(max_length=100, help_text="Sale category name (e.g., '10% Off', 'Free Headphones')")
    slug = models.SlugField(max_length=100, unique=True, blank=True)
    sale_type = models.CharField(max_length=20, choices=SALE_TYPE_CHOICES, default='percentage')
    description = models.TextField(blank=True, help_text="Detailed description of the sale")

    # Sale Configuration
    discount_percentage = models.DecimalField(
        max_digits=5, decimal_places=2, null=True, blank=True,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Discount percentage (0-100)"
    )
    discount_amount = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True,
        help_text="Fixed discount amount"
    )
    minimum_purchase_amount = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True,
        help_text="Minimum purchase amount to qualify for this sale"
    )
    maximum_discount_amount = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True,
        help_text="Maximum discount amount (for percentage discounts)"
    )

    # Free Item Configuration
    free_item_description = models.CharField(
        max_length=200, blank=True,
        help_text="Description of free item (e.g., 'Free wireless mouse')"
    )
    free_item_value = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True,
        help_text="Value of the free item"
    )

    # Buy X Get Y Configuration
    buy_quantity = models.PositiveIntegerField(
        null=True, blank=True,
        help_text="Buy X quantity (for Buy X Get Y deals)"
    )
    get_quantity = models.PositiveIntegerField(
        null=True, blank=True,
        help_text="Get Y quantity free (for Buy X Get Y deals)"
    )

    # Display Settings
    badge_text = models.CharField(max_length=50, help_text="Text to show on product badge")
    badge_color = models.CharField(max_length=7, choices=BADGE_COLOR_CHOICES, default='#dc3545')
    priority = models.IntegerField(choices=PRIORITY_CHOICES, default=2, help_text="Display priority (higher numbers show first)")

    # Timing and Availability
    start_date = models.DateTimeField(default=timezone.now)
    end_date = models.DateTimeField(null=True, blank=True, help_text="Leave blank for ongoing sale")

    # Usage Limits
    max_uses_per_customer = models.PositiveIntegerField(
        null=True, blank=True,
        help_text="Maximum times a customer can use this sale (leave blank for unlimited)"
    )
    max_total_uses = models.PositiveIntegerField(
        null=True, blank=True,
        help_text="Maximum total uses across all customers (leave blank for unlimited)"
    )
    current_uses = models.PositiveIntegerField(default=0, help_text="Current number of uses")

    # Status and Features
    is_active = models.BooleanField(default=True)
    is_featured = models.BooleanField(default=False, help_text="Show prominently on homepage")
    is_stackable = models.BooleanField(default=False, help_text="Can be combined with other sales")
    requires_coupon_code = models.BooleanField(default=False, help_text="Requires a coupon code to activate")
    coupon_code = models.CharField(max_length=50, blank=True, help_text="Coupon code (if required)")

    # Target Audience
    is_member_only = models.BooleanField(default=False, help_text="Only for registered members")
    is_first_time_buyer_only = models.BooleanField(default=False, help_text="Only for first-time buyers")

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        'auth.User', on_delete=models.SET_NULL, null=True, blank=True,
        related_name='created_sales'
    )

    class Meta:
        ordering = ['-priority', '-is_featured', '-start_date']
        verbose_name = 'Sale Category'
        verbose_name_plural = 'Sale Categories'

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    def is_currently_active(self):
        """Check if sale is currently active"""
        if not self.is_active:
            return False

        now = timezone.now()
        if self.end_date:
            return self.start_date <= now <= self.end_date
        return self.start_date <= now

    def is_usage_limit_reached(self):
        """Check if usage limit has been reached"""
        if self.max_total_uses:
            return self.current_uses >= self.max_total_uses
        return False

    def can_be_used(self):
        """Check if sale can currently be used"""
        return (
            self.is_currently_active() and
            not self.is_usage_limit_reached()
        )

    def get_display_text(self):
        """Get formatted display text for the sale"""
        if self.sale_type == 'percentage' and self.discount_percentage:
            return f"{int(self.discount_percentage)}% OFF"
        elif self.sale_type == 'fixed_amount' and self.discount_amount:
            return f"SAVE ${self.discount_amount}"
        elif self.sale_type == 'free_item' and self.free_item_description:
            return f"FREE {self.free_item_description.upper()}"
        elif self.sale_type == 'buy_x_get_y' and self.buy_quantity and self.get_quantity:
            return f"BUY {self.buy_quantity} GET {self.get_quantity} FREE"
        else:
            return self.badge_text

    def get_savings_amount(self, product_price):
        """Calculate savings amount for a given product price"""
        if self.sale_type == 'percentage' and self.discount_percentage:
            savings = (product_price * self.discount_percentage) / 100
            if self.maximum_discount_amount:
                savings = min(savings, self.maximum_discount_amount)
            return savings
        elif self.sale_type == 'fixed_amount' and self.discount_amount:
            return min(self.discount_amount, product_price)
        return Decimal('0.00')

    def get_final_price(self, product_price):
        """Calculate final price after applying this sale"""
        savings = self.get_savings_amount(product_price)
        return max(product_price - savings, Decimal('0.00'))


class ProductSaleAssignment(models.Model):
    """Track which products are assigned to which sales"""

    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='sale_assignments')
    sale_category = models.ForeignKey(SaleCategory, on_delete=models.CASCADE, related_name='product_assignments')
    assigned_date = models.DateTimeField(auto_now_add=True)
    assigned_by = models.ForeignKey(
        'auth.User', on_delete=models.SET_NULL, null=True, blank=True,
        related_name='product_sale_assignments'
    )
    is_active = models.BooleanField(default=True)

    # Override settings for this specific product
    custom_discount_percentage = models.DecimalField(
        max_digits=5, decimal_places=2, null=True, blank=True,
        help_text="Override discount percentage for this product"
    )
    custom_discount_amount = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True,
        help_text="Override discount amount for this product"
    )

    class Meta:
        unique_together = ['product', 'sale_category']
        ordering = ['-assigned_date']
        verbose_name = 'Product Sale Assignment'
        verbose_name_plural = 'Product Sale Assignments'

    def __str__(self):
        return f"{self.product.name} - {self.sale_category.name}"

    def get_effective_discount_percentage(self):
        """Get the effective discount percentage (custom or default)"""
        return self.custom_discount_percentage or self.sale_category.discount_percentage

    def get_effective_discount_amount(self):
        """Get the effective discount amount (custom or default)"""
        return self.custom_discount_amount or self.sale_category.discount_amount


class SaleUsageLog(models.Model):
    """Track usage of sales for analytics and limits"""

    sale_category = models.ForeignKey(SaleCategory, on_delete=models.CASCADE, related_name='usage_logs')
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='sale_usage_logs', null=True, blank=True)
    user = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, blank=True)

    # Usage details
    original_price = models.DecimalField(max_digits=10, decimal_places=2)
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2)
    final_price = models.DecimalField(max_digits=10, decimal_places=2)

    # Session and tracking
    session_key = models.CharField(max_length=40, blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)

    # Metadata
    used_at = models.DateTimeField(auto_now_add=True)
    order_id = models.CharField(max_length=100, blank=True, help_text="Associated order ID if applicable")

    class Meta:
        ordering = ['-used_at']
        verbose_name = 'Sale Usage Log'
        verbose_name_plural = 'Sale Usage Logs'

    def __str__(self):
        return f"{self.sale_category.name} - {self.used_at.strftime('%Y-%m-%d %H:%M')}"


class SalePerformanceMetrics(models.Model):
    """Daily/weekly/monthly performance metrics for sales"""

    PERIOD_CHOICES = [
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
    ]

    sale_category = models.ForeignKey(SaleCategory, on_delete=models.CASCADE, related_name='performance_metrics')
    period_type = models.CharField(max_length=10, choices=PERIOD_CHOICES)
    period_start = models.DateField()
    period_end = models.DateField()

    # Metrics
    total_uses = models.PositiveIntegerField(default=0)
    total_savings_given = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_revenue_impact = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    unique_users = models.PositiveIntegerField(default=0)
    unique_products = models.PositiveIntegerField(default=0)

    # Conversion metrics
    views = models.PositiveIntegerField(default=0, help_text="How many times products with this sale were viewed")
    conversions = models.PositiveIntegerField(default=0, help_text="How many times the sale was actually used")
    conversion_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0, help_text="Conversion rate percentage")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['sale_category', 'period_type', 'period_start']
        ordering = ['-period_start']
        verbose_name = 'Sale Performance Metric'
        verbose_name_plural = 'Sale Performance Metrics'

    def __str__(self):
        return f"{self.sale_category.name} - {self.period_type} ({self.period_start})"


class SaleCampaign(models.Model):
    """Group multiple sales into campaigns for better organization"""

    CAMPAIGN_STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('scheduled', 'Scheduled'),
        ('active', 'Active'),
        ('paused', 'Paused'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]

    name = models.CharField(max_length=100)
    slug = models.SlugField(max_length=100, unique=True, blank=True)
    description = models.TextField(blank=True)

    # Campaign timing
    start_date = models.DateTimeField()
    end_date = models.DateTimeField(null=True, blank=True)

    # Campaign settings
    status = models.CharField(max_length=20, choices=CAMPAIGN_STATUS_CHOICES, default='draft')
    is_featured = models.BooleanField(default=False)

    # Target and goals
    target_revenue = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    target_conversions = models.PositiveIntegerField(null=True, blank=True)
    budget = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)

    # Relationships
    sale_categories = models.ManyToManyField(SaleCategory, related_name='campaigns', blank=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        'auth.User', on_delete=models.SET_NULL, null=True, blank=True,
        related_name='created_campaigns'
    )

    class Meta:
        ordering = ['-start_date']
        verbose_name = 'Sale Campaign'
        verbose_name_plural = 'Sale Campaigns'

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    def is_active(self):
        """Check if campaign is currently active"""
        if self.status != 'active':
            return False

        now = timezone.now()
        if self.end_date:
            return self.start_date <= now <= self.end_date
        return self.start_date <= now

    def get_total_sales_count(self):
        """Get total number of sales in this campaign"""
        return self.sale_categories.count()

    def get_active_sales_count(self):
        """Get number of active sales in this campaign"""
        return self.sale_categories.filter(is_active=True).count()


class SaleNotification(models.Model):
    """Notifications for sale events and milestones"""

    NOTIFICATION_TYPE_CHOICES = [
        ('sale_started', 'Sale Started'),
        ('sale_ending_soon', 'Sale Ending Soon'),
        ('sale_ended', 'Sale Ended'),
        ('usage_limit_reached', 'Usage Limit Reached'),
        ('low_performance', 'Low Performance Alert'),
        ('high_performance', 'High Performance Alert'),
        ('budget_exceeded', 'Budget Exceeded'),
        ('target_reached', 'Target Reached'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPE_CHOICES)
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='medium')

    # Related objects
    sale_category = models.ForeignKey(SaleCategory, on_delete=models.CASCADE, null=True, blank=True)
    campaign = models.ForeignKey(SaleCampaign, on_delete=models.CASCADE, null=True, blank=True)

    # Notification content
    title = models.CharField(max_length=200)
    message = models.TextField()

    # Recipients
    recipient = models.ForeignKey('auth.User', on_delete=models.CASCADE, null=True, blank=True)
    send_to_all_admins = models.BooleanField(default=False)

    # Status
    is_read = models.BooleanField(default=False)
    is_sent = models.BooleanField(default=False)
    sent_at = models.DateTimeField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Sale Notification'
        verbose_name_plural = 'Sale Notifications'

    def __str__(self):
        return f"{self.get_notification_type_display()} - {self.title}"
