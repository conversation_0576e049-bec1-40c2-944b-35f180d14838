{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>Products - Admin Dashboard - iTechStore</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        /* Admin Dashboard Styles */
        .admin-container {
            display: flex;
            min-height: calc(100vh - 150px);
        }

        .admin-sidebar {
            width: 250px;
            background-color: #1a1a1a;
            color: white;
            padding: 20px 0;
            flex-shrink: 0;
        }

        .admin-logo {
            padding: 0 20px 20px;
            border-bottom: 1px solid #333;
            margin-bottom: 20px;
        }

        .admin-logo h2 {
            font-size: 20px;
            display: flex;
            align-items: center;
        }

        .admin-logo i {
            margin-right: 10px;
            color: #4dabf7;
        }

        .admin-menu {
            list-style: none;
        }

        .admin-menu li {
            margin-bottom: 5px;
        }

        .admin-menu a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: #aaa;
            transition: all 0.3s;
        }

        .admin-menu a:hover, .admin-menu a.active {
            background-color: #333;
            color: white;
        }

        .admin-menu i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .admin-content {
            flex: 1;
            padding: 30px;
            background-color: #f5f7fa;
            overflow-y: auto;
        }

        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .admin-title h1 {
            font-size: 24px;
            color: #333;
        }

        .admin-user {
            display: flex;
            align-items: center;
        }

        .admin-user-info {
            margin-right: 15px;
            text-align: right;
        }

        .admin-user-name {
            font-weight: 500;
            color: #333;
        }

        .admin-user-role {
            font-size: 12px;
            color: #777;
        }

        .admin-user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #4dabf7;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
        }

        .admin-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 30px;
        }

        .admin-card-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-card-title {
            font-size: 18px;
            color: #333;
            font-weight: 500;
        }

        .admin-card-actions a {
            padding: 8px 15px;
            background-color: #4dabf7;
            color: white;
            border-radius: 5px;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .admin-card-actions a:hover {
            background-color: #0c8599;
        }

        .admin-card-body {
            padding: 20px;
        }

        .admin-table {
            width: 100%;
            border-collapse: collapse;
        }

        .admin-table th {
            text-align: left;
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            color: #333;
            font-weight: 500;
        }

        .admin-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            color: #555;
        }

        .admin-table tr:last-child td {
            border-bottom: none;
        }

        .admin-table tr:hover td {
            background-color: #f9f9f9;
        }

        .status-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-available {
            background-color: #e8f5e9;
            color: #4caf50;
        }

        .status-preorder {
            background-color: #fff3e0;
            color: #ff9800;
        }

        .status-unavailable {
            background-color: #ffebee;
            color: #f44336;
        }

        .action-buttons {
            display: flex;
            gap: 5px;
        }

        .action-btn {
            width: 30px;
            height: 30px;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            transition: all 0.3s;
        }

        .edit-btn {
            background-color: #e3f2fd;
            color: #2196f3;
        }

        .edit-btn:hover {
            background-color: #2196f3;
            color: white;
        }

        .delete-btn {
            background-color: #ffebee;
            color: #f44336;
        }

        .delete-btn:hover {
            background-color: #f44336;
            color: white;
        }

        .view-btn {
            background-color: #e8f5e9;
            color: #4caf50;
        }

        .view-btn:hover {
            background-color: #4caf50;
            color: white;
        }

        .search-filter-bar {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .search-box {
            display: flex;
            align-items: center;
            background-color: white;
            border-radius: 5px;
            padding: 0 15px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            width: 300px;
        }

        .search-box input {
            border: none;
            padding: 10px 0;
            flex: 1;
            outline: none;
        }

        .search-box i {
            color: #777;
        }

        .filter-options {
            display: flex;
            gap: 10px;
        }

        .filter-select {
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            background-color: white;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            outline: none;
        }

        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }

        .pagination a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 35px;
            height: 35px;
            margin: 0 5px;
            border-radius: 5px;
            background-color: white;
            color: #333;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
        }

        .pagination a:hover, .pagination a.active {
            background-color: #4dabf7;
            color: white;
        }

        .product-image {
            width: 50px;
            height: 50px;
            border-radius: 5px;
            overflow: hidden;
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .product-info {
            display: flex;
            align-items: center;
        }

        .product-details {
            margin-left: 15px;
        }

        .product-name {
            font-weight: 500;
            margin-bottom: 3px;
        }

        .product-id {
            font-size: 12px;
            color: #777;
        }

        @media (max-width: 992px) {
            .admin-container {
                flex-direction: column;
            }

            .admin-sidebar {
                width: 100%;
                padding: 10px 0;
            }

            .admin-logo {
                padding: 0 15px 15px;
                margin-bottom: 10px;
            }

            .admin-menu a {
                padding: 10px 15px;
            }

            .search-filter-bar {
                flex-direction: column;
                gap: 10px;
            }

            .search-box {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- Top Navigation Bar -->
    <div class="top-nav">
        <div class="container">
            <div class="top-nav-left">
                <span>Admin Panel</span>
            </div>
            <div class="top-nav-right">
                <span>Welcome, {{ request.user.username }}</span>
                <a href="{% url 'admin_logout' %}" class="sign-in">Logout</a>
            </div>
        </div>
    </div>

    <!-- Admin Container -->
    <div class="admin-container">
        <!-- Admin Sidebar -->
        <div class="admin-sidebar">
            <div class="admin-logo">
                <h2><i class="fas fa-tachometer-alt"></i> Admin Panel</h2>
            </div>
            <ul class="admin-menu">
                <li><a href="{% url 'admin_dashboard' %}"><i class="fas fa-home"></i> Dashboard</a></li>
                <li><a href="{% url 'admin_products' %}" class="active"><i class="fas fa-box"></i> Products</a></li>
                <li><a href="{% url 'admin_users' %}"><i class="fas fa-users"></i> Users</a></li>
                <li><a href="{% url 'admin_orders' %}"><i class="fas fa-shopping-cart"></i> Orders</a></li>
                <li><a href="{% url 'admin_categories' %}"><i class="fas fa-tags"></i> Categories</a></li>
                <li><a href="{% url 'admin_settings' %}"><i class="fas fa-cog"></i> Settings</a></li>
                <li><a href="/django-admin/" target="_blank"><i class="fas fa-tools"></i> Django Admin</a></li>
                <li><a href="{% url 'home' %}"><i class="fas fa-store"></i> View Store</a></li>
                <li><a href="{% url 'admin_logout' %}"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
            </ul>
        </div>

        <!-- Admin Content -->
        <div class="admin-content">
            <div class="admin-header">
                <div class="admin-title">
                    <h1>Products</h1>
                </div>
                <div class="admin-user">
                    <div class="admin-user-info">
                        <div class="admin-user-name">{{ request.user.username }}</div>
                        <div class="admin-user-role">Administrator</div>
                    </div>
                    <div class="admin-user-avatar">
                        {{ request.user.username|first|upper }}
                    </div>
                </div>
            </div>

            <!-- Search and Filter Bar -->
            <div class="search-filter-bar">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Search products...">
                </div>
                <div class="filter-options">
                    <select class="filter-select">
                        <option value="">All Categories</option>
                        <option value="laptops">Laptops</option>
                        <option value="smartphones">Smartphones</option>
                        <option value="tablets">Tablets</option>
                        <option value="accessories">Accessories</option>
                    </select>
                    <select class="filter-select">
                        <option value="">All Status</option>
                        <option value="available">Available</option>
                        <option value="preorder">Pre-order</option>
                        <option value="unavailable">Unavailable</option>
                    </select>
                </div>
            </div>

            <!-- Products Table -->
            <div class="admin-card">
                <div class="admin-card-header">
                    <div class="admin-card-title">All Products</div>
                    <div class="admin-card-actions">
                        <a href="{% url 'admin_product_add' %}"><i class="fas fa-plus"></i> Add Product</a>
                    </div>
                </div>
                <div class="admin-card-body">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>Product</th>
                                <th>Price</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in products %}
                            <tr>
                                <td>
                                    <div class="product-info">
                                        <div class="product-image">
                                            {% if product.image %}
                                                <img src="{{ product.image.url }}" alt="{{ product.name }}">
                                            {% else %}
                                                <img src="https://via.placeholder.com/50x50?text=No+Image" alt="{{ product.name }}">
                                            {% endif %}
                                        </div>
                                        <div class="product-details">
                                            <div class="product-name">{{ product.name }}</div>
                                            <div class="product-id">ID: {{ product.id }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>${{ product.price }}</td>
                                <td>
                                    {% if product.stock_status == 'available' %}
                                        <span class="status-badge status-available">Available</span>
                                    {% elif product.stock_status == 'preorder' %}
                                        <span class="status-badge status-preorder">Pre-order</span>
                                    {% else %}
                                        <span class="status-badge status-unavailable">Unavailable</span>
                                    {% endif %}
                                </td>
                                <td>{{ product.created_at|date:"M d, Y" }}</td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="{% url 'admin_product_edit' product.id %}" class="action-btn edit-btn"><i class="fas fa-edit"></i></a>
                                        <a href="{% url 'admin_product_delete' product.id %}" class="action-btn delete-btn"><i class="fas fa-trash"></i></a>
                                        <a href="{% url 'admin_product_view' product.id %}" class="action-btn view-btn"><i class="fas fa-eye"></i></a>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" style="text-align: center;">No products found</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Pagination -->
            {% if products.has_other_pages %}
            <div class="pagination">
                {% if products.has_previous %}
                    <a href="?page={{ products.previous_page_number }}"><i class="fas fa-chevron-left"></i></a>
                {% endif %}

                {% for i in products.paginator.page_range %}
                    {% if products.number == i %}
                        <a href="#" class="active">{{ i }}</a>
                    {% else %}
                        <a href="?page={{ i }}">{{ i }}</a>
                    {% endif %}
                {% endfor %}

                {% if products.has_next %}
                    <a href="?page={{ products.next_page_number }}"><i class="fas fa-chevron-right"></i></a>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>

    <script src="{% static 'js/script.js' %}"></script>
</body>
</html>
