#!/usr/bin/env python
"""
Management command for managing sale categories and products
"""
from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from authtryapp.models import Product, SaleCategory
from decimal import Decimal


class Command(BaseCommand):
    help = 'Manage sale categories and products'

    def add_arguments(self, parser):
        subparsers = parser.add_subparsers(dest='action', help='Available actions')
        
        # Create sale category
        create_parser = subparsers.add_parser('create-sale', help='Create a new sale category')
        create_parser.add_argument('--name', required=True, help='Sale category name')
        create_parser.add_argument('--type', required=True, choices=[
            'percentage', 'fixed_amount', 'free_item', 'buy_x_get_y', 
            'bundle', 'clearance', 'flash_sale', 'seasonal', 
            'gift_with_purchase', 'free_shipping'
        ], help='Sale type')
        create_parser.add_argument('--badge-text', required=True, help='Badge text to display')
        create_parser.add_argument('--badge-color', default='#dc3545', help='Badge color (hex)')
        create_parser.add_argument('--discount-percentage', type=float, help='Discount percentage (0-100)')
        create_parser.add_argument('--discount-amount', type=float, help='Fixed discount amount')
        create_parser.add_argument('--free-item', help='Free item description')
        create_parser.add_argument('--buy-quantity', type=int, help='Buy X quantity')
        create_parser.add_argument('--get-quantity', type=int, help='Get Y quantity free')
        create_parser.add_argument('--description', help='Sale description')
        create_parser.add_argument('--featured', action='store_true', help='Mark as featured')
        create_parser.add_argument('--end-date', help='End date (YYYY-MM-DD HH:MM:SS)')
        
        # Add products to sale
        add_parser = subparsers.add_parser('add-products', help='Add products to sale category')
        add_parser.add_argument('--sale-slug', required=True, help='Sale category slug')
        add_parser.add_argument('--product-ids', nargs='+', type=int, help='Product IDs to add')
        add_parser.add_argument('--product-names', nargs='+', help='Product names to add (partial match)')
        
        # Remove products from sale
        remove_parser = subparsers.add_parser('remove-products', help='Remove products from sale category')
        remove_parser.add_argument('--sale-slug', required=True, help='Sale category slug')
        remove_parser.add_argument('--product-ids', nargs='+', type=int, help='Product IDs to remove')
        
        # List sales
        list_parser = subparsers.add_parser('list-sales', help='List all sale categories')
        list_parser.add_argument('--active-only', action='store_true', help='Show only active sales')
        
        # List products in sale
        products_parser = subparsers.add_parser('list-products', help='List products in sale category')
        products_parser.add_argument('--sale-slug', required=True, help='Sale category slug')

    def handle(self, *args, **options):
        action = options['action']
        
        if action == 'create-sale':
            self.create_sale_category(options)
        elif action == 'add-products':
            self.add_products_to_sale(options)
        elif action == 'remove-products':
            self.remove_products_from_sale(options)
        elif action == 'list-sales':
            self.list_sale_categories(options)
        elif action == 'list-products':
            self.list_products_in_sale(options)
        else:
            self.stdout.write(self.style.ERROR('Please specify an action'))

    def create_sale_category(self, options):
        """Create a new sale category"""
        try:
            sale_data = {
                'name': options['name'],
                'sale_type': options['type'],
                'badge_text': options['badge_text'],
                'badge_color': options['badge_color'],
                'is_featured': options.get('featured', False),
            }
            
            # Add optional fields
            if options.get('discount_percentage'):
                sale_data['discount_percentage'] = Decimal(str(options['discount_percentage']))
            if options.get('discount_amount'):
                sale_data['discount_amount'] = Decimal(str(options['discount_amount']))
            if options.get('free_item'):
                sale_data['free_item_description'] = options['free_item']
            if options.get('buy_quantity'):
                sale_data['buy_quantity'] = options['buy_quantity']
            if options.get('get_quantity'):
                sale_data['get_quantity'] = options['get_quantity']
            if options.get('description'):
                sale_data['description'] = options['description']
            if options.get('end_date'):
                sale_data['end_date'] = timezone.datetime.strptime(
                    options['end_date'], '%Y-%m-%d %H:%M:%S'
                ).replace(tzinfo=timezone.get_current_timezone())
            
            sale = SaleCategory.objects.create(**sale_data)
            self.stdout.write(
                self.style.SUCCESS(f'✅ Created sale category: {sale.name} (slug: {sale.slug})')
            )
            
        except Exception as e:
            raise CommandError(f'Error creating sale category: {e}')

    def add_products_to_sale(self, options):
        """Add products to a sale category"""
        try:
            sale = SaleCategory.objects.get(slug=options['sale_slug'])
        except SaleCategory.DoesNotExist:
            raise CommandError(f'Sale category with slug "{options["sale_slug"]}" not found')
        
        products_added = 0
        
        # Add by product IDs
        if options.get('product_ids'):
            for product_id in options['product_ids']:
                try:
                    product = Product.objects.get(id=product_id)
                    sale.products.add(product)
                    products_added += 1
                    self.stdout.write(f'  ✅ Added: {product.name}')
                except Product.DoesNotExist:
                    self.stdout.write(self.style.WARNING(f'  ⚠️ Product ID {product_id} not found'))
        
        # Add by product names (partial match)
        if options.get('product_names'):
            for name_part in options['product_names']:
                products = Product.objects.filter(name__icontains=name_part)
                for product in products:
                    sale.products.add(product)
                    products_added += 1
                    self.stdout.write(f'  ✅ Added: {product.name}')
        
        self.stdout.write(
            self.style.SUCCESS(f'✅ Added {products_added} products to sale "{sale.name}"')
        )

    def remove_products_from_sale(self, options):
        """Remove products from a sale category"""
        try:
            sale = SaleCategory.objects.get(slug=options['sale_slug'])
        except SaleCategory.DoesNotExist:
            raise CommandError(f'Sale category with slug "{options["sale_slug"]}" not found')
        
        products_removed = 0
        
        if options.get('product_ids'):
            for product_id in options['product_ids']:
                try:
                    product = Product.objects.get(id=product_id)
                    sale.products.remove(product)
                    products_removed += 1
                    self.stdout.write(f'  ✅ Removed: {product.name}')
                except Product.DoesNotExist:
                    self.stdout.write(self.style.WARNING(f'  ⚠️ Product ID {product_id} not found'))
        
        self.stdout.write(
            self.style.SUCCESS(f'✅ Removed {products_removed} products from sale "{sale.name}"')
        )

    def list_sale_categories(self, options):
        """List all sale categories"""
        sales = SaleCategory.objects.all()
        
        if options.get('active_only'):
            sales = sales.filter(is_active=True)
        
        self.stdout.write(self.style.SUCCESS('📋 Sale Categories:'))
        self.stdout.write('-' * 80)
        
        for sale in sales:
            status = '🟢 Active' if sale.is_currently_active() else '🔴 Inactive'
            featured = '⭐ Featured' if sale.is_featured else ''
            product_count = sale.products.count()
            
            self.stdout.write(f'{sale.name} ({sale.slug})')
            self.stdout.write(f'  Type: {sale.get_sale_type_display()}')
            self.stdout.write(f'  Badge: {sale.badge_text}')
            self.stdout.write(f'  Status: {status} {featured}')
            self.stdout.write(f'  Products: {product_count}')
            self.stdout.write(f'  Start: {sale.start_date.strftime("%Y-%m-%d %H:%M")}')
            if sale.end_date:
                self.stdout.write(f'  End: {sale.end_date.strftime("%Y-%m-%d %H:%M")}')
            self.stdout.write('')

    def list_products_in_sale(self, options):
        """List products in a sale category"""
        try:
            sale = SaleCategory.objects.get(slug=options['sale_slug'])
        except SaleCategory.DoesNotExist:
            raise CommandError(f'Sale category with slug "{options["sale_slug"]}" not found')
        
        products = sale.products.all()
        
        self.stdout.write(self.style.SUCCESS(f'📦 Products in "{sale.name}":'))
        self.stdout.write('-' * 60)
        
        for product in products:
            self.stdout.write(f'ID: {product.id} | {product.name} | ${product.price}')
        
        self.stdout.write(f'\nTotal: {products.count()} products')
