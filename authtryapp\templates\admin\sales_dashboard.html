{% extends 'admin/base_admin.html' %}
{% load static %}

{% block title %}Sales Management Dashboard{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'admin/css/sales_management_admin.css' %}">
<style>
.sales-dashboard {
    padding: 20px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    text-align: center;
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 8px;
    display: block;
}

.stat-label {
    font-size: 14px;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.dashboard-section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.section-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}

.sales-list {
    display: grid;
    gap: 15px;
}

.sale-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
    transition: all 0.2s ease;
}

.sale-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.sale-info h4 {
    margin: 0 0 5px 0;
    color: #333;
    font-size: 16px;
}

.sale-info p {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
}

.sale-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    color: white;
    margin-right: 10px;
}

.sale-stats {
    text-align: right;
    color: #495057;
    font-size: 14px;
}

.quick-actions {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
}

.quick-action-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.quick-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    color: white;
    text-decoration: none;
}

.quick-action-btn.secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

.quick-action-btn.secondary:hover {
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.campaigns-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.campaign-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #dee2e6;
    border-radius: 10px;
    padding: 20px;
    transition: all 0.2s ease;
}

.campaign-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
}

.campaign-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
}

.campaign-status {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
    margin-bottom: 10px;
}

.campaign-status.active {
    background: #d4edda;
    color: #155724;
}

.campaign-status.scheduled {
    background: #d1ecf1;
    color: #0c5460;
}

.campaign-status.draft {
    background: #f8d7da;
    color: #721c24;
}
</style>
{% endblock %}

{% block content %}
<div class="sales-dashboard">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>🏷️ Sales Management Dashboard</h1>
        <div class="quick-actions">
            <a href="{% url 'admin_sale_category_add' %}" class="quick-action-btn">
                <i class="fas fa-plus"></i>
                Create New Sale
            </a>
            <a href="{% url 'admin_sales_categories' %}" class="quick-action-btn secondary">
                <i class="fas fa-list"></i>
                View All Sales
            </a>
        </div>
    </div>

    <!-- Statistics Grid -->
    <div class="stats-grid">
        <div class="stat-card">
            <span class="stat-number">{{ total_sales }}</span>
            <span class="stat-label">Total Sales</span>
        </div>
        <div class="stat-card">
            <span class="stat-number">{{ active_sales }}</span>
            <span class="stat-label">Active Sales</span>
        </div>
        <div class="stat-card">
            <span class="stat-number">{{ featured_sales }}</span>
            <span class="stat-label">Featured Sales</span>
        </div>
        <div class="stat-card">
            <span class="stat-number">{{ products_in_sales }}</span>
            <span class="stat-label">Products in Sales</span>
        </div>
        <div class="stat-card">
            <span class="stat-number">{{ total_campaigns }}</span>
            <span class="stat-label">Total Campaigns</span>
        </div>
        <div class="stat-card">
            <span class="stat-number">{{ active_campaigns }}</span>
            <span class="stat-label">Active Campaigns</span>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <!-- Recent Sales -->
            <div class="dashboard-section">
                <h3 class="section-title">Recent Sales</h3>
                <div class="sales-list">
                    {% for sale in recent_sales %}
                    <div class="sale-item">
                        <div class="sale-info">
                            <h4>
                                <span class="sale-badge" style="background-color: {{ sale.badge_color }};">
                                    {{ sale.badge_text }}
                                </span>
                                {{ sale.name }}
                            </h4>
                            <p>{{ sale.get_sale_type_display }} • Priority: {{ sale.priority }}/5</p>
                        </div>
                        <div class="sale-stats">
                            <div>
                                {% if sale.is_active %}
                                    <span class="badge badge-success">Active</span>
                                {% else %}
                                    <span class="badge badge-secondary">Inactive</span>
                                {% endif %}
                                {% if sale.is_featured %}
                                    <span class="badge badge-warning">Featured</span>
                                {% endif %}
                            </div>
                            <div class="mt-1">
                                <small>Uses: {{ sale.current_uses }}</small>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-muted">No sales created yet.</p>
                    {% endfor %}
                </div>
            </div>

            <!-- Top Performing Sales -->
            <div class="dashboard-section">
                <h3 class="section-title">Top Performing Sales</h3>
                <div class="sales-list">
                    {% for sale in top_sales %}
                    <div class="sale-item">
                        <div class="sale-info">
                            <h4>
                                <span class="sale-badge" style="background-color: {{ sale.badge_color }};">
                                    {{ sale.badge_text }}
                                </span>
                                {{ sale.name }}
                            </h4>
                            <p>{{ sale.get_sale_type_display }}</p>
                        </div>
                        <div class="sale-stats">
                            <div><strong>{{ sale.current_uses }}</strong> uses</div>
                            <div class="mt-1">
                                <small>
                                    {% if sale.discount_percentage %}
                                        {{ sale.discount_percentage }}% off
                                    {% elif sale.discount_amount %}
                                        ${{ sale.discount_amount }} off
                                    {% endif %}
                                </small>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-muted">No sales data available yet.</p>
                    {% endfor %}
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Recent Campaigns -->
            <div class="dashboard-section">
                <h3 class="section-title">Recent Campaigns</h3>
                <div class="campaigns-grid">
                    {% for campaign in recent_campaigns %}
                    <div class="campaign-card">
                        <div class="campaign-title">{{ campaign.name }}</div>
                        <div class="campaign-status {{ campaign.status }}">{{ campaign.get_status_display }}</div>
                        <p class="text-muted">{{ campaign.description|truncatewords:15 }}</p>
                        {% if campaign.target_revenue %}
                        <div class="mt-2">
                            <small><strong>Target:</strong> ${{ campaign.target_revenue }}</small>
                        </div>
                        {% endif %}
                    </div>
                    {% empty %}
                    <p class="text-muted">No campaigns created yet.</p>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add some interactive features
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // Auto-refresh stats every 30 seconds
    setTimeout(() => {
        location.reload();
    }, 30000);
});
</script>
{% endblock %}
