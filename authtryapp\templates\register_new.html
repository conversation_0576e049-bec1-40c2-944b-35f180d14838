{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>Register - iTechStore</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        /* Additional styles for register page */
        .register-container {
            padding: 60px 0;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .register-form {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 600px;
            margin: 40px 0;
        }
        
        .register-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .register-header h1 {
            font-size: 28px;
            margin-bottom: 10px;
            color: #333;
        }
        
        .register-header p {
            color: #777;
            font-size: 16px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            border-color: #4dabf7;
            outline: none;
        }
        
        .help-text {
            font-size: 12px;
            color: #777;
            margin-top: 5px;
        }
        
        .error-text {
            font-size: 12px;
            color: #f44336;
            margin-top: 5px;
        }
        
        .register-button {
            width: 100%;
            padding: 14px;
            background: linear-gradient(to right, #4dabf7, #0c8599);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: opacity 0.3s;
        }
        
        .register-button:hover {
            opacity: 0.9;
        }
        
        .register-footer {
            text-align: center;
            margin-top: 25px;
            color: #777;
        }
        
        .register-footer a {
            color: #4dabf7;
            font-weight: 500;
            text-decoration: none;
        }
        
        .register-footer a:hover {
            text-decoration: underline;
        }
        
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            color: white;
        }
        
        .alert-danger {
            background-color: #f44336;
        }
        
        .alert-success {
            background-color: #4caf50;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
        }
        
        .form-col {
            flex: 1;
        }
        
        .social-register {
            margin-top: 30px;
            text-align: center;
        }
        
        .social-register p {
            color: #777;
            margin-bottom: 15px;
            position: relative;
        }
        
        .social-register p::before,
        .social-register p::after {
            content: "";
            position: absolute;
            top: 50%;
            width: 35%;
            height: 1px;
            background-color: #ddd;
        }
        
        .social-register p::before {
            left: 0;
        }
        
        .social-register p::after {
            right: 0;
        }
        
        .social-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
        }
        
        .social-button {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            transition: transform 0.3s;
        }
        
        .social-button:hover {
            transform: translateY(-3px);
        }
        
        .facebook {
            background-color: #3b5998;
        }
        
        .google {
            background-color: #db4437;
        }
        
        .twitter {
            background-color: #1da1f2;
        }
    </style>
</head>
<body>
    <!-- Top Navigation Bar -->
    <div class="top-nav">
        <div class="container">
            <div class="top-nav-left">
                <span>NPR</span>
                <i class="fas fa-chevron-down"></i>
            </div>
            <div class="top-nav-right">
                <a href="{% url 'register' %}" class="create-account active">Create an account</a>
                <a href="{% url 'login' %}" class="sign-in">Sign in</a>
            </div>
        </div>
    </div>

    <!-- Main Navigation -->
    <nav class="main-nav">
        <div class="container">
            <div class="logo">
                <a href="{% url 'home' %}">
                    <h1><span class="i-text">i</span><span class="tech-text">Tech</span><span class="store-text">Store</span></h1>
                    <p class="tagline">Your Technology Destination</p>
                </a>
            </div>
            <div class="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </div>
            <div class="nav-links">
                <ul>
                    <li><a href="{% url 'home' %}">Home</a></li>
                    <li class="dropdown">
                        <a href="#">Shop <i class="fas fa-chevron-down"></i></a>
                        <div class="dropdown-content">
                            <a href="#">Laptops</a>
                            <a href="#">Smartphones</a>
                            <a href="#">Tablets</a>
                            <a href="#">Accessories</a>
                        </div>
                    </li>
                    <li><a href="#">Brands</a></li>
                    <li class="dropdown">
                        <a href="#">Blogs <i class="fas fa-chevron-down"></i></a>
                        <div class="dropdown-content">
                            <a href="#">Tech News</a>
                            <a href="#">Reviews</a>
                            <a href="#">Tutorials</a>
                        </div>
                    </li>
                    <li><a href="#">PC BLD</a></li>
                </ul>
            </div>
            <div class="nav-icons">
                <a href="#" class="search-icon"><i class="fas fa-search"></i></a>
                <a href="{% url 'cart_summary' %}" class="cart-icon">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count">{{ cart.total_items|default:"0" }}</span>
                </a>
            </div>
        </div>
    </nav>

    <!-- Register Section -->
    <section class="register-container">
        <div class="register-form">
            <div class="register-header">
                <h1>Create an Account</h1>
                <p>Join iTechStore for exclusive deals and faster checkout</p>
            </div>
            
            {% if messages %}
                {% for message in messages %}
                    <div class="alert {% if message.tags == 'error' %}alert-danger{% else %}alert-success{% endif %}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
            
            <form method="post" action="{% url 'register' %}">
                {% csrf_token %}
                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" id="username" name="username" placeholder="Choose a username" required>
                    {% if form.username.errors %}
                        <div class="error-text">{{ form.username.errors.0 }}</div>
                    {% endif %}
                    <div class="help-text">{{ form.username.help_text }}</div>
                </div>
                
                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label for="first_name">First Name</label>
                            <input type="text" id="first_name" name="first_name" placeholder="Your first name" required>
                            {% if form.first_name.errors %}
                                <div class="error-text">{{ form.first_name.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label for="last_name">Last Name</label>
                            <input type="text" id="last_name" name="last_name" placeholder="Your last name" required>
                            {% if form.last_name.errors %}
                                <div class="error-text">{{ form.last_name.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" placeholder="Your email address" required>
                    {% if form.email.errors %}
                        <div class="error-text">{{ form.email.errors.0 }}</div>
                    {% endif %}
                </div>
                
                <div class="form-group">
                    <label for="password1">Password</label>
                    <input type="password" id="password1" name="password1" placeholder="Create a password" required>
                    {% if form.password1.errors %}
                        <div class="error-text">{{ form.password1.errors.0 }}</div>
                    {% endif %}
                    <div class="help-text">{{ form.password1.help_text }}</div>
                </div>
                
                <div class="form-group">
                    <label for="password2">Confirm Password</label>
                    <input type="password" id="password2" name="password2" placeholder="Confirm your password" required>
                    {% if form.password2.errors %}
                        <div class="error-text">{{ form.password2.errors.0 }}</div>
                    {% endif %}
                </div>
                
                <button type="submit" class="register-button">Create Account</button>
                
                <div class="register-footer">
                    <p>Already have an account? <a href="{% url 'login' %}">Sign in</a></p>
                </div>
                
                <div class="social-register">
                    <p>Or register with</p>
                    <div class="social-buttons">
                        <a href="#" class="social-button facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-button google"><i class="fab fa-google"></i></a>
                        <a href="#" class="social-button twitter"><i class="fab fa-twitter"></i></a>
                    </div>
                </div>
            </form>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-columns">
                <div class="footer-column">
                    <h3>Shop</h3>
                    <ul>
                        <li><a href="#">Laptops</a></li>
                        <li><a href="#">Smartphones</a></li>
                        <li><a href="#">Tablets</a></li>
                        <li><a href="#">Accessories</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Support</h3>
                    <ul>
                        <li><a href="#">Contact Us</a></li>
                        <li><a href="#">FAQs</a></li>
                        <li><a href="#">Shipping</a></li>
                        <li><a href="#">Returns</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Company</h3>
                    <ul>
                        <li><a href="#">About Us</a></li>
                        <li><a href="#">Careers</a></li>
                        <li><a href="#">Blog</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Connect</h3>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                    <div class="newsletter">
                        <h4>Subscribe to our newsletter</h4>
                        <form>
                            <input type="email" placeholder="Your email address">
                            <button type="submit">Subscribe</button>
                        </form>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 iTechStore. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="{% static 'js/script.js' %}"></script>
</body>
</html>
