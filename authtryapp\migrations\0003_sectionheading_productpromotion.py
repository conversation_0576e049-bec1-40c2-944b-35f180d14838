# Generated by Django 5.0.6 on 2025-05-23 11:03

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('authtryapp', '0002_brand_product_brand'),
    ]

    operations = [
        migrations.CreateModel(
            name='SectionHeading',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('section_type', models.CharField(choices=[('featured_products', 'Featured Products'), ('best_selling', 'Best Selling Products'), ('new_arrivals', 'New Arrivals'), ('top_deals', 'Top Deals'), ('special_offers', 'Special Offers'), ('clearance', 'Clearance')], max_length=50, unique=True)),
                ('title', models.CharField(max_length=100)),
                ('subtitle', models.CharField(blank=True, max_length=200, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Section Heading',
                'verbose_name_plural': 'Section Headings',
            },
        ),
        migrations.CreateModel(
            name='ProductPromotion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sale_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('discount_percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('is_featured', models.BooleanField(default=False)),
                ('is_best_seller', models.BooleanField(default=False)),
                ('is_new_arrival', models.BooleanField(default=False)),
                ('is_top_deal', models.BooleanField(default=False)),
                ('start_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('end_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='promotions', to='authtryapp.product')),
            ],
        ),
    ]
