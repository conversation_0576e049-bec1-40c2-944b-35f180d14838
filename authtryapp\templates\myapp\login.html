{% extends "base.html" %}

{% block title %}Login{% endblock %}

{% block content %}
    <div class="login-container">
        <h3 class="text-center">Login</h3>

        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} text-center">{{ message }}</div>
            {% endfor %}
        {% endif %}

        {% if form.errors %}
            <div class="alert alert-danger text-center">
                Invalid username or password. Please try again.
            </div>
        {% endif %}

        <form method="post">
            {% csrf_token %}

            <div class="mb-3">
                <label for="{{ form.username.id_for_label }}" class="form-label">Username</label>
                {{ form.username }}
            </div>

            <div class="mb-3">
                <label for="{{ form.password.id_for_label }}" class="form-label">Password</label>
                {{ form.password }}
            </div>

            <button type="submit" class="btn btn-primary w-100">Login</button>
        </form>

        <div class="text-center mt-3">
            <a href="#">Forgot Password?</a>
        </div>
        <div class="text-center mt-2">
            <p>Don't have an account? <a href="{% url 'register' %}">Register here</a></p>
        </div>
    </div>
{% endblock %}
