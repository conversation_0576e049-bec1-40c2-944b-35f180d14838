{% extends 'admin/base_admin.html' %}

{% block title %}Edit Promotion{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="mt-4">Edit Promotion</h1>

    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'admin_dashboard' %}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{% url 'admin_promotions' %}">Promotions</a></li>
            <li class="breadcrumb-item active">Edit</li>
        </ol>
    </nav>

    {% if messages %}
    <div class="row">
        <div class="col-12">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-edit me-1"></i>
            Edit Promotion for {{ promotion.product.name }}
        </div>
        <div class="card-body">
            <form method="post" action="{% url 'admin_promotion_edit' promotion.id %}">
                {% csrf_token %}

                {% if form.non_field_errors %}
                <div class="alert alert-danger">
                    {% for error in form.non_field_errors %}
                    {{ error }}
                    {% endfor %}
                </div>
                {% endif %}

                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="{{ form.product.id_for_label }}" class="form-label">Product*</label>
                            {{ form.product }}
                            {% if form.product.errors %}
                            <div class="text-danger">
                                {% for error in form.product.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="{{ form.sale_price.id_for_label }}" class="form-label">Sale Price*</label>
                            {{ form.sale_price }}
                            {% if form.sale_price.errors %}
                            <div class="text-danger">
                                {% for error in form.sale_price.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="{{ form.discount_percentage.id_for_label }}" class="form-label">Discount Percentage</label>
                            {{ form.discount_percentage }}
                            <small class="form-text text-muted">Leave blank to calculate automatically</small>
                            {% if form.discount_percentage.errors %}
                            <div class="text-danger">
                                {% for error in form.discount_percentage.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.start_date.id_for_label }}" class="form-label">Start Date*</label>
                            {{ form.start_date }}
                            {% if form.start_date.errors %}
                            <div class="text-danger">
                                {% for error in form.start_date.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.end_date.id_for_label }}" class="form-label">End Date</label>
                            {{ form.end_date }}
                            <small class="form-text text-muted">Leave blank for ongoing promotion</small>
                            {% if form.end_date.errors %}
                            <div class="text-danger">
                                {% for error in form.end_date.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-12">
                        <label class="form-label">Promotion Type</label>
                        <div class="form-check">
                            {{ form.is_featured }}
                            <label class="form-check-label" for="{{ form.is_featured.id_for_label }}">
                                Featured Product
                            </label>
                        </div>
                        <div class="form-check">
                            {{ form.is_best_seller }}
                            <label class="form-check-label" for="{{ form.is_best_seller.id_for_label }}">
                                Best Seller
                            </label>
                        </div>
                        <div class="form-check">
                            {{ form.is_new_arrival }}
                            <label class="form-check-label" for="{{ form.is_new_arrival.id_for_label }}">
                                New Arrival
                            </label>
                        </div>
                        <div class="form-check">
                            {{ form.is_top_deal }}
                            <label class="form-check-label" for="{{ form.is_top_deal.id_for_label }}">
                                Top Deal
                            </label>
                        </div>
                        <div class="form-check">
                            {{ form.is_special_offer }}
                            <label class="form-check-label" for="{{ form.is_special_offer.id_for_label }}">
                                Special Offer
                            </label>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Update Promotion
                        </button>
                        <a href="{% url 'admin_promotions' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Promotions
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Format datetime-local inputs to match Django's format
        const datetimeInputs = document.querySelectorAll('input[type="datetime-local"]');
        datetimeInputs.forEach(input => {
            if (input.value) {
                // Convert Django datetime format to HTML datetime-local format
                const djangoDateTime = input.value;
                if (djangoDateTime.includes('+')) {
                    // Remove timezone info if present
                    const localDateTime = djangoDateTime.split('+')[0];
                    input.value = localDateTime;
                }
            }
        });
    });
</script>
{% endblock %}
