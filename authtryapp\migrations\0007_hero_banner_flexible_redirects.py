# Generated by Django 5.0.6 on 2025-05-26 02:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('authtryapp', '0006_herobanner_carousel_coverage_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='herobanner',
            options={'ordering': ['order', '-created_at'], 'verbose_name': 'Hero Banner', 'verbose_name_plural': 'Hero Banners'},
        ),
        migrations.RenameField(
            model_name='herobanner',
            old_name='image_aspect_ratio',
            new_name='detected_aspect_ratio',
        ),
        migrations.RemoveField(
            model_name='herobanner',
            name='carousel_coverage',
        ),
        migrations.RemoveField(
            model_name='herobanner',
            name='display_order',
        ),
        migrations.RemoveField(
            model_name='herobanner',
            name='image_display_mode',
        ),
        migrations.RemoveField(
            model_name='herobanner',
            name='manual_height',
        ),
        migrations.RemoveField(
            model_name='herobanner',
            name='manual_width',
        ),
        migrations.RemoveField(
            model_name='herobanner',
            name='primary_button_text',
        ),
        migrations.RemoveField(
            model_name='herobanner',
            name='primary_button_url',
        ),
        migrations.RemoveField(
            model_name='herobanner',
            name='secondary_button_text',
        ),
        migrations.RemoveField(
            model_name='herobanner',
            name='secondary_button_url',
        ),
        migrations.RemoveField(
            model_name='herobanner',
            name='subtitle',
        ),
        migrations.AddField(
            model_name='herobanner',
            name='cta_label',
            field=models.CharField(default='Shop Now', help_text='Button text', max_length=50),
        ),
        migrations.AddField(
            model_name='herobanner',
            name='description',
            field=models.TextField(default='', help_text='Short description/subtitle', max_length=500),
        ),
        migrations.AddField(
            model_name='herobanner',
            name='detected_height',
            field=models.IntegerField(blank=True, help_text='Auto-detected image height', null=True),
        ),
        migrations.AddField(
            model_name='herobanner',
            name='detected_width',
            field=models.IntegerField(blank=True, help_text='Auto-detected image width', null=True),
        ),
        migrations.AddField(
            model_name='herobanner',
            name='height_unit',
            field=models.CharField(choices=[('px', 'Pixels'), ('%', 'Percentage')], default='px', help_text='Height unit', max_length=2),
        ),
        migrations.AddField(
            model_name='herobanner',
            name='link_target',
            field=models.CharField(default='#', help_text='URL/Slug/ID based on link_type', max_length=200),
        ),
        migrations.AddField(
            model_name='herobanner',
            name='link_type',
            field=models.CharField(choices=[('product', 'Product Page'), ('category', 'Product Category'), ('custom', 'Custom URL'), ('anchor', 'Page Section (Anchor)')], default='custom', help_text='Determines the redirect logic', max_length=10),
        ),
        migrations.AddField(
            model_name='herobanner',
            name='order',
            field=models.PositiveIntegerField(default=0, help_text='Display sequence of the banner (lower numbers first)'),
        ),
        migrations.AddField(
            model_name='herobanner',
            name='secondary_cta_label',
            field=models.CharField(blank=True, help_text='Secondary button text (optional)', max_length=50),
        ),
        migrations.AddField(
            model_name='herobanner',
            name='secondary_link_target',
            field=models.CharField(blank=True, help_text='Secondary button target', max_length=200),
        ),
        migrations.AddField(
            model_name='herobanner',
            name='secondary_link_type',
            field=models.CharField(blank=True, choices=[('product', 'Product Page'), ('category', 'Product Category'), ('custom', 'Custom URL'), ('anchor', 'Page Section (Anchor)')], help_text='Secondary button link type', max_length=10),
        ),
        migrations.AddField(
            model_name='herobanner',
            name='width_unit',
            field=models.CharField(choices=[('px', 'Pixels'), ('%', 'Percentage')], default='px', help_text='Width unit', max_length=2),
        ),
        migrations.AlterField(
            model_name='herobanner',
            name='image',
            field=models.ImageField(help_text='Banner image', upload_to='hero_banners/'),
        ),
        migrations.AlterField(
            model_name='herobanner',
            name='image_alt',
            field=models.CharField(help_text='Alt text for accessibility', max_length=200),
        ),
        migrations.AlterField(
            model_name='herobanner',
            name='image_height',
            field=models.IntegerField(blank=True, help_text='Image height (use with height_unit)', null=True),
        ),
        migrations.AlterField(
            model_name='herobanner',
            name='image_width',
            field=models.IntegerField(blank=True, help_text='Image width (use with width_unit)', null=True),
        ),
        migrations.AlterField(
            model_name='herobanner',
            name='is_active',
            field=models.BooleanField(default=True, help_text='Show this banner on the website'),
        ),
        migrations.AlterField(
            model_name='herobanner',
            name='title',
            field=models.CharField(help_text='Banner heading (e.g., product name or campaign)', max_length=200),
        ),
    ]
