document.addEventListener('DOMContentLoaded', function() {
    // Handle quantity changes
    const quantityForms = document.querySelectorAll('.quantity-form');

    quantityForms.forEach(form => {
        const decreaseBtn = form.querySelector('.decrease');
        const increaseBtn = form.querySelector('.increase');
        const quantityInput = form.querySelector('.quantity-input');
        const itemRow = form.closest('tr');
        const itemId = itemRow ? itemRow.dataset.itemId : null;

        if (decreaseBtn && increaseBtn && quantityInput && itemId) {
            // Decrease quantity
            decreaseBtn.addEventListener('click', function() {
                let currentValue = parseInt(quantityInput.value);
                if (currentValue > 1) {
                    quantityInput.value = currentValue - 1;
                    updateCartItem(itemId, currentValue - 1, form);
                }
            });

            // Increase quantity
            increaseBtn.addEventListener('click', function() {
                let currentValue = parseInt(quantityInput.value);
                quantityInput.value = currentValue + 1;
                updateCartItem(itemId, currentValue + 1, form);
            });

            // Handle direct input changes
            quantityInput.addEventListener('change', function() {
                let newValue = parseInt(quantityInput.value);
                if (isNaN(newValue) || newValue < 1) {
                    newValue = 1;
                    quantityInput.value = 1;
                }
                updateCartItem(itemId, newValue, form);
            });
        }
    });

    // Handle remove item buttons
    const removeForms = document.querySelectorAll('.remove-form');

    removeForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            const itemRow = form.closest('tr');
            const itemId = itemRow ? itemRow.dataset.itemId : null;

            if (itemId) {
                removeCartItem(itemId, form, itemRow);
            }
        });
    });

    // Handle clear cart button
    const clearCartForm = document.querySelector('.clear-cart-form');

    if (clearCartForm) {
        clearCartForm.addEventListener('submit', function(e) {
            e.preventDefault();
            clearCart(clearCartForm);
        });
    }

    // Function to update cart item quantity
    function updateCartItem(itemId, quantity, form) {
        const formData = new FormData();
        formData.append('quantity', quantity);

        // Get CSRF token from the form
        const csrfToken = form.querySelector('input[name="csrfmiddlewaretoken"]').value;

        fetch(`/cart/update/${itemId}/`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': csrfToken
            },
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.status === 'success') {
                // Update item total
                const itemRow = form.closest('tr');
                const itemTotalElement = itemRow.querySelector('.item-total');
                if (itemTotalElement) {
                    itemTotalElement.textContent = `$${data.item_total.toFixed(2)}`;
                }

                // Update cart count and subtotal
                updateCartSummary(data);
            } else {
                console.error('Error updating cart:', data.message);
            }
        })
        .catch(error => {
            console.error('Error updating cart:', error);
        });
    }

    // Function to remove item from cart
    function removeCartItem(itemId, form, itemRow) {
        // Get CSRF token from the form
        const csrfToken = form.querySelector('input[name="csrfmiddlewaretoken"]').value;

        fetch(`/cart/remove/${itemId}/`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': csrfToken
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.status === 'success') {
                // Remove the row from the table
                itemRow.remove();

                // Update cart count and subtotal
                updateCartSummary(data);

                // If cart is empty, reload the page to show empty cart message
                if (data.cart_total === 0) {
                    window.location.reload();
                }
            } else {
                console.error('Error removing item:', data.message);
            }
        })
        .catch(error => {
            console.error('Error removing item:', error);
        });
    }

    // Function to clear the cart
    function clearCart(form) {
        // Get CSRF token from the form
        const csrfToken = form.querySelector('input[name="csrfmiddlewaretoken"]').value;

        fetch('/cart/clear/', {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': csrfToken
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.status === 'success') {
                // Reload the page to show empty cart
                window.location.reload();
            } else {
                console.error('Error clearing cart:', data.message);
            }
        })
        .catch(error => {
            console.error('Error clearing cart:', error);
        });
    }

    // Function to update cart summary
    function updateCartSummary(data) {
        // Update all cart count elements
        const cartCountElements = document.querySelectorAll('.cart-count, .floating-cart-count');
        cartCountElements.forEach(element => {
            element.textContent = data.cart_total;
        });

        // Update subtotal and total
        const subtotalElement = document.querySelector('.cart-subtotal');
        const totalElement = document.querySelector('.cart-total');

        if (subtotalElement && data.cart_subtotal !== undefined) {
            subtotalElement.textContent = `$${data.cart_subtotal.toFixed(2)}`;
        }

        if (totalElement && data.cart_subtotal !== undefined) {
            totalElement.textContent = `$${data.cart_subtotal.toFixed(2)}`;
        }
    }
});
