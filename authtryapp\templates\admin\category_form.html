{% extends 'admin/base_admin.html' %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="mt-4">{{ action }} Category</h1>
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-edit me-1"></i>
            Category Details
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                
                {% if form.non_field_errors %}
                <div class="alert alert-danger">
                    {% for error in form.non_field_errors %}
                        {{ error }}
                    {% endfor %}
                </div>
                {% endif %}

                {% for field in form %}
                <div class="mb-3">
                    <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                    {{ field }}
                    {% if field.help_text %}
                        <div class="form-text">{{ field.help_text }}</div>
                    {% endif %}
                    {% if field.errors %}
                        <div class="alert alert-danger">
                            {{ field.errors }}
                        </div>
                    {% endif %}
                </div>
                {% endfor %}

                <div class="mb-3">
                    <button type="submit" class="btn btn-primary">Save Category</button>
                    <a href="{% url 'admin_categories' %}" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
