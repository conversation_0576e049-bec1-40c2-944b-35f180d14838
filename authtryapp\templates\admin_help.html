{% extends 'base_new.html' %}
{% load static %}

{% block title %}Admin Panel Guide - iTechStore{% endblock %}

{% block extra_css %}
<style>
    .admin-guide {
        max-width: 1200px;
        margin: 40px auto;
        padding: 0 20px;
    }

    .guide-header {
        text-align: center;
        margin-bottom: 50px;
    }

    .guide-header h1 {
        font-size: 36px;
        color: #333;
        margin-bottom: 10px;
    }

    .guide-header p {
        font-size: 18px;
        color: #666;
    }

    .admin-access-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 12px;
        text-align: center;
        margin-bottom: 40px;
    }

    .admin-access-card h2 {
        margin-bottom: 15px;
        font-size: 24px;
    }

    .admin-access-card .admin-url {
        background: rgba(255,255,255,0.2);
        padding: 15px;
        border-radius: 8px;
        font-family: monospace;
        font-size: 18px;
        margin: 20px 0;
    }

    .admin-btn {
        display: inline-block;
        background: white;
        color: #667eea;
        padding: 15px 30px;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 600;
        margin: 10px;
        transition: transform 0.3s ease;
    }

    .admin-btn:hover {
        transform: translateY(-2px);
        color: #667eea;
    }

    .guide-section {
        background: white;
        padding: 30px;
        border-radius: 12px;
        margin-bottom: 30px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .guide-section h3 {
        color: #333;
        margin-bottom: 20px;
        font-size: 24px;
        border-bottom: 2px solid #667eea;
        padding-bottom: 10px;
    }

    .feature-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }

    .feature-card {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        border-left: 4px solid #667eea;
    }

    .feature-card h4 {
        color: #333;
        margin-bottom: 10px;
    }

    .feature-card p {
        color: #666;
        line-height: 1.6;
    }

    .step-list {
        counter-reset: step-counter;
    }

    .step-list li {
        counter-increment: step-counter;
        margin-bottom: 15px;
        padding-left: 40px;
        position: relative;
    }

    .step-list li::before {
        content: counter(step-counter);
        position: absolute;
        left: 0;
        top: 0;
        background: #667eea;
        color: white;
        width: 25px;
        height: 25px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 14px;
    }

    .warning-box {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
        padding: 15px;
        border-radius: 8px;
        margin: 20px 0;
    }

    .success-box {
        background: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
        padding: 15px;
        border-radius: 8px;
        margin: 20px 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="admin-guide">
    <div class="guide-header">
        <h1>🎛️ Admin Panel Guide</h1>
        <p>Complete guide to managing your iTechStore carousel and content</p>
    </div>

    <!-- Admin Access Card -->
    <div class="admin-access-card">
        <h2>🔐 Access Admin Panel</h2>
        <div class="admin-url">http://localhost:8000/django-admin/</div>
        <a href="/django-admin/" class="admin-btn" target="_blank">
            <i class="fas fa-external-link-alt"></i> Open Admin Panel
        </a>
        {% if not user.is_superuser %}
        <div class="warning-box">
            ⚠️ You need superuser privileges to access the admin panel. Contact your administrator.
        </div>
        {% endif %}
    </div>

    <!-- Hero Carousel Management -->
    <div class="guide-section">
        <h3>🎨 Managing Hero Carousel</h3>
        <p>Control the main banner carousel on your homepage with custom content, images, and styling.</p>
        
        <div class="feature-grid">
            <div class="feature-card">
                <h4>📝 Banner Content</h4>
                <p>Set custom titles, subtitles, and brand text for each slide. Support for multi-line titles and rich descriptions.</p>
            </div>
            <div class="feature-card">
                <h4>🎨 Visual Design</h4>
                <p>Choose custom gradient backgrounds with hex color picker. Upload high-quality images for each banner.</p>
            </div>
            <div class="feature-card">
                <h4>🔗 Button Actions</h4>
                <p>Configure primary and secondary buttons with custom text and URLs. Link to products, categories, or external pages.</p>
            </div>
            <div class="feature-card">
                <h4>⚙️ Display Settings</h4>
                <p>Control banner order, activate/deactivate slides, and manage carousel timing and navigation.</p>
            </div>
        </div>

        <h4>Quick Steps:</h4>
        <ol class="step-list">
            <li>Go to <strong>AUTHTRYAPP → Hero Banners</strong></li>
            <li>Click <strong>"Add Hero Banner"</strong></li>
            <li>Fill in title, subtitle, and brand text</li>
            <li>Set button text and URLs</li>
            <li>Choose gradient colors and upload image</li>
            <li>Set display order and activate</li>
            <li>Save and view changes on homepage</li>
        </ol>
    </div>

    <!-- Site Configuration -->
    <div class="guide-section">
        <h3>⚙️ Site Configuration</h3>
        <p>Global settings for carousel behavior and site-wide preferences.</p>
        
        <div class="feature-grid">
            <div class="feature-card">
                <h4>🕐 Carousel Timing</h4>
                <p>Set auto-slide interval in milliseconds (5000 = 5 seconds). Control carousel speed globally.</p>
            </div>
            <div class="feature-card">
                <h4>🎯 Navigation Controls</h4>
                <p>Show/hide navigation arrows and dots. Customize user interaction with the carousel.</p>
            </div>
            <div class="feature-card">
                <h4>📱 Responsive Settings</h4>
                <p>Configure products per row for desktop, tablet, and mobile views. Optimize for all devices.</p>
            </div>
            <div class="feature-card">
                <h4>🏷️ Site Information</h4>
                <p>Update site name and tagline displayed across the website. Maintain brand consistency.</p>
            </div>
        </div>
    </div>

    <!-- Featured Products -->
    <div class="guide-section">
        <h3>🏷️ Featured Products Management</h3>
        <p>Control which products appear in different sections of your homepage.</p>
        
        <div class="success-box">
            ✅ <strong>Available Sections:</strong> Hero Section, Featured Products, Best Selling, New Arrivals, Top Deals
        </div>

        <h4>Managing Product Placement:</h4>
        <ol class="step-list">
            <li>Navigate to <strong>AUTHTRYAPP → Featured Products</strong></li>
            <li>Click <strong>"Add Featured Product"</strong></li>
            <li>Select product from dropdown</li>
            <li>Choose section (Featured, Best Selling, etc.)</li>
            <li>Set display order (lower numbers appear first)</li>
            <li>Activate and save</li>
        </ol>
    </div>

    <!-- Quick Actions -->
    <div class="guide-section">
        <h3>🚀 Quick Actions</h3>
        <div class="feature-grid">
            <div class="feature-card">
                <h4>🔄 Update Carousel</h4>
                <p>Changes are immediate - just save and refresh your website to see updates.</p>
            </div>
            <div class="feature-card">
                <h4>⏱️ Change Speed</h4>
                <p>Modify auto-slide interval in Site Configuration (3000 = 3 sec, 7000 = 7 sec).</p>
            </div>
            <div class="feature-card">
                <h4>👁️ Hide/Show Navigation</h4>
                <p>Toggle arrows and dots visibility in Site Configuration settings.</p>
            </div>
            <div class="feature-card">
                <h4>📋 Reorder Content</h4>
                <p>Change display order numbers to rearrange banners and products.</p>
            </div>
        </div>
    </div>

    <div class="success-box">
        🎉 <strong>All changes are real-time!</strong> No server restart required. Your updates appear immediately on the website.
    </div>
</div>
{% endblock %}
