<!-- Sales Product Card Component -->
<div class="sales-product-card" data-product-id="{{ product.id }}">
    <!-- Product Image Container -->
    <div class="sales-product-image-container">
        {% if product.image %}
            <img src="{{ product.image.url }}" alt="{{ product.name }}" class="sales-product-image">
        {% else %}
            <img src="https://images.unsplash.com/photo-1593640408182-31c70c8268f5?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                 alt="{{ product.name }}" class="sales-product-image">
        {% endif %}

        <!-- Product Badges -->
        <div class="sales-product-badges">
            {% if product.is_sponsored %}
                <span class="badge sponsored-badge">Sponsored</span>
            {% endif %}

            <!-- Sale Category Badges from Sales Management App -->
            {% for sale in product.active_sale_categories %}
                <span class="badge sale-category-badge"
                      style="background-color: {{ sale.badge_color }}; color: white;"
                      title="{{ sale.description }}">
                    {{ sale.get_display_text }}
                </span>
            {% endfor %}

            <!-- Fallback to original discount badge if no sale categories -->
            {% if not product.has_active_sales and product.is_on_sale %}
                <span class="badge discount-badge">SAVE ${{ product.discount_amount|floatformat:0 }}</span>
            {% endif %}

            {% if product.has_gift %}
                <span class="badge gift-badge">Gift With Purchase</span>
            {% endif %}
        </div>
    </div>

    <!-- Product Information -->
    <div class="sales-product-info">
        <!-- Product Title & Specs -->
        <div class="sales-product-title-section">
            <h3 class="sales-product-title">
                <a href="{% url 'product_detail_alt' product.slug %}" style="text-decoration: none; color: inherit;">
                    {{ product.name }}
                </a>
            </h3>
            {% if product.short_specs %}
                <p class="sales-product-specs">{{ product.short_specs }}</p>
            {% endif %}
        </div>

        <!-- Star Rating & Reviews -->
        {% if product.rating > 0 %}
        <div class="sales-product-rating">
            <div class="rating-stars">
                {% for star in product.rating_stars_range %}
                    {% if star <= product.rating %}
                        <i class="fas fa-star filled"></i>
                    {% elif star <= product.rating|add:0.5 %}
                        <i class="fas fa-star-half-alt filled"></i>
                    {% else %}
                        <i class="far fa-star"></i>
                    {% endif %}
                {% endfor %}
            </div>
            <span class="rating-text">{{ product.rating_display }}</span>
            {% if product.review_count > 0 %}
                <span class="review-count">({{ product.review_count }} Review{{ product.review_count|pluralize }})</span>
            {% endif %}
        </div>
        {% endif %}

        <!-- Price Section -->
        <div class="sales-product-price">
            {% if product.has_active_sales %}
                <!-- Show pricing with Sales Management discounts -->
                <div class="price-row">
                    <span class="current-price">₨. {{ product.get_effective_price|floatformat:2 }}</span>
                    {% if product.get_savings_amount > 0 %}
                        <span class="original-price">₨. {{ product.price|floatformat:2 }}</span>
                    {% endif %}
                </div>
                {% if product.get_savings_amount > 0 %}
                    <div class="savings-info">
                        <span class="savings-amount">Save ₨. {{ product.get_savings_amount|floatformat:0 }}</span>
                        {% with primary_sale=product.primary_sale_badge %}
                            {% if primary_sale.sale_type == 'percentage' %}
                                <span class="discount-percentage">{{ primary_sale.discount_percentage|floatformat:0 }}% OFF</span>
                            {% endif %}
                        {% endwith %}
                    </div>
                {% endif %}
            {% elif product.is_on_sale %}
                <!-- Fallback to original sale system -->
                <div class="price-row">
                    <span class="current-price">₨. {{ product.display_price|floatformat:2 }}</span>
                    <span class="original-price">₨. {{ product.original_price|floatformat:2 }}</span>
                </div>
                <div class="savings-info">
                    <span class="discount-percentage">{{ product.discount_percentage|floatformat:0 }}% OFF</span>
                    <span class="savings-amount">Save ₨. {{ product.discount_amount|floatformat:0 }}</span>
                </div>
            {% else %}
                <span class="current-price">₨. {{ product.display_price|floatformat:2 }}</span>
            {% endif %}
        </div>

        <!-- Gift Description -->
        {% if product.has_gift and product.gift_description %}
        <div class="gift-description">
            <i class="fas fa-gift"></i>
            <span>{{ product.gift_description }}</span>
        </div>
        {% endif %}

        <!-- Availability Status -->
        <div class="sales-product-availability">
            <span class="availability-status {% if product.is_available_to_ship %}available{% else %}unavailable{% endif %}">
                {{ product.availability_display }}
            </span>
        </div>

        <!-- Action Buttons -->
        <div class="sales-product-actions">
            {% if product.is_available_to_ship %}
                <button class="add-to-cart-btn primary" data-product-id="{{ product.id }}">
                    <i class="fas fa-shopping-cart"></i>
                    Add to Cart
                </button>
            {% elif product.stock_status == 'preorder' %}
                <button class="add-to-cart-btn preorder" data-product-id="{{ product.id }}">
                    <i class="fas fa-clock"></i>
                    Pre-order Now
                </button>
            {% else %}
                <button class="add-to-cart-btn disabled" disabled>
                    <i class="fas fa-times"></i>
                    Out of Stock
                </button>
            {% endif %}

            <a href="{% url 'product_detail_alt' product.slug %}" class="quick-view-btn" title="View Details">
                <i class="fas fa-eye"></i>
            </a>
        </div>
    </div>
</div>
