/* Hero Banner Admin Enhancements */

.field-get_redirect_url_preview {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    border-left: 4px solid #007cba;
    font-family: monospace;
    font-size: 12px;
}

.field-get_image_info {
    background: #e8f5e8;
    padding: 8px;
    border-radius: 4px;
    font-weight: bold;
}

/* Link type specific styling */
.field-link_type select {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 8px;
    border-radius: 4px;
}

.field-link_target input {
    font-family: monospace;
    background: #f1f3f4;
    border: 2px solid #e1e5e9;
    padding: 8px;
    border-radius: 4px;
}

.field-link_target input:focus {
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2);
}

/* CTA button styling */
.field-cta_label input,
.field-secondary_cta_label input {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    font-weight: bold;
}

/* Image dimension fields */
.field-image_width input,
.field-image_height input {
    width: 80px;
    text-align: center;
    font-weight: bold;
    border: 2px solid #6c757d;
    border-radius: 4px;
    padding: 6px;
}

.field-width_unit select,
.field-height_unit select {
    width: 60px;
    margin-left: 5px;
    border: 2px solid #6c757d;
    border-radius: 4px;
    padding: 6px;
}

/* Order field enhancement */
.field-order input {
    width: 60px;
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    background: #fff3cd;
    border: 2px solid #ffc107;
    border-radius: 4px;
    padding: 8px;
}

/* Fieldset enhancements */
.module h2 {
    background: linear-gradient(135deg, #007cba 0%, #0056b3 100%);
    color: white;
    padding: 12px 15px;
    margin: 0;
    border-radius: 4px 4px 0 0;
}

.module .description {
    background: #e3f2fd;
    padding: 10px 15px;
    margin: 0;
    border-left: 4px solid #2196f3;
    font-style: italic;
    color: #1565c0;
}

/* Collapse sections */
.collapse .module {
    border: 2px dashed #dee2e6;
}

.collapse .module h2 {
    background: #6c757d;
    opacity: 0.8;
}

/* Help text enhancements */
.help {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 8px;
    margin-top: 5px;
    font-size: 12px;
    color: #856404;
}

/* List display enhancements */
.result_list .field-get_image_info {
    font-family: monospace;
    font-size: 11px;
    background: #e8f5e8;
    padding: 4px;
    border-radius: 3px;
}

.result_list .field-link_type {
    font-weight: bold;
    text-transform: uppercase;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 3px;
    color: white;
}

.result_list .field-link_type[data-type="product"] {
    background: #28a745;
}

.result_list .field-link_type[data-type="category"] {
    background: #007bff;
}

.result_list .field-link_type[data-type="custom"] {
    background: #6c757d;
}

.result_list .field-link_type[data-type="anchor"] {
    background: #ffc107;
    color: #212529;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .field-image_width input,
    .field-image_height input {
        width: 60px;
    }
    
    .field-width_unit select,
    .field-height_unit select {
        width: 50px;
    }
}
