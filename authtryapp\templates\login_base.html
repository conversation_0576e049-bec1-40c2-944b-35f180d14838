{% extends 'base_new.html' %}
{% load static %}

{% block title %}Login - iTechStore{% endblock %}

{% block extra_css %}
<style>
    /* Additional styles for login page */
    .login-container {
        padding: 60px 0;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: calc(100vh - 300px);
    }
    
    .login-form {
        background-color: white;
        border-radius: 10px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        padding: 40px;
        width: 100%;
        max-width: 450px;
    }
    
    .login-header {
        text-align: center;
        margin-bottom: 30px;
    }
    
    .login-header h1 {
        font-size: 28px;
        margin-bottom: 10px;
        color: #333;
    }
    
    .login-header p {
        color: #777;
        font-size: 16px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #333;
    }
    
    .form-group input {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 16px;
        transition: border-color 0.3s;
    }
    
    .form-group input:focus {
        border-color: #4dabf7;
        outline: none;
    }
    
    .login-button {
        width: 100%;
        padding: 14px;
        background: linear-gradient(to right, #4dabf7, #0c8599);
        color: white;
        border: none;
        border-radius: 5px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: opacity 0.3s;
    }
    
    .login-button:hover {
        opacity: 0.9;
    }
    
    .login-footer {
        text-align: center;
        margin-top: 25px;
        color: #777;
    }
    
    .login-footer a {
        color: #4dabf7;
        font-weight: 500;
        text-decoration: none;
    }
    
    .login-footer a:hover {
        text-decoration: underline;
    }
    
    .alert {
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
        color: white;
    }
    
    .alert-danger {
        background-color: #f44336;
    }
    
    .alert-success {
        background-color: #4caf50;
    }
    
    .social-login {
        margin-top: 30px;
        text-align: center;
    }
    
    .social-login p {
        color: #777;
        margin-bottom: 15px;
        position: relative;
    }
    
    .social-login p::before,
    .social-login p::after {
        content: "";
        position: absolute;
        top: 50%;
        width: 35%;
        height: 1px;
        background-color: #ddd;
    }
    
    .social-login p::before {
        left: 0;
    }
    
    .social-login p::after {
        right: 0;
    }
    
    .social-buttons {
        display: flex;
        justify-content: center;
        gap: 15px;
    }
    
    .social-button {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        transition: transform 0.3s;
    }
    
    .social-button:hover {
        transform: translateY(-3px);
    }
    
    .facebook {
        background-color: #3b5998;
    }
    
    .google {
        background-color: #db4437;
    }
    
    .twitter {
        background-color: #1da1f2;
    }
</style>
{% endblock %}

{% block content %}
<!-- Login Section -->
<section class="login-container">
    <div class="login-form">
        <div class="login-header">
            <h1>Welcome Back</h1>
            <p>Sign in to your iTechStore account</p>
        </div>
        
        {% if messages %}
            {% for message in messages %}
                <div class="alert {% if message.tags == 'error' %}alert-danger{% else %}alert-success{% endif %}">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
        
        {% if form.errors %}
            <div class="alert alert-danger">
                Invalid username or password. Please try again.
            </div>
        {% endif %}
        
        <form method="post" action="{% url 'login' %}">
            {% csrf_token %}
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" placeholder="Enter your username" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" placeholder="Enter your password" required>
            </div>
            <button type="submit" class="login-button">Sign In</button>
            
            <div class="login-footer">
                <p>Don't have an account? <a href="{% url 'register' %}">Create an account</a></p>
            </div>
            
            <div class="social-login">
                <p>Or sign in with</p>
                <div class="social-buttons">
                    <a href="#" class="social-button facebook"><i class="fab fa-facebook-f"></i></a>
                    <a href="#" class="social-button google"><i class="fab fa-google"></i></a>
                    <a href="#" class="social-button twitter"><i class="fab fa-twitter"></i></a>
                </div>
            </div>
        </form>
    </div>
</section>
{% endblock %}
