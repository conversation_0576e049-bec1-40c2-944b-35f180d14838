{% extends "admin/change_list.html" %}
{% load i18n admin_urls static admin_list %}

{% block title %}🏷️ Sale Categories - {{ block.super }}{% endblock %}

{% block extrahead %}
{{ block.super }}
<style>
    .sale-categories-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        text-align: center;
    }
    
    .sale-categories-header h1 {
        color: white;
        margin: 0;
        font-size: 28px;
    }
    
    .sale-categories-header p {
        margin: 10px 0 0 0;
        opacity: 0.9;
        font-size: 16px;
    }
    
    .quick-stats {
        display: flex;
        justify-content: center;
        gap: 30px;
        margin-top: 15px;
    }
    
    .stat-item {
        text-align: center;
    }
    
    .stat-number {
        font-size: 24px;
        font-weight: bold;
        display: block;
    }
    
    .stat-label {
        font-size: 12px;
        opacity: 0.8;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .quick-actions {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
        text-align: center;
    }
    
    .quick-actions h3 {
        margin: 0 0 10px 0;
        color: #333;
        font-size: 16px;
    }
    
    .quick-action-btn {
        display: inline-block;
        padding: 8px 16px;
        margin: 0 5px;
        background: #007bff;
        color: white;
        text-decoration: none;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        transition: background-color 0.2s;
    }
    
    .quick-action-btn:hover {
        background: #0056b3;
        color: white;
        text-decoration: none;
    }
    
    .quick-action-btn.success {
        background: #28a745;
    }
    
    .quick-action-btn.success:hover {
        background: #1e7e34;
    }
    
    .quick-action-btn.warning {
        background: #ffc107;
        color: #212529;
    }
    
    .quick-action-btn.warning:hover {
        background: #e0a800;
    }
</style>
{% endblock %}

{% block content_title %}
<div class="sale-categories-header">
    <h1>🏷️ Sale Categories Management</h1>
    <p>Create and manage different types of sales, promotions, and special offers</p>
    
    <div class="quick-stats">
        <div class="stat-item">
            <span class="stat-number">{{ cl.result_count }}</span>
            <span class="stat-label">Total Categories</span>
        </div>
        <div class="stat-item">
            <span class="stat-number">{{ active_count|default:"0" }}</span>
            <span class="stat-label">Active Sales</span>
        </div>
        <div class="stat-item">
            <span class="stat-number">{{ featured_count|default:"0" }}</span>
            <span class="stat-label">Featured</span>
        </div>
    </div>
</div>

<div class="quick-actions">
    <h3>Quick Actions</h3>
    <a href="{% url 'admin:authtryapp_salecategory_add' %}" class="quick-action-btn success">
        ➕ Create New Sale Category
    </a>
    <a href="{% url 'admin:authtryapp_product_changelist' %}" class="quick-action-btn">
        📦 Manage Products
    </a>
    <a href="/admin/authtryapp/salecategory/?is_active__exact=1" class="quick-action-btn">
        ✅ View Active Sales
    </a>
    <a href="/admin/authtryapp/salecategory/?is_featured__exact=1" class="quick-action-btn warning">
        ⭐ View Featured Sales
    </a>
</div>
{% endblock %}

{% block result_list %}
<div class="results">
    <table id="result_list">
        <thead>
            <tr>
                <th scope="col" class="action-checkbox-column">
                    <div class="text">
                        <span><input type="checkbox" id="action-toggle"></span>
                    </div>
                </th>
                {% for header in result_headers %}
                    <th scope="col" {{ header.class_attrib }}>
                        {% if header.sortable %}
                            {% if header.sort_priority > 0 %}
                                <div class="sortoptions">
                                    <a class="sortremove" href="{{ header.url_remove }}" title="{% trans "Remove from sorting" %}"></a>
                                    {% if header.ascending %}
                                        <a href="{{ header.url_toggle }}" class="toggle ascending" title="{% trans "Toggle sorting" %}"></a>
                                    {% else %}
                                        <a href="{{ header.url_toggle }}" class="toggle descending" title="{% trans "Toggle sorting" %}"></a>
                                    {% endif %}
                                </div>
                            {% endif %}
                            <div class="text"><a href="{{ header.url_primary }}">{{ header.text|capfirst }}</a></div>
                        {% else %}
                            <div class="text"><span>{{ header.text|capfirst }}</span></div>
                        {% endif %}
                    </th>
                {% endfor %}
            </tr>
        </thead>
        <tbody>
            {% for result in results %}
                {% cycle 'row1' 'row2' as rowcolors silent %}
                <tr class="{% cycle rowcolors %} sale-category-row
                    {% if result.is_featured %}featured{% endif %}
                    {% if not result.is_active %}inactive{% endif %}">
                    <td class="action-checkbox">
                        <input type="checkbox" name="_selected_action" value="{{ result.pk|unlocalize }}" class="action-select">
                    </td>
                    {% for item in result.items %}
                        <td{{ item.class_attrib }}>{{ item.contents|safe }}</td>
                    {% endfor %}
                </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}

{% block extrajs %}
{{ block.super }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add enhanced styling to the table
    const table = document.querySelector('#result_list');
    if (table) {
        table.style.borderRadius = '8px';
        table.style.overflow = 'hidden';
        table.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
    }
    
    // Add tooltips to status indicators
    const statusCells = document.querySelectorAll('.field-is_currently_active img');
    statusCells.forEach(cell => {
        const isActive = cell.src.includes('icon-yes');
        cell.title = isActive ? 'Currently Active' : 'Currently Inactive';
    });
    
    // Enhance the add button
    const addButton = document.querySelector('.addlink');
    if (addButton) {
        addButton.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
        addButton.style.color = 'white';
        addButton.style.padding = '10px 20px';
        addButton.style.borderRadius = '6px';
        addButton.style.fontWeight = '600';
        addButton.style.textDecoration = 'none';
        addButton.style.display = 'inline-block';
        addButton.style.marginBottom = '20px';
    }
});
</script>
{% endblock %}
