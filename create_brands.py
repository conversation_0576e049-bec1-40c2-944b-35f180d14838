import os
import django
from django.conf import settings

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'authtry.settings')
django.setup()

from authtryapp.models import Brand

# List of popular tech brands with descriptions
brands_data = [
    {
        'name': 'Apple',
        'description': 'Known for premium design and innovative technology, Apple offers a range of high-performance MacBooks with macOS.',
    },
    {
        'name': 'Dell',
        'description': 'A leading computer manufacturer offering diverse laptops from budget-friendly Inspiron to premium XPS series.',
    },
    {
        'name': 'HP',
        'description': 'Hewlett-Packard provides reliable laptops for home, business, and gaming, with options for every budget.',
    },
    {
        'name': 'Lenovo',
        'description': 'Known for ThinkPad business laptops and innovative designs, Lenovo offers excellent build quality and performance.',
    },
    {
        'name': 'Asus',
        'description': 'From gaming powerhouses like ROG to lightweight ZenBooks, Asus offers cutting-edge technology at competitive prices.',
    },
    {
        'name': 'Acer',
        'description': 'Provides affordable laptops with good performance, from lightweight Swifts to powerful gaming Predator series.',
    },
    {
        'name': 'MSI',
        'description': 'Specializing in high-performance gaming laptops with powerful graphics and advanced cooling systems.',
    },
    {
        'name': 'Samsung',
        'description': 'Known for sleek, lightweight laptops with vibrant displays and innovative features.',
    },
    {
        'name': 'Razer',
        'description': 'Premium gaming laptops with high-end specs, RGB lighting, and sleek designs for serious gamers.',
    },
    {
        'name': 'Microsoft',
        'description': 'Creators of Surface laptops and tablets, focusing on premium design, touch screens, and Windows integration.',
    },
    {
        'name': 'LG',
        'description': 'Known for ultralight laptops with excellent displays and long battery life.',
    },
    {
        'name': 'Gigabyte',
        'description': 'Powerful gaming laptops with high-end components and excellent thermal management.',
    }
]

def create_brands():
    """Create brands if they don't already exist"""
    brands_created = 0
    brands_existing = 0
    
    for brand_data in brands_data:
        brand, created = Brand.objects.get_or_create(
            name=brand_data['name'],
            defaults={
                'description': brand_data['description']
            }
        )
        
        if created:
            print(f"Created brand: {brand.name}")
            brands_created += 1
        else:
            print(f"Brand already exists: {brand.name}")
            brands_existing += 1
    
    print(f"\nSummary: {brands_created} brands created, {brands_existing} already existed.")

if __name__ == '__main__':
    print("Creating brands...")
    create_brands()
    print("Done!") 