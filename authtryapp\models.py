from django.contrib.auth.models import User
from django.db import models
from decimal import Decimal
from django.utils.text import slugify
from django.urls import reverse
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator

class Brand(models.Model):
    name = models.CharField(max_length=100)
    slug = models.SlugField(max_length=100, unique=True, blank=True)
    description = models.TextField(blank=True, null=True)
    logo = models.ImageField(upload_to='brands/', null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

class Category(models.Model):
    name = models.CharField(max_length=100)
    slug = models.SlugField(max_length=100, unique=True, blank=True)  # Allow blank as it's auto-generated
    description = models.TextField(blank=True, null=True)
    image = models.ImageField(upload_to='categories/', null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = 'Categories'
        ordering = ['name']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

class Product(models.Model):
    STOCK_STATUS_CHOICES = [
        ('available', 'Available'),
        ('preorder', 'Pre-order'),
        ('notavailable', 'Not Available'),
    ]

    name = models.CharField(max_length=200)
    slug = models.SlugField(max_length=200, unique=True, blank=True)  # Allow blank as it's auto-generated
    brand = models.ForeignKey('Brand', on_delete=models.SET_NULL, null=True, blank=True, related_name='products')
    price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    description = models.TextField(blank=True, default='')  # Add default empty string
    short_specs = models.CharField(max_length=300, blank=True, default='')  # Short specifications for sales display
    image = models.ImageField(upload_to='products/', null=True, blank=True)
    category = models.ForeignKey('Category', on_delete=models.SET_NULL, null=True, blank=True, related_name='products')
    stock_status = models.CharField(
        max_length=20,
        choices=STOCK_STATUS_CHOICES,
        default='available'
    )

    # Sales and Rating Fields
    original_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, help_text="Original price before discount")
    rating = models.DecimalField(max_digits=3, decimal_places=2, default=0.0, help_text="Average rating (0.0 to 5.0)")
    review_count = models.PositiveIntegerField(default=0, help_text="Number of reviews")
    is_sponsored = models.BooleanField(default=False, help_text="Show sponsored tag")
    has_gift = models.BooleanField(default=False, help_text="Gift with purchase available")
    gift_description = models.CharField(max_length=200, blank=True, default='', help_text="Description of gift (e.g., 'Free headphones')")

    # Sale category relationship
    sale_categories = models.ManyToManyField('SaleCategory', blank=True, related_name='products', help_text="Sale categories this product belongs to")

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            base_slug = slugify(self.name)
            counter = 1
            self.slug = base_slug
            while Product.objects.filter(slug=self.slug).exclude(id=self.id).exists():
                self.slug = f"{base_slug}-{counter}"
                counter += 1
        super().save(*args, **kwargs)

    @property
    def is_on_sale(self):
        """Check if product is on sale (has original price higher than current price)"""
        return self.original_price and self.price and self.original_price > self.price

    @property
    def discount_amount(self):
        """Calculate discount amount"""
        if self.is_on_sale:
            return self.original_price - self.price
        return Decimal('0.00')

    @property
    def discount_percentage(self):
        """Calculate discount percentage"""
        if self.is_on_sale and self.original_price > 0:
            return round(((self.original_price - self.price) / self.original_price) * 100, 0)
        return 0

    @property
    def display_price(self):
        """Get the price to display (sale price if on sale, otherwise regular price)"""
        return self.price if self.price else Decimal('0.00')

    @property
    def is_available_to_ship(self):
        """Check if product is available to ship"""
        return self.stock_status == 'available'

    @property
    def availability_display(self):
        """Get formatted availability status"""
        if self.stock_status == 'available':
            return "✅ Available to ship"
        elif self.stock_status == 'preorder':
            return "📅 Pre-order"
        else:
            return "❌ Out of stock"

    @property
    def rating_stars_range(self):
        """Get range for template star rendering (1-5)"""
        return range(1, 6)

    @property
    def rating_display(self):
        """Get formatted rating for display"""
        if self.rating > 0:
            return f"{self.rating:.1f}"
        return "No rating"

    @property
    def active_sale_categories(self):
        """Get currently active sale categories for this product from Sales Management app"""
        try:
            from sales_management.models import ProductSaleAssignment
            assignments = ProductSaleAssignment.objects.filter(
                product=self,
                is_active=True,
                sale_category__is_active=True
            ).select_related('sale_category')

            active_sales = []
            for assignment in assignments:
                sale = assignment.sale_category
                if sale.is_currently_active():
                    active_sales.append(sale)

            # Sort by priority and featured status
            return sorted(active_sales, key=lambda x: (-x.priority, -x.is_featured))
        except ImportError:
            # Fallback to old system if sales_management app not available
            return self.sale_categories.filter(is_active=True).filter(
                models.Q(end_date__isnull=True) | models.Q(end_date__gte=timezone.now())
            ).filter(start_date__lte=timezone.now())

    @property
    def primary_sale_badge(self):
        """Get the primary sale badge to display (highest priority)"""
        active_sales = self.active_sale_categories
        if active_sales:
            # Return the highest priority sale
            return active_sales[0] if isinstance(active_sales, list) else active_sales.first()
        return None

    @property
    def has_active_sales(self):
        """Check if product has any active sales"""
        active_sales = self.active_sale_categories
        return len(active_sales) > 0 if isinstance(active_sales, list) else active_sales.exists()

    def get_sale_badges(self):
        """Get all sale badges for this product"""
        active_sales = self.active_sale_categories
        if isinstance(active_sales, list):
            return [
                {
                    'text': sale.get_display_text(),
                    'color': sale.badge_color,
                    'description': sale.description,
                    'type': sale.sale_type
                }
                for sale in active_sales
            ]
        else:
            return [
                {
                    'text': sale.get_display_text(),
                    'color': sale.badge_color,
                    'description': sale.description,
                    'type': sale.sale_type
                }
                for sale in active_sales
            ]

    def get_effective_price(self):
        """Get the final price after applying the best available discount"""
        primary_sale = self.primary_sale_badge
        if primary_sale:
            return primary_sale.get_final_price(self.price)
        return self.price

    def get_savings_amount(self):
        """Get the total savings amount from the best available discount"""
        primary_sale = self.primary_sale_badge
        if primary_sale:
            return primary_sale.get_savings_amount(self.price)
        return Decimal('0.00')

class Cart(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    session_id = models.CharField(max_length=100, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Cart {self.id}"

    @property
    def total_price(self):
        return sum(item.total_price for item in self.items.all())

    @property
    def total_items(self):
        return sum(item.quantity for item in self.items.all())

class CartItem(models.Model):
    cart = models.ForeignKey(Cart, related_name='items', on_delete=models.CASCADE)
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField(default=1)
    is_preorder = models.BooleanField(default=False)
    added_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.quantity} x {self.product.name}"

    @property
    def total_price(self):
        if self.product.price:
            return self.product.price * Decimal(self.quantity)
        return Decimal('0.00')


class SectionHeading(models.Model):
    """Model for editable section headings on the website"""
    SECTION_TYPES = [
        ('featured_products', 'Featured Products'),
        ('best_selling', 'Best Selling Products'),
        ('new_arrivals', 'New Arrivals'),
        ('top_deals', 'Top Deals'),
        ('special_offers', 'Special Offers'),
        ('clearance', 'Clearance'),
    ]

    section_type = models.CharField(max_length=50, choices=SECTION_TYPES, unique=True)
    title = models.CharField(max_length=100)
    subtitle = models.CharField(max_length=200, blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title

    class Meta:
        verbose_name = 'Section Heading'
        verbose_name_plural = 'Section Headings'


class ProductPromotion(models.Model):
    """Model for product promotions and sales"""
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='promotions')
    sale_price = models.DecimalField(max_digits=10, decimal_places=2)
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True)
    is_featured = models.BooleanField(default=False)
    is_best_seller = models.BooleanField(default=False)
    is_new_arrival = models.BooleanField(default=False)
    is_top_deal = models.BooleanField(default=False)
    is_special_offer = models.BooleanField(default=False)
    start_date = models.DateTimeField(default=timezone.now)
    end_date = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.product.name} - {self.sale_price}"

    def is_active(self):
        now = timezone.now()
        if self.end_date:
            return self.start_date <= now <= self.end_date
        return self.start_date <= now

    def save(self, *args, **kwargs):
        # Calculate discount percentage if not provided
        if not self.discount_percentage and self.product.price:
            original_price = self.product.price
            if original_price > 0:
                discount = ((original_price - self.sale_price) / original_price) * 100
                self.discount_percentage = round(discount, 2)
        super().save(*args, **kwargs)


class SaleCategory(models.Model):
    """Model for different types of sales and promotions"""
    SALE_TYPE_CHOICES = [
        ('percentage', 'Percentage Discount'),
        ('fixed_amount', 'Fixed Amount Off'),
        ('free_item', 'Free Item with Purchase'),
        ('buy_x_get_y', 'Buy X Get Y Free'),
        ('bundle', 'Bundle Deal'),
        ('clearance', 'Clearance Sale'),
        ('flash_sale', 'Flash Sale'),
        ('seasonal', 'Seasonal Sale'),
        ('gift_with_purchase', 'Gift with Purchase'),
        ('free_shipping', 'Free Shipping'),
    ]

    BADGE_COLOR_CHOICES = [
        ('#dc3545', 'Red'),
        ('#fd7e14', 'Orange'),
        ('#ffc107', 'Yellow'),
        ('#28a745', 'Green'),
        ('#007bff', 'Blue'),
        ('#6f42c1', 'Purple'),
        ('#e83e8c', 'Pink'),
        ('#17a2b8', 'Teal'),
        ('#6c757d', 'Gray'),
        ('#343a40', 'Dark'),
    ]

    name = models.CharField(max_length=100, help_text="Sale category name (e.g., '10% Off', 'Free Headphones')")
    slug = models.SlugField(max_length=100, unique=True, blank=True)
    sale_type = models.CharField(max_length=20, choices=SALE_TYPE_CHOICES, default='percentage')

    # Sale details
    discount_percentage = models.DecimalField(
        max_digits=5, decimal_places=2, null=True, blank=True,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Discount percentage (0-100)"
    )
    discount_amount = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True,
        help_text="Fixed discount amount"
    )
    free_item_description = models.CharField(
        max_length=200, blank=True,
        help_text="Description of free item (e.g., 'Free wireless mouse')"
    )
    buy_quantity = models.PositiveIntegerField(
        null=True, blank=True,
        help_text="Buy X quantity (for Buy X Get Y deals)"
    )
    get_quantity = models.PositiveIntegerField(
        null=True, blank=True,
        help_text="Get Y quantity free (for Buy X Get Y deals)"
    )

    # Display settings
    badge_text = models.CharField(max_length=50, help_text="Text to show on product badge")
    badge_color = models.CharField(max_length=7, choices=BADGE_COLOR_CHOICES, default='#dc3545')
    description = models.TextField(blank=True, help_text="Detailed description of the sale")

    # Timing
    start_date = models.DateTimeField(default=timezone.now)
    end_date = models.DateTimeField(null=True, blank=True, help_text="Leave blank for ongoing sale")

    # Status
    is_active = models.BooleanField(default=True)
    is_featured = models.BooleanField(default=False, help_text="Show prominently on homepage")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-is_featured', '-start_date']
        verbose_name = 'Sale Category'
        verbose_name_plural = 'Sale Categories'

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    def is_currently_active(self):
        """Check if sale is currently active"""
        if not self.is_active:
            return False

        now = timezone.now()
        if self.end_date:
            return self.start_date <= now <= self.end_date
        return self.start_date <= now

    def get_display_text(self):
        """Get formatted display text for the sale"""
        if self.sale_type == 'percentage' and self.discount_percentage:
            return f"{int(self.discount_percentage)}% OFF"
        elif self.sale_type == 'fixed_amount' and self.discount_amount:
            return f"SAVE ${self.discount_amount}"
        elif self.sale_type == 'free_item' and self.free_item_description:
            return f"FREE {self.free_item_description.upper()}"
        elif self.sale_type == 'buy_x_get_y' and self.buy_quantity and self.get_quantity:
            return f"BUY {self.buy_quantity} GET {self.get_quantity} FREE"
        else:
            return self.badge_text


class HeroBanner(models.Model):
    """Model for hero carousel banners with flexible redirects and editable properties"""

    # Link type choices for flexible redirects
    LINK_TYPE_CHOICES = [
        ('product', 'Product Page'),
        ('category', 'Product Category'),
        ('custom', 'Custom URL'),
        ('anchor', 'Page Section (Anchor)'),
    ]

    # Image dimension unit choices
    DIMENSION_UNIT_CHOICES = [
        ('px', 'Pixels'),
        ('%', 'Percentage'),
    ]

    IMAGE_DISPLAY_CHOICES = [
        ('contain', 'Fit in Frame (Show full image)'),
        ('cover', 'Fill Carousel (Crop if needed)'),
        ('auto', 'Auto-detect (Smart sizing)'),
    ]

    # Basic banner content
    title = models.CharField(max_length=200, help_text="Banner heading (e.g., product name or campaign)")
    description = models.TextField(max_length=500, default="", help_text="Short description/subtitle")
    brand_text = models.CharField(max_length=50, blank=True, null=True, help_text="Brand text (e.g., SAPPHIRE PULSE)")

    # Image and visual properties
    image = models.ImageField(upload_to='hero_banners/', help_text="Banner image")
    image_alt = models.CharField(max_length=200, help_text="Alt text for accessibility")

    # Editable image dimensions
    image_width = models.IntegerField(null=True, blank=True, help_text="Image width (use with width_unit)")
    image_height = models.IntegerField(null=True, blank=True, help_text="Image height (use with height_unit)")
    width_unit = models.CharField(max_length=2, choices=DIMENSION_UNIT_CHOICES, default='px', help_text="Width unit")
    height_unit = models.CharField(max_length=2, choices=DIMENSION_UNIT_CHOICES, default='px', help_text="Height unit")

    # Background styling
    background_gradient_start = models.CharField(max_length=7, default="#8B5CF6", help_text="Hex color code")
    background_gradient_end = models.CharField(max_length=7, default="#EC4899", help_text="Hex color code")

    # Flexible redirect system
    link_type = models.CharField(max_length=10, choices=LINK_TYPE_CHOICES, default='custom',
                               help_text="Determines the redirect logic")
    link_target = models.CharField(max_length=200, default="#", help_text="URL/Slug/ID based on link_type")
    cta_label = models.CharField(max_length=50, default="Shop Now", help_text="Button text")

    # Secondary button (optional)
    secondary_cta_label = models.CharField(max_length=50, blank=True, help_text="Secondary button text (optional)")
    secondary_link_type = models.CharField(max_length=10, choices=LINK_TYPE_CHOICES, blank=True,
                                         help_text="Secondary button link type")
    secondary_link_target = models.CharField(max_length=200, blank=True, help_text="Secondary button target")

    # Display settings
    order = models.PositiveIntegerField(default=0, help_text="Display sequence of the banner (lower numbers first)")
    is_active = models.BooleanField(default=True, help_text="Show this banner on the website")

    # Auto-detected properties (for reference)
    detected_width = models.IntegerField(null=True, blank=True, help_text="Auto-detected image width")
    detected_height = models.IntegerField(null=True, blank=True, help_text="Auto-detected image height")
    detected_aspect_ratio = models.FloatField(null=True, blank=True, help_text="Auto-calculated aspect ratio")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', '-created_at']
        verbose_name = 'Hero Banner'
        verbose_name_plural = 'Hero Banners'

    def __str__(self):
        return f"{self.title} (Order: {self.order})"

    def clean(self):
        """Validate model fields"""
        from django.core.exceptions import ValidationError

        # Validate image dimensions
        if self.image_width is not None and self.image_width < 1:
            raise ValidationError({'image_width': 'Image width must be greater than 0'})

        if self.image_height is not None and self.image_height < 1:
            raise ValidationError({'image_height': 'Image height must be greater than 0'})

        # Validate link target based on link type
        if self.link_type == 'product' and not self.link_target:
            raise ValidationError({'link_target': 'Product slug/ID is required for product links'})

        if self.link_type == 'category' and not self.link_target:
            raise ValidationError({'link_target': 'Category slug is required for category links'})

    def get_image_dimensions_css(self):
        """Return CSS style for image dimensions"""
        styles = []

        if self.image_width:
            styles.append(f"width: {self.image_width}{self.width_unit}")

        if self.image_height:
            styles.append(f"height: {self.image_height}{self.height_unit}")

        return "; ".join(styles) if styles else ""

    def get_image_info(self):
        """Return human-readable image information"""
        if self.image_width and self.image_height:
            return f"{self.image_width}{self.width_unit} × {self.image_height}{self.height_unit}"
        elif self.detected_width and self.detected_height:
            return f"{self.detected_width}px × {self.detected_height}px (Auto-detected)"
        return "No dimensions set"

    def get_redirect_url(self, button_type='primary'):
        """Generate the correct redirect URL based on link_type"""
        from django.urls import reverse
        from django.core.exceptions import ObjectDoesNotExist

        # Determine which link to process
        if button_type == 'primary':
            link_type = self.link_type
            link_target = self.link_target
        else:
            link_type = self.secondary_link_type
            link_target = self.secondary_link_target

        if not link_type or not link_target:
            return '#'

        try:
            if link_type == 'product':
                # Handle product links - try by slug first, then by ID
                try:
                    if link_target.isdigit():
                        product = Product.objects.get(id=int(link_target))
                    else:
                        product = Product.objects.get(slug=link_target)
                    return reverse('product_detail', kwargs={'product_slug': product.slug})
                except ObjectDoesNotExist:
                    return f"/product/{link_target}/"

            elif link_type == 'category':
                # Handle category links
                try:
                    if link_target.isdigit():
                        category = Category.objects.get(id=int(link_target))
                        return reverse('category_view', kwargs={'slug': category.slug})
                    else:
                        return reverse('category_view', kwargs={'slug': link_target})
                except (ObjectDoesNotExist, Exception):
                    return f"/category/{link_target}/"

            elif link_type == 'anchor':
                # Handle anchor links (page sections)
                return f"#{link_target.lstrip('#')}"

            elif link_type == 'custom':
                # Handle custom URLs
                if link_target.startswith('http'):
                    return link_target
                elif link_target.startswith('/'):
                    return link_target
                else:
                    return f"/{link_target}"

        except Exception:
            # Fallback for any errors
            pass

        return '#'

    def get_cta_button_text(self, button_type='primary'):
        """Get the appropriate CTA button text"""
        if button_type == 'primary':
            return self.cta_label or "Shop Now"
        else:
            return self.secondary_cta_label or "Learn More"

    def has_secondary_button(self):
        """Check if secondary button should be displayed"""
        return bool(self.secondary_cta_label and self.secondary_link_type and self.secondary_link_target)

    def get_secondary_redirect_url(self):
        """Get secondary button redirect URL"""
        return self.get_redirect_url('secondary')


class FeaturedProduct(models.Model):
    """Model to mark products as featured for different sections"""
    SECTION_CHOICES = [
        ('hero', 'Hero Section'),
        ('featured', 'Featured Products'),
        ('best_selling', 'Best Selling'),
        ('new_arrivals', 'New Arrivals'),
        ('top_deals', 'Top Deals'),
    ]

    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='featured_in')
    section = models.CharField(max_length=20, choices=SECTION_CHOICES)
    display_order = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['section', 'display_order']
        unique_together = ['product', 'section']
        verbose_name = 'Featured Product'
        verbose_name_plural = 'Featured Products'

    def __str__(self):
        return f"{self.product.name} - {self.get_section_display()}"


class SiteConfiguration(models.Model):
    """Model for general site configuration"""
    # Hero section settings
    hero_auto_slide_interval = models.PositiveIntegerField(
        default=5000,
        help_text="Auto slide interval in milliseconds (5000 = 5 seconds)"
    )
    hero_show_arrows = models.BooleanField(default=True)
    hero_show_dots = models.BooleanField(default=True)

    # Product carousel settings
    products_per_row_desktop = models.PositiveIntegerField(default=4)
    products_per_row_tablet = models.PositiveIntegerField(default=2)
    products_per_row_mobile = models.PositiveIntegerField(default=1)

    # General settings
    site_name = models.CharField(max_length=100, default="iTechStore")
    site_tagline = models.CharField(max_length=200, default="Your Technology Destination")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Site Configuration'
        verbose_name_plural = 'Site Configuration'

    def __str__(self):
        return f"Site Configuration - {self.site_name}"

    def save(self, *args, **kwargs):
        # Ensure only one configuration exists
        if not self.pk and SiteConfiguration.objects.exists():
            raise ValueError("Only one site configuration is allowed")
        super().save(*args, **kwargs)
