{% extends 'admin/base_admin.html' %}

{% block title %}Products in {{ current_category.name }}{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mt-4 mb-4">
        <div>
            <h1>Products in Category: {{ current_category.name }}</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'admin_dashboard' %}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'admin_categories' %}">Categories</a></li>
                    <li class="breadcrumb-item active">{{ current_category.name }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{% url 'admin_product_add' %}?category={{ current_category.id }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add Product
            </a>
            <a href="{% url 'admin_products' %}" class="btn btn-outline-secondary ms-2">
                <i class="fas fa-list"></i> All Products
            </a>
        </div>
    </div>

    <!-- Category Details Card -->
    <div class="card mb-4">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                {% if current_category.image %}
                <img src="{{ current_category.image.url }}" alt="{{ current_category.name }}" class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                {% else %}
                <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                    <i class="fas fa-tag text-white"></i>
                </div>
                {% endif %}
                <div>
                    <h5 class="mb-0">{{ current_category.name }}</h5>
                    {% if current_category.description %}
                    <small class="text-muted">{{ current_category.description }}</small>
                    {% endif %}
                </div>
            </div>
            <div>
                <a href="{% url 'admin_category_edit' current_category.id %}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-edit"></i> Edit Category
                </a>
            </div>
        </div>
    </div>

    <!-- Products Table Card -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Products ({{ products.paginator.count }})
        </div>
        <div class="card-body">
            {% if products %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Image</th>
                            <th>Name</th>
                            <th>Price</th>
                            <th>Stock Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for product in products %}
                        <tr>
                            <td style="width: 80px;">
                                {% if product.image %}
                                <img src="{{ product.image.url }}" alt="{{ product.name }}" style="width: 50px; height: 50px; object-fit: cover;">
                                {% else %}
                                <div class="bg-light d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                    <i class="fas fa-box text-muted"></i>
                                </div>
                                {% endif %}
                            </td>
                            <td>{{ product.name }}</td>
                            <td>${{ product.price|default:"N/A" }}</td>
                            <td>
                                <span class="badge {% if product.stock_status == 'available' %}bg-success{% elif product.stock_status == 'preorder' %}bg-warning text-dark{% else %}bg-danger{% endif %}">
                                    {{ product.get_stock_status_display }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="{% url 'admin_product_edit' product.id %}?referring_category={{ current_category.id }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'admin_product_view' product.id %}" class="btn btn-sm btn-outline-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'admin_product_delete' product.id %}" class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if products.paginator.num_pages > 1 %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center mt-4">
                    {% if products.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ products.previous_page_number }}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% endif %}

                    {% for i in products.paginator.page_range %}
                    {% if products.number == i %}
                    <li class="page-item active"><a class="page-link" href="?page={{ i }}">{{ i }}</a></li>
                    {% elif i > products.number|add:"-3" and i < products.number|add:"3" %}
                    <li class="page-item"><a class="page-link" href="?page={{ i }}">{{ i }}</a></li>
                    {% endif %}
                    {% endfor %}

                    {% if products.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ products.next_page_number }}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ products.paginator.num_pages }}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                <h4>No products in this category</h4>
                <p class="text-muted">Get started by adding your first product to this category</p>
                <a href="{% url 'admin_product_add' %}?category={{ current_category.id }}" class="btn btn-primary mt-2">
                    <i class="fas fa-plus"></i> Add Product
                </a>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Other Categories -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-tags me-1"></i>
            Other Categories
        </div>
        <div class="card-body">
            <div class="row">
                {% for category in categories %}
                {% if category.id != current_category.id %}
                <div class="col-md-3 col-sm-6 mb-3">
                    <a href="{% url 'admin_products_by_category' category.id %}" class="text-decoration-none">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body d-flex align-items-center">
                                {% if category.image %}
                                <img src="{{ category.image.url }}" alt="{{ category.name }}" class="rounded-circle me-3" style="width: 40px; height: 40px; object-fit: cover;">
                                {% else %}
                                <div class="bg-light rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                    <i class="fas fa-tag text-muted"></i>
                                </div>
                                {% endif %}
                                <div>
                                    <h6 class="mb-0">{{ category.name }}</h6>
                                    <small class="text-muted">{{ category.products.count }} products</small>
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
                {% endif %}
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}
.btn-group .btn {
    padding: 0.25rem 0.5rem;
}
.table th {
    background-color: #f8f9fa;
    font-weight: 600;
}
.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}
</style>
{% endblock %} 