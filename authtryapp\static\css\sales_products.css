/* Sales Products Section Styles */

/* Sales Section Container */
.sales-section {
    margin: 60px 0;
    padding: 0 20px;
}

.sales-header {
    text-align: center;
    margin-bottom: 50px;
}

.sales-title {
    font-size: 36px;
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
}

.sales-subtitle {
    font-size: 18px;
    color: #666;
    font-weight: 400;
}

/* Sales Products Grid */
.sales-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 24px;
    max-width: 1400px;
    margin: 0 auto;
}

/* Sales Product Card */
.sales-product-card {
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    border: 1px solid #f0f0f0;
}

.sales-product-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.12);
    border-color: #e0e0e0;
}

/* Product Image Container */
.sales-product-image-container {
    position: relative;
    height: 240px;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fafafa;
    overflow: hidden;
}

.sales-product-image {
    max-height: 200px;
    max-width: 100%;
    width: auto;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.sales-product-card:hover .sales-product-image {
    transform: scale(1.05);
}

/* Product Badges */
.sales-product-badges {
    position: absolute;
    top: 12px;
    left: 12px;
    display: flex;
    flex-direction: column;
    gap: 6px;
    z-index: 2;
}

.badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.sponsored-badge {
    background: #ff6b35;
    color: white;
}

.gift-badge {
    background: #28a745;
    color: white;
}

.discount-badge {
    background: #dc3545;
    color: white;
}

/* Sale category badges */
.sale-category-badge {
    background: #dc3545 !important; /* Fallback color */
    color: white !important;
    border: none;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    animation: pulse-badge 2s infinite;
}

/* Pulse animation for sale badges */
@keyframes pulse-badge {
    0% {
        transform: scale(1);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
}

/* Specific badge type styling */
.sale-category-badge[style*="#dc3545"] {
    /* Red badges - percentage discounts */
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
}

.sale-category-badge[style*="#fd7e14"] {
    /* Orange badges - special offers */
    background: linear-gradient(135deg, #fd7e14 0%, #e8590c 100%) !important;
}

.sale-category-badge[style*="#28a745"] {
    /* Green badges - free items */
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
}

.sale-category-badge[style*="#6f42c1"] {
    /* Purple badges - buy x get y */
    background: linear-gradient(135deg, #6f42c1 0%, #59359a 100%) !important;
}

.sale-category-badge[style*="#e83e8c"] {
    /* Pink badges - flash sales */
    background: linear-gradient(135deg, #e83e8c 0%, #d91a72 100%) !important;
    animation: flash-pulse 1s infinite;
}

.sale-category-badge[style*="#007bff"] {
    /* Blue badges - fixed amount */
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

.sale-category-badge[style*="#17a2b8"] {
    /* Teal badges - free shipping */
    background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%) !important;
}

/* Flash sale animation */
@keyframes flash-pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.1);
    }
}

/* Enhanced savings display */
.savings-info {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-top: 4px;
}

.savings-amount {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.discount-percentage {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.price-row {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.original-price {
    text-decoration: line-through;
    color: #6c757d;
    font-size: 14px;
}

.current-price {
    font-weight: bold;
    color: #dc3545;
    font-size: 18px;
}

/* Product Information */
.sales-product-info {
    padding: 20px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    gap: 12px;
}

/* Title & Specs */
.sales-product-title-section {
    margin-bottom: 8px;
}

.sales-product-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 6px 0;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.sales-product-specs {
    font-size: 13px;
    color: #666;
    margin: 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Rating Section */
.sales-product-rating {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.rating-stars {
    display: flex;
    gap: 2px;
}

.rating-stars i {
    font-size: 14px;
    color: #ddd;
}

.rating-stars i.filled {
    color: #ffc107;
}

.rating-text {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.review-count {
    font-size: 13px;
    color: #666;
}

/* Price Section */
.sales-product-price {
    margin-bottom: 8px;
}

.price-row {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 4px;
}

.current-price {
    font-size: 20px;
    font-weight: 700;
    color: #dc3545;
}

.original-price {
    font-size: 16px;
    color: #999;
    text-decoration: line-through;
}

.savings-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.discount-percentage {
    background: #dc3545;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
}

.savings-amount {
    font-size: 13px;
    color: #28a745;
    font-weight: 600;
}

/* Gift Description */
.gift-description {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    font-size: 13px;
    color: #28a745;
    font-weight: 500;
}

.gift-description i {
    color: #28a745;
}

/* Availability Status */
.sales-product-availability {
    margin-bottom: 12px;
}

.availability-status {
    font-size: 13px;
    font-weight: 600;
    padding: 4px 0;
}

.availability-status.available {
    color: #28a745;
}

.availability-status.unavailable {
    color: #dc3545;
}

/* Action Buttons */
.sales-product-actions {
    display: flex;
    gap: 10px;
    margin-top: auto;
}

.add-to-cart-btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.add-to-cart-btn.primary {
    background: #007bff;
    color: white;
}

.add-to-cart-btn.primary:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.add-to-cart-btn.preorder {
    background: #ffc107;
    color: #212529;
}

.add-to-cart-btn.preorder:hover {
    background: #e0a800;
}

.add-to-cart-btn.disabled {
    background: #6c757d;
    color: white;
    cursor: not-allowed;
    opacity: 0.7;
}

.quick-view-btn {
    width: 44px;
    height: 44px;
    border: 2px solid #e9ecef;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    text-decoration: none;
}

.quick-view-btn:hover {
    border-color: #007bff;
    color: #007bff;
    background: #f8f9fa;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sales-products-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 16px;
        padding: 0 10px;
    }

    .sales-product-image-container {
        height: 200px;
        padding: 15px;
    }

    .sales-product-image {
        max-height: 170px;
    }

    .sales-product-info {
        padding: 16px;
    }

    .sales-title {
        font-size: 28px;
    }

    .sales-subtitle {
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .sales-products-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .sales-product-actions {
        flex-direction: column;
    }

    .quick-view-btn {
        width: 100%;
        height: 40px;
    }
}
