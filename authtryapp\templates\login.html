<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Login</title>
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: "Segoe UI", sans-serif;
      background: linear-gradient(to bottom, #0a0a23, #2929a3);
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100vh;
    }

    .login-card {
      background-color: #14143d;
      padding: 40px 30px;
      border-radius: 16px;
      width: 100%;
      max-width: 400px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      text-align: center;
      position: relative;
    }

    .login-icon {
      width: 80px;
      height: 80px;
      background: radial-gradient(circle, #2b2bc9 30%, #14143d 90%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 20px;
    }

    .login-icon img {
      width: 40px;
      height: 40px;
    }

    .login-card h2 {
      margin-bottom: 30px;
      font-size: 24px;
    }

    .form-group {
      text-align: left;
      margin-bottom: 20px;
    }

    .form-group label {
      display: block;
      font-size: 14px;
      margin-bottom: 6px;
    }

    .form-group input {
      width: 100%;
      padding: 12px;
      border-radius: 8px;
      border: none;
      font-size: 14px;
      background-color: #f1f1f1;
      color: #333;
    }

    .login-btn {
      margin-top: 10px;
      width: 100%;
      padding: 12px;
      background: linear-gradient(to right, #8e2de2, #4ac7ff);
      border: none;
      border-radius: 8px;
      color: white;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
    }

    .login-btn:hover {
      opacity: 0.9;
    }
  </style>
</head>
<body>
  <div class="login-card">
    <div class="login-icon">
      <img src="data:image/svg+xml;base64,PHN2ZyBmaWxsPSIjZmZmIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgdmlld0JveD0iMCAwIDUxMiA1MTIiPjxwYXRoIGQ9Ik0yNTYgMGMtMTQxLjQgMC0yNTYgMTE0LjYtMjU2IDI1NnMxMTQuNiAyNTYgMjU2IDI1NiAyNTYtMTE0LjYgMjU2LTI1Ni0xMTQuNi0yNTYtMjU2LTI1NnptMCA0OThjLTMyLjggMC02NC4zLTcuNS05Mi4zLTIwLjcgNS43OC0yOC40IDE3LjUtNTMuOCAzNS42LTc1LjQgMTkuNS0yMi45IDQ1LjUtNDEuMiA3NS40LTUyLjggMjYuOC0xMC4yIDU2LjItMTUuOCA4Ni4zLTE1LjggMzAuMSAwIDU5LjUgNS42IDg2LjMgMTUuOCAyOS45IDExLjYgNTUuOSAzMC4xIDc1LjQgNTIuOCAxOC4xIDIxLjYgMjkuOCA0NyAzNS42IDc1LjQtMjguMSAxMy4yLTU5LjUgMjAuNy05Mi4zIDIwLjctODQuNSAwLTE1Mi0xMy4zLTIwMy4xLTUwLjR6bTAgLTQzMmM1Ni40IDAgMTAyLjMgNDUuOSA2Mi4zIDQ1LjkgMCAzNS4xLTMyLjEgNjQtNzIgNjRzLTcyLTI4LjktNzItNjRjLTQwLjIgMC02Mi4zLTQ1LjkgNjIuMy00NS45eiIvPjwvc3ZnPg==" alt="User Icon" />
    </div>
    <h2>Login</h2>

    {% if messages %}
      {% for message in messages %}
        <div style="background-color: {% if message.tags == 'error' %}#ff5757{% else %}#4caf50{% endif %};
                    padding: 10px;
                    border-radius: 8px;
                    margin-bottom: 20px;">
          {{ message }}
        </div>
      {% endfor %}
    {% endif %}

    {% if form.errors %}
      <div style="background-color: #ff5757;
                  padding: 10px;
                  border-radius: 8px;
                  margin-bottom: 20px;">
        Invalid username or password. Please try again.
      </div>
    {% endif %}

    <form method="post" action="{% url 'login' %}">
      {% csrf_token %}
      <div class="form-group">
        <label>Username</label>
        <input type="text" name="username" placeholder="Enter your username" required />
      </div>
      <div class="form-group">
        <label>Password</label>
        <input type="password" name="password" placeholder="Enter your password" required />
      </div>
      <button type="submit" class="login-btn">Login</button>

      <div style="margin-top: 20px; font-size: 14px;">
        <p>Don't have an account? <a href="{% url 'register' %}" style="color: #4ac7ff; text-decoration: none;">Register here</a></p>
      </div>
    </form>
  </div>
</body>
</html>
