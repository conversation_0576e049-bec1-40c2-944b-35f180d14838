// UI Enhancements for iTechStore
document.addEventListener('DOMContentLoaded', function() {
    // Product Card Hover Effects
    const productCards = document.querySelectorAll('.product-card');
    
    productCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            // Add subtle shadow pulse animation
            this.style.animation = 'shadow-pulse 2s infinite';
            
            // Enhance the stock status label
            const statusLabel = this.querySelector('.stock-status');
            if (statusLabel) {
                statusLabel.style.transform = 'translateY(-5px)';
                statusLabel.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.2)';
            }
        });
        
        card.addEventListener('mouseleave', function() {
            // Remove animations
            this.style.animation = '';
            
            // Reset the stock status label
            const statusLabel = this.querySelector('.stock-status');
            if (statusLabel) {
                statusLabel.style.transform = '';
                statusLabel.style.boxShadow = '';
            }
        });
    });
    
    // Smooth Dropdown Animation
    const dropdowns = document.querySelectorAll('.dropdown');
    
    dropdowns.forEach(dropdown => {
        const dropdownContent = dropdown.querySelector('.dropdown-content');
        
        // Add transition delay to each dropdown item for cascade effect
        if (dropdownContent) {
            const links = dropdownContent.querySelectorAll('a');
            links.forEach((link, index) => {
                link.style.transitionDelay = `${index * 50}ms`;
                
                // Add hover effect
                link.addEventListener('mouseenter', function() {
                    this.style.paddingLeft = '25px';
                });
                
                link.addEventListener('mouseleave', function() {
                    this.style.paddingLeft = '20px';
                });
            });
        }
    });
    
    // Enhanced Navbar Transparency Effect
    const mainNav = document.querySelector('.main-nav');
    
    if (mainNav) {
        // Initial state based on scroll position
        if (window.scrollY > 50) {
            mainNav.style.backgroundColor = 'rgba(255, 255, 255, 1)';
        } else {
            mainNav.style.backgroundColor = 'rgba(255, 255, 255, 0.95)';
        }
        
        // Add glass morphism effect on hover
        mainNav.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'rgba(255, 255, 255, 1)';
            this.style.backdropFilter = 'blur(15px)';
        });
        
        mainNav.addEventListener('mouseleave', function() {
            if (window.scrollY <= 50) {
                this.style.backgroundColor = 'rgba(255, 255, 255, 0.95)';
            }
            this.style.backdropFilter = 'blur(10px)';
        });
    }
    
    // Add smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            const targetId = this.getAttribute('href');
            if (targetId !== '#') {
                e.preventDefault();
                
                document.querySelector(targetId).scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // Add CSS animation classes
    const style = document.createElement('style');
    style.textContent = `
        @keyframes shadow-pulse {
            0% {
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            }
            50% {
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            }
            100% {
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            }
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes slideIn {
            from { transform: translateX(-20px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    `;
    document.head.appendChild(style);
    
    // Enhanced Mobile Menu Dropdowns
    const mobileDropdowns = document.querySelectorAll('.mobile-dropdown > a');
    
    mobileDropdowns.forEach(dropdown => {
        dropdown.addEventListener('click', function(e) {
            e.preventDefault();
            
            const parent = this.parentElement;
            const dropdownContent = parent.querySelector('.mobile-dropdown-content');
            const icon = this.querySelector('i');
            
            // Toggle active class
            parent.classList.toggle('active');
            
            // Rotate icon
            if (icon) {
                icon.style.transition = 'transform 0.3s ease';
                if (parent.classList.contains('active')) {
                    icon.style.transform = 'rotate(180deg)';
                } else {
                    icon.style.transform = 'rotate(0)';
                }
            }
        });
    });
});
