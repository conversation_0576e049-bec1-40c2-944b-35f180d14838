// Hero Banner Admin Enhancements
document.addEventListener('DOMContentLoaded', function() {
    
    // Link type change handler
    const linkTypeField = document.querySelector('#id_link_type');
    const linkTargetField = document.querySelector('#id_link_target');
    const linkTargetHelp = document.querySelector('.field-link_target .help');
    
    if (linkTypeField && linkTargetField) {
        function updateLinkTargetHelp() {
            const linkType = linkTypeField.value;
            let helpText = '';
            let placeholder = '';
            
            switch(linkType) {
                case 'product':
                    helpText = 'Enter product slug (e.g., "dell-monitor-s2425h") or product ID';
                    placeholder = 'dell-monitor-s2425h';
                    break;
                case 'category':
                    helpText = 'Enter category slug (e.g., "gaming-laptops") or category ID';
                    placeholder = 'gaming-laptops';
                    break;
                case 'anchor':
                    helpText = 'Enter page section ID (e.g., "featured-products")';
                    placeholder = 'featured-products';
                    break;
                case 'custom':
                    helpText = 'Enter full URL (e.g., "https://example.com") or relative path';
                    placeholder = 'https://example.com';
                    break;
                default:
                    helpText = 'Select a link type first';
                    placeholder = '';
            }
            
            if (linkTargetHelp) {
                linkTargetHelp.textContent = helpText;
            }
            linkTargetField.placeholder = placeholder;
        }
        
        linkTypeField.addEventListener('change', updateLinkTargetHelp);
        updateLinkTargetHelp(); // Initialize
    }
    
    // Secondary link type handler
    const secondaryLinkTypeField = document.querySelector('#id_secondary_link_type');
    const secondaryLinkTargetField = document.querySelector('#id_secondary_link_target');
    
    if (secondaryLinkTypeField && secondaryLinkTargetField) {
        function updateSecondaryLinkTargetHelp() {
            const linkType = secondaryLinkTypeField.value;
            let placeholder = '';
            
            switch(linkType) {
                case 'product':
                    placeholder = 'product-slug-or-id';
                    break;
                case 'category':
                    placeholder = 'category-slug-or-id';
                    break;
                case 'anchor':
                    placeholder = 'section-id';
                    break;
                case 'custom':
                    placeholder = 'https://example.com';
                    break;
            }
            
            secondaryLinkTargetField.placeholder = placeholder;
        }
        
        secondaryLinkTypeField.addEventListener('change', updateSecondaryLinkTargetHelp);
        updateSecondaryLinkTargetHelp();
    }
    
    // Image dimension unit synchronization
    const widthField = document.querySelector('#id_image_width');
    const heightField = document.querySelector('#id_image_height');
    const widthUnitField = document.querySelector('#id_width_unit');
    const heightUnitField = document.querySelector('#id_height_unit');
    
    if (widthUnitField && heightUnitField) {
        // Sync units when one changes
        widthUnitField.addEventListener('change', function() {
            if (this.value === '%' && heightUnitField.value === 'px') {
                heightUnitField.value = '%';
            }
        });
        
        heightUnitField.addEventListener('change', function() {
            if (this.value === '%' && widthUnitField.value === 'px') {
                widthUnitField.value = '%';
            }
        });
    }
    
    // Dimension validation
    if (widthField && heightField) {
        function validateDimensions() {
            const width = parseInt(widthField.value);
            const height = parseInt(heightField.value);
            const widthUnit = widthUnitField ? widthUnitField.value : 'px';
            const heightUnit = heightUnitField ? heightUnitField.value : 'px';
            
            // Validate percentage values
            if (widthUnit === '%' && width > 100) {
                widthField.style.borderColor = '#dc3545';
                widthField.title = 'Percentage values should be 100 or less';
            } else if (width < 0) {
                widthField.style.borderColor = '#dc3545';
                widthField.title = 'Width must be positive';
            } else {
                widthField.style.borderColor = '#28a745';
                widthField.title = '';
            }
            
            if (heightUnit === '%' && height > 100) {
                heightField.style.borderColor = '#dc3545';
                heightField.title = 'Percentage values should be 100 or less';
            } else if (height < 0) {
                heightField.style.borderColor = '#dc3545';
                heightField.title = 'Height must be positive';
            } else {
                heightField.style.borderColor = '#28a745';
                heightField.title = '';
            }
        }
        
        widthField.addEventListener('input', validateDimensions);
        heightField.addEventListener('input', validateDimensions);
        if (widthUnitField) widthUnitField.addEventListener('change', validateDimensions);
        if (heightUnitField) heightUnitField.addEventListener('change', validateDimensions);
    }
    
    // Order field auto-increment
    const orderField = document.querySelector('#id_order');
    if (orderField && !orderField.value) {
        // Auto-suggest next order number
        const existingOrders = Array.from(document.querySelectorAll('.result_list .field-order'))
            .map(el => parseInt(el.textContent) || 0);
        const maxOrder = Math.max(0, ...existingOrders);
        orderField.value = maxOrder + 1;
        orderField.style.background = '#d4edda';
    }
    
    // CTA label suggestions
    const ctaLabelField = document.querySelector('#id_cta_label');
    if (ctaLabelField && linkTypeField) {
        function suggestCTALabel() {
            if (ctaLabelField.value === 'Shop Now' || !ctaLabelField.value) {
                const linkType = linkTypeField.value;
                let suggestion = '';
                
                switch(linkType) {
                    case 'product':
                        suggestion = 'Buy Now';
                        break;
                    case 'category':
                        suggestion = 'Shop Category';
                        break;
                    case 'anchor':
                        suggestion = 'View Section';
                        break;
                    case 'custom':
                        suggestion = 'Visit Link';
                        break;
                }
                
                if (suggestion) {
                    ctaLabelField.value = suggestion;
                    ctaLabelField.style.background = '#fff3cd';
                    setTimeout(() => {
                        ctaLabelField.style.background = '';
                    }, 2000);
                }
            }
        }
        
        linkTypeField.addEventListener('change', suggestCTALabel);
    }
    
    // Add visual indicators for link types in list view
    const linkTypeCells = document.querySelectorAll('.result_list .field-link_type');
    linkTypeCells.forEach(cell => {
        const linkType = cell.textContent.trim().toLowerCase();
        cell.setAttribute('data-type', linkType);
    });
    
    console.log('Hero Banner Admin enhancements loaded');
});
