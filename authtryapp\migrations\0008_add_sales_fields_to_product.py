# Generated by Django 5.0.6 on 2025-05-26 03:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('authtryapp', '0007_hero_banner_flexible_redirects'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='gift_description',
            field=models.CharField(blank=True, default='', help_text="Description of gift (e.g., 'Free headphones')", max_length=200),
        ),
        migrations.AddField(
            model_name='product',
            name='has_gift',
            field=models.BooleanField(default=False, help_text='Gift with purchase available'),
        ),
        migrations.AddField(
            model_name='product',
            name='is_sponsored',
            field=models.BooleanField(default=False, help_text='Show sponsored tag'),
        ),
        migrations.AddField(
            model_name='product',
            name='original_price',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Original price before discount', max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='rating',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='Average rating (0.0 to 5.0)', max_digits=3),
        ),
        migrations.AddField(
            model_name='product',
            name='review_count',
            field=models.PositiveIntegerField(default=0, help_text='Number of reviews'),
        ),
        migrations.AddField(
            model_name='product',
            name='short_specs',
            field=models.CharField(blank=True, default='', max_length=300),
        ),
    ]
