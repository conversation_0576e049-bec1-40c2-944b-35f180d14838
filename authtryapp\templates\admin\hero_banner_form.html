{% extends 'admin/base_admin.html' %}

{% block title %}{{ action }} Hero Banner{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mt-4 mb-4">
        <h1>{{ action }} Hero Banner</h1>
        <a href="{% url 'admin_hero_banners' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Banners
        </a>
    </div>

    <div class="row">
        <!-- Form Section -->
        <div class="col-lg-8">
            <form method="post" enctype="multipart/form-data" id="bannerForm">
                {% csrf_token %}

                <!-- Banner Content -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-edit"></i> Banner Content
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="title" class="form-label">Title *</label>
                                <textarea class="form-control" id="title" name="title" rows="2" required placeholder="AMD Radeon™ RX 9070 XT GPU">{% if banner %}{{ banner.title }}{% endif %}</textarea>
                                <small class="form-text text-muted">Main headline (supports line breaks)</small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="brand_text" class="form-label">Brand Text</label>
                                <input type="text" class="form-control" id="brand_text" name="brand_text" value="{% if banner %}{{ banner.brand_text }}{% endif %}" placeholder="SAPPHIRE PULSE">
                                <small class="form-text text-muted">Small brand label (optional)</small>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">Description *</label>
                            <textarea class="form-control" id="description" name="description" rows="2" required placeholder="Experience next-generation gaming with cutting-edge performance...">{% if banner %}{{ banner.description }}{% endif %}</textarea>
                            <small class="form-text text-muted">Description text</small>
                        </div>
                    </div>
                </div>

                <!-- Button Configuration -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-mouse-pointer"></i> Button Configuration
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="cta_label" class="form-label">Primary Button Text</label>
                                <input type="text" class="form-control" id="cta_label" name="cta_label" value="{% if banner %}{{ banner.cta_label }}{% else %}Shop Now{% endif %}" placeholder="Shop Now">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="link_type" class="form-label">Link Type</label>
                                <select class="form-select" id="link_type" name="link_type">
                                    <option value="custom" {% if not banner or banner.link_type == 'custom' %}selected{% endif %}>Custom URL</option>
                                    <option value="product" {% if banner and banner.link_type == 'product' %}selected{% endif %}>Product Page</option>
                                    <option value="category" {% if banner and banner.link_type == 'category' %}selected{% endif %}>Category Page</option>
                                    <option value="anchor" {% if banner and banner.link_type == 'anchor' %}selected{% endif %}>Page Section</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="link_target" class="form-label">Link Target</label>
                                <input type="text" class="form-control" id="link_target" name="link_target" value="{% if banner %}{{ banner.link_target }}{% else %}#featured-products{% endif %}" placeholder="#featured-products">
                                <small class="form-text text-muted">URL, slug, or anchor based on type</small>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="secondary_cta_label" class="form-label">Secondary Button Text</label>
                                <input type="text" class="form-control" id="secondary_cta_label" name="secondary_cta_label" value="{% if banner %}{{ banner.secondary_cta_label }}{% endif %}" placeholder="Learn More">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="secondary_link_type" class="form-label">Secondary Link Type</label>
                                <select class="form-select" id="secondary_link_type" name="secondary_link_type">
                                    <option value="">None</option>
                                    <option value="custom" {% if banner and banner.secondary_link_type == 'custom' %}selected{% endif %}>Custom URL</option>
                                    <option value="product" {% if banner and banner.secondary_link_type == 'product' %}selected{% endif %}>Product Page</option>
                                    <option value="category" {% if banner and banner.secondary_link_type == 'category' %}selected{% endif %}>Category Page</option>
                                    <option value="anchor" {% if banner and banner.secondary_link_type == 'anchor' %}selected{% endif %}>Page Section</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="secondary_link_target" class="form-label">Secondary Link Target</label>
                                <input type="text" class="form-control" id="secondary_link_target" name="secondary_link_target" value="{% if banner %}{{ banner.secondary_link_target }}{% endif %}" placeholder="#">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Visual Design -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-palette"></i> Visual Design
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="background_gradient_start" class="form-label">Gradient Start Color</label>
                                <div class="input-group">
                                    <input type="color" class="form-control form-control-color" id="background_gradient_start" name="background_gradient_start" value="{% if banner %}{{ banner.background_gradient_start }}{% else %}#8B5CF6{% endif %}">
                                    <input type="text" class="form-control" id="gradient_start_text" value="{% if banner %}{{ banner.background_gradient_start }}{% else %}#8B5CF6{% endif %}">
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="background_gradient_end" class="form-label">Gradient End Color</label>
                                <div class="input-group">
                                    <input type="color" class="form-control form-control-color" id="background_gradient_end" name="background_gradient_end" value="{% if banner %}{{ banner.background_gradient_end }}{% else %}#EC4899{% endif %}">
                                    <input type="text" class="form-control" id="gradient_end_text" value="{% if banner %}{{ banner.background_gradient_end }}{% else %}#EC4899{% endif %}">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="image" class="form-label">Banner Image</label>
                                <input type="file" class="form-control" id="image" name="image" accept="image/*">
                                <small class="form-text text-muted">Recommended: 800x600px, max 2MB</small>
                                {% if banner.image %}
                                <div class="mt-2">
                                    <img src="{{ banner.image.url }}" alt="Current image" class="img-thumbnail" style="max-height: 100px;">
                                    <small class="d-block text-muted">Current image</small>
                                </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="image_alt" class="form-label">Image Alt Text</label>
                                <input type="text" class="form-control" id="image_alt" name="image_alt" value="{% if banner %}{{ banner.image_alt }}{% endif %}" placeholder="AMD GPU">
                                <small class="form-text text-muted">For accessibility</small>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="image_width" class="form-label">Image Width</label>
                                <input type="number" class="form-control" id="image_width" name="image_width" value="{% if banner.image_width %}{{ banner.image_width }}{% endif %}" placeholder="800">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="width_unit" class="form-label">Width Unit</label>
                                <select class="form-select" id="width_unit" name="width_unit">
                                    <option value="px" {% if not banner or banner.width_unit == 'px' %}selected{% endif %}>Pixels (px)</option>
                                    <option value="%" {% if banner and banner.width_unit == '%' %}selected{% endif %}>Percentage (%)</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="image_height" class="form-label">Image Height</label>
                                <input type="number" class="form-control" id="image_height" name="image_height" value="{% if banner.image_height %}{{ banner.image_height }}{% endif %}" placeholder="600">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="height_unit" class="form-label">Height Unit</label>
                                <select class="form-select" id="height_unit" name="height_unit">
                                    <option value="px" {% if not banner or banner.height_unit == 'px' %}selected{% endif %}>Pixels (px)</option>
                                    <option value="%" {% if banner and banner.height_unit == '%' %}selected{% endif %}>Percentage (%)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Display Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-cog"></i> Display Settings
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="order" class="form-label">Display Order</label>
                                <input type="number" class="form-control" id="order" name="order" value="{% if banner %}{{ banner.order }}{% else %}0{% endif %}" min="0">
                                <small class="form-text text-muted">Lower numbers appear first</small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch mt-4">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" {% if not banner or banner.is_active %}checked{% endif %}>
                                    <label class="form-check-label" for="is_active">
                                        Active (show on website)
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'admin_hero_banners' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> {{ action }} Banner
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Preview Section -->
        <div class="col-lg-4">
            <div class="card sticky-top">
                <div class="card-header">
                    <i class="fas fa-eye"></i> Live Preview
                </div>
                <div class="card-body p-0">
                    <div id="bannerPreview" class="position-relative" style="height: 300px; background: linear-gradient(135deg, #8B5CF6 0%, #EC4899 100%);">
                        <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center text-white p-4">
                            <div>
                                <small id="previewBrand" class="d-block opacity-75 mb-1">SAPPHIRE PULSE</small>
                                <h5 id="previewTitle" class="mb-2">AMD Radeon™ RX 9070 XT GPU</h5>
                                <p id="previewSubtitle" class="small mb-3">Experience next-generation gaming...</p>
                                <div class="d-flex gap-2">
                                    <span id="previewPrimaryBtn" class="badge bg-light text-dark px-3 py-2">AVAILABLE NOW</span>
                                    <span id="previewSecondaryBtn" class="badge bg-outline-light text-white px-3 py-2">LEARN MORE</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="p-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i> Preview updates as you type
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Live preview functionality
    const titleInput = document.getElementById('title');
    const brandInput = document.getElementById('brand_text');
    const descriptionInput = document.getElementById('description');
    const primaryBtnInput = document.getElementById('cta_label');
    const secondaryBtnInput = document.getElementById('secondary_cta_label');
    const gradientStartInput = document.getElementById('background_gradient_start');
    const gradientEndInput = document.getElementById('background_gradient_end');
    const gradientStartText = document.getElementById('gradient_start_text');
    const gradientEndText = document.getElementById('gradient_end_text');

    const previewTitle = document.getElementById('previewTitle');
    const previewBrand = document.getElementById('previewBrand');
    const previewSubtitle = document.getElementById('previewSubtitle');
    const previewPrimaryBtn = document.getElementById('previewPrimaryBtn');
    const previewSecondaryBtn = document.getElementById('previewSecondaryBtn');
    const bannerPreview = document.getElementById('bannerPreview');

    function updatePreview() {
        previewTitle.textContent = titleInput.value || 'Banner Title';
        previewBrand.textContent = brandInput.value || '';
        previewBrand.style.display = brandInput.value ? 'block' : 'none';
        previewSubtitle.textContent = descriptionInput.value || 'Banner description...';
        previewPrimaryBtn.textContent = primaryBtnInput.value || 'PRIMARY';
        previewSecondaryBtn.textContent = secondaryBtnInput.value || 'SECONDARY';

        const startColor = gradientStartInput.value;
        const endColor = gradientEndInput.value;
        bannerPreview.style.background = `linear-gradient(135deg, ${startColor} 0%, ${endColor} 100%)`;
    }

    // Color picker sync
    gradientStartInput.addEventListener('input', function() {
        gradientStartText.value = this.value;
        updatePreview();
    });

    gradientEndInput.addEventListener('input', function() {
        gradientEndText.value = this.value;
        updatePreview();
    });

    gradientStartText.addEventListener('input', function() {
        gradientStartInput.value = this.value;
        updatePreview();
    });

    gradientEndText.addEventListener('input', function() {
        gradientEndInput.value = this.value;
        updatePreview();
    });

    // Text input listeners
    [titleInput, brandInput, descriptionInput, primaryBtnInput, secondaryBtnInput].forEach(input => {
        input.addEventListener('input', updatePreview);
    });

    // Initial preview update
    updatePreview();

    // Form validation and enhancement
    const form = document.getElementById('bannerForm');
    const submitBtn = form.querySelector('button[type="submit"]');

    // Add loading state to submit button
    form.addEventListener('submit', function(e) {
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
        submitBtn.disabled = true;
    });

    // Image upload preview
    const imageInput = document.getElementById('image');
    const imagePreview = document.createElement('div');
    imagePreview.className = 'mt-2';
    imageInput.parentNode.appendChild(imagePreview);

    imageInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                imagePreview.innerHTML = `
                    <img src="${e.target.result}" alt="Preview" class="img-thumbnail" style="max-height: 100px;">
                    <small class="d-block text-muted">New image preview</small>
                `;
            };
            reader.readAsDataURL(file);
        } else {
            imagePreview.innerHTML = '';
        }
    });

    // Auto-save draft functionality (optional)
    let autoSaveTimeout;
    const autoSaveInputs = [titleInput, brandInput, descriptionInput, primaryBtnInput, secondaryBtnInput];

    autoSaveInputs.forEach(input => {
        input.addEventListener('input', function() {
            clearTimeout(autoSaveTimeout);
            autoSaveTimeout = setTimeout(() => {
                // Save to localStorage as draft
                const draftData = {
                    title: titleInput.value,
                    brand_text: brandInput.value,
                    description: descriptionInput.value,
                    cta_label: primaryBtnInput.value,
                    secondary_cta_label: secondaryBtnInput.value,
                    background_gradient_start: gradientStartInput.value,
                    background_gradient_end: gradientEndInput.value
                };
                localStorage.setItem('hero_banner_draft', JSON.stringify(draftData));

                // Show draft saved indicator
                const draftIndicator = document.getElementById('draftIndicator') || document.createElement('small');
                draftIndicator.id = 'draftIndicator';
                draftIndicator.className = 'text-success';
                draftIndicator.innerHTML = '<i class="fas fa-check"></i> Draft saved';
                submitBtn.parentNode.appendChild(draftIndicator);

                setTimeout(() => {
                    if (draftIndicator.parentNode) {
                        draftIndicator.remove();
                    }
                }, 2000);
            }, 1000);
        });
    });

    // Load draft on page load
    const savedDraft = localStorage.getItem('hero_banner_draft');
    if (savedDraft && !titleInput.value) {
        const draftData = JSON.parse(savedDraft);
        Object.keys(draftData).forEach(key => {
            const input = document.querySelector(`[name="${key}"]`);
            if (input && !input.value) {
                input.value = draftData[key];
            }
        });
        updatePreview();
    }

    // Clear draft on successful submit
    form.addEventListener('submit', function() {
        localStorage.removeItem('hero_banner_draft');
    });
});
</script>
{% endblock %}
