{% extends 'base_new.html' %}
{% load static %}

{% block title %}{{ brand.name }} Products{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/product_card.css' %}">
<style>
    :root {
        --primary-color: #2563eb;
        --primary-hover: #1d4ed8;
        --secondary-color: #f0f9ff;
        --accent-color: #0284c7;
        --text-dark: #1e293b;
        --text-light: #64748b;
        --bg-light: #f8fafc;
        --bg-white: #ffffff;
        --shadow-sm: 0 1px 2px rgba(0,0,0,0.05);
        --shadow-md: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06);
        --shadow-lg: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05);
        --radius-sm: 0.375rem;
        --radius-md: 0.5rem;
        --radius-lg: 0.75rem;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --danger-color: #ef4444;
    }

    .brand-header {
        background: linear-gradient(135deg, var(--secondary-color) 0%, #e0f2fe 100%);
        padding: 50px 0 30px;
        margin-bottom: 40px;
        border-bottom: 1px solid #e2e8f0;
    }

    .breadcrumb {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 30px;
        font-size: 0.875rem;
    }

    .breadcrumb-item {
        color: var(--text-light);
    }

    .breadcrumb-item a {
        color: var(--accent-color);
        text-decoration: none;
        transition: color 0.2s;
    }

    .breadcrumb-item a:hover {
        color: var(--primary-color);
    }

    .breadcrumb-item.active {
        color: var(--text-dark);
        font-weight: 600;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: "/";
        color: #cbd5e1;
        margin-right: 8px;
    }

    .brand-hero {
        display: flex;
        align-items: center;
        gap: 40px;
        margin-bottom: 40px;
        background: var(--bg-white);
        border-radius: var(--radius-lg);
        padding: 30px;
        box-shadow: var(--shadow-md);
    }

    .brand-logo {
        flex: 0 0 220px;
        height: 140px;
        background: var(--bg-white);
        border-radius: var(--radius-md);
        padding: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: var(--shadow-sm);
        border: 1px solid #f1f5f9;
    }

    .brand-logo img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }

    .brand-info {
        flex: 1;
    }

    .brand-title {
        font-size: 2.25rem;
        font-weight: 700;
        margin-bottom: 12px;
        color: var(--text-dark);
        letter-spacing: -0.02em;
    }

    .brand-description {
        color: var(--text-light);
        margin-bottom: 20px;
        max-width: 800px;
        line-height: 1.6;
        font-size: 1.05rem;
    }

    .product-count {
        background-color: #f1f5f9;
        display: inline-block;
        padding: 6px 16px;
        border-radius: 30px;
        font-size: 14px;
        font-weight: 600;
        color: var(--text-dark);
        box-shadow: var(--shadow-sm);
        border: 1px solid #e2e8f0;
    }

    .filter-sort-options {
        display: flex;
        gap: 20px;
        background: var(--bg-white);
        border-radius: var(--radius-lg);
        padding: 20px;
        box-shadow: var(--shadow-md);
        margin-bottom: 40px;
        flex-wrap: wrap;
        border: 1px solid #f1f5f9;
    }

    .filter-group, .sort-group {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .filter-group label, .sort-group label {
        font-weight: 600;
        color: var(--text-dark);
        font-size: 0.9rem;
    }

    .form-control {
        border-radius: var(--radius-md);
        border: 1px solid #e2e8f0;
        padding: 10px 16px;
        font-size: 14px;
        color: var(--text-dark);
        background-color: var(--bg-light);
        transition: all 0.2s;
        min-width: 180px;
        appearance: none;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%2364748b' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 12px center;
        background-size: 16px;
        padding-right: 40px;
    }

    .form-control:focus {
        border-color: var(--primary-color);
        outline: none;
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
    }

    .product-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 30px;
        margin-bottom: 60px;
    }

    .product-card {
        background: var(--bg-white);
        border-radius: var(--radius-lg);
        overflow: hidden;
        box-shadow: var(--shadow-md);
        transition: all 0.3s ease;
        position: relative;
        display: flex;
        flex-direction: column;
        height: 100%;
        padding: 0;
        border: 1px solid #f1f5f9;
    }

    .product-card:hover {
        transform: translateY(-8px);
        box-shadow: var(--shadow-lg);
        border-color: #e2e8f0;
    }

    .product-image {
        height: 220px;
        padding: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--bg-white);
        transition: background 0.3s ease;
    }

    .product-card:hover .product-image {
        background: var(--secondary-color);
    }

    .product-image img {
        max-height: 180px;
        width: auto;
        max-width: 100%;
        object-fit: contain;
        transition: transform 0.3s ease;
    }

    .product-card:hover .product-image img {
        transform: scale(1.05);
    }

    .product-info {
        padding: 22px 25px 25px;
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        background: linear-gradient(0deg, #f8fafc 0%, var(--bg-white) 100%);
    }

    .product-brand {
        font-size: 13px;
        color: var(--accent-color);
        text-transform: uppercase;
        font-weight: 600;
        margin-bottom: 8px;
        letter-spacing: 0.05em;
    }

    .product-info h3 {
        font-size: 17px;
        font-weight: 600;
        margin-bottom: 12px;
        color: var(--text-dark);
        line-height: 1.4;
        min-height: 48px;
    }

    .product-info h3 a {
        background-image: linear-gradient(transparent 95%, currentColor 5%);
        background-repeat: no-repeat;
        background-size: 0% 100%;
        transition: background-size 0.3s;
    }

    .product-info h3 a:hover {
        background-size: 100% 100%;
    }

    .starting-from {
        font-size: 12px;
        color: var(--text-light);
        margin-bottom: 4px;
    }

    .price {
        font-size: 20px;
        font-weight: 700;
        color: var(--text-dark);
        margin-bottom: 20px;
    }

    .stock-status {
        position: absolute;
        top: 15px;
        right: 15px;
        padding: 6px 12px;
        font-size: 12px;
        font-weight: 600;
        border-radius: var(--radius-md);
        text-transform: capitalize;
        letter-spacing: 0.03em;
        box-shadow: var(--shadow-sm);
    }

    .stock-status.available {
        background: var(--success-color);
        color: white;
    }

    .stock-status.preorder {
        background: var(--warning-color);
        color: #1e293b;
    }

    .stock-status.notavailable {
        background: var(--danger-color);
        color: white;
    }

    .product-buttons {
        display: flex;
        gap: 12px;
        margin-top: auto;
    }

    .add-to-cart {
        flex: 1;
        background: var(--primary-color);
        color: white;
        border: none;
        padding: 12px 0;
        border-radius: var(--radius-md);
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 0.03em;
    }

    .add-to-cart:hover {
        background: var(--primary-hover);
        box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
    }

    .add-to-cart.disabled {
        background: #94a3b8;
        cursor: not-allowed;
        opacity: 0.8;
    }

    .preorder-btn {
        background: var(--warning-color);
        color: #1e293b;
    }

    .preorder-btn:hover {
        background: #f59e0b;
        box-shadow: 0 4px 12px rgba(245, 158, 11, 0.2);
    }

    .view-cart-btn {
        width: 44px;
        height: 44px;
        border: 1px solid #e2e8f0;
        background: var(--bg-white);
        color: var(--accent-color);
        border-radius: var(--radius-md);
        display: flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        transition: all 0.2s;
        font-size: 18px;
    }

    .view-cart-btn:hover {
        background: var(--secondary-color);
        color: var(--primary-color);
        border-color: #bfdbfe;
    }

    .no-products {
        grid-column: 1 / -1;
        text-align: center;
        padding: 60px 40px;
        background-color: var(--bg-light);
        border-radius: var(--radius-lg);
        border: 1px dashed #cbd5e1;
    }

    .no-products p {
        margin-bottom: 25px;
        color: var(--text-dark);
        font-size: 1.1rem;
    }

    .no-products .btn {
        display: inline-block;
        padding: 12px 24px;
        border-radius: var(--radius-md);
        text-decoration: none;
        font-weight: 600;
        transition: all 0.2s;
        margin: 0 10px;
    }

    .no-products .btn-primary {
        background-color: var(--primary-color);
        color: white;
    }

    .no-products .btn-primary:hover {
        background-color: var(--primary-hover);
        box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
    }

    .no-products .btn-outline-primary {
        background-color: transparent;
        color: var(--primary-color);
        border: 1px solid var(--primary-color);
    }

    .no-products .btn-outline-primary:hover {
        background-color: var(--secondary-color);
    }

    /* Navigation dots */
    .product-nav-dots {
        display: flex;
        justify-content: center;
        gap: 8px;
        margin-top: 50px;
        margin-bottom: 30px;
    }

    .nav-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: #e2e8f0;
        cursor: pointer;
        transition: all 0.2s;
        border: 2px solid transparent;
    }

    .nav-dot.active {
        background-color: var(--primary-color);
        transform: scale(1.2);
    }

    .nav-dot:hover:not(.active) {
        background-color: #cbd5e1;
        border-color: var(--primary-color);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
function updateFilters() {
    const stockFilter = document.getElementById('stock-filter').value;
    const sortOption = document.getElementById('sort-options').value;

    // Get current URL and parameters
    const url = new URL(window.location.href);

    // Update or add parameters
    if (stockFilter) {
        url.searchParams.set('stock', stockFilter);
    } else {
        url.searchParams.delete('stock');
    }

    if (sortOption) {
        url.searchParams.set('sort', sortOption);
    } else {
        url.searchParams.delete('sort');
    }

    // Navigate to new URL
    window.location.href = url.toString();
}
</script>
{% endblock %}

{% block content %}
<section class="brand-header">
    <div class="container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'home' %}">Home</a></li>
                <li class="breadcrumb-item"><a href="{% url 'brand_list' %}">Brands</a></li>
                <li class="breadcrumb-item active">{{ brand.name }}</li>
            </ol>
        </nav>

        <div class="brand-hero">
            <div class="brand-logo">
                {% if brand.logo %}
                <img src="{{ brand.logo.url }}" alt="{{ brand.name }}">
                {% else %}
                <img src="https://via.placeholder.com/200x120?text={{ brand.name }}" alt="{{ brand.name }}">
                {% endif %}
            </div>

            <div class="brand-info">
                <h1 class="brand-title">{{ brand.name }}</h1>
                {% if brand.description %}
                <p class="brand-description">{{ brand.description }}</p>
                {% else %}
                <p class="brand-description">Explore the complete range of {{ brand.name }} products available at our store. From the latest releases to exclusive deals, find everything {{ brand.name }} has to offer.</p>
                {% endif %}
                <div class="product-count">{{ products.count }} Products</div>
            </div>
        </div>

        <!-- Filter and Sort Options -->
        <div class="filter-sort-options">
            <div class="filter-group">
                <label for="stock-filter">Filter by:</label>
                <select id="stock-filter" class="form-control" onchange="updateFilters()">
                    <option value="">All Products</option>
                    <option value="available" {% if current_stock == 'available' %}selected{% endif %}>Available Now</option>
                    <option value="preorder" {% if current_stock == 'preorder' %}selected{% endif %}>Pre-order</option>
                    <option value="notavailable" {% if current_stock == 'notavailable' %}selected{% endif %}>Out of Stock</option>
                </select>
            </div>

            <div class="sort-group">
                <label for="sort-options">Sort by:</label>
                <select id="sort-options" class="form-control" onchange="updateFilters()">
                    <option value="newest" {% if current_sort == 'newest' %}selected{% endif %}>Newest First</option>
                    <option value="price_asc" {% if current_sort == 'price_asc' %}selected{% endif %}>Price: Low to High</option>
                    <option value="price_desc" {% if current_sort == 'price_desc' %}selected{% endif %}>Price: High to Low</option>
                    <option value="name_asc" {% if current_sort == 'name_asc' %}selected{% endif %}>Name: A to Z</option>
                    <option value="name_desc" {% if current_sort == 'name_desc' %}selected{% endif %}>Name: Z to A</option>
                </select>
            </div>
        </div>
    </div>
</section>

<section class="brand-products">
    <div class="container">
        <div class="sales-products-grid">
            {% for product in products %}
            {% include 'includes/sales_product_card.html' with product=product %}
            {% empty %}
            <div class="no-products">
                <p>No products from {{ brand.name }} found.</p>
                <a href="{% url 'brand_list' %}" class="btn btn-primary me-3">View All Brands</a>
                <a href="{% url 'home' %}" class="btn btn-outline-primary">Back to Home</a>
            </div>
            {% endfor %}
        </div>

        <!-- Navigation dots for pagination -->
        {% if products.count > 12 %}
        <div class="product-nav-dots">
            <div class="nav-dot active"></div>
            <div class="nav-dot"></div>
            <div class="nav-dot"></div>
            <div class="nav-dot"></div>
        </div>
        {% endif %}
    </div>
</section>
{% endblock %}