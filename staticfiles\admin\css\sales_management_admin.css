/* Sales Management Admin Styling */

/* App-level styling */
.app-sales_management {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    margin-bottom: 15px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.app-sales_management h2 {
    color: white !important;
    font-weight: 600;
    padding: 20px;
    margin: 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px 12px 0 0;
}

.app-sales_management .model {
    background: rgba(255, 255, 255, 0.95);
    margin: 0;
    border-radius: 0 0 12px 12px;
}

.app-sales_management .model:last-child {
    border-radius: 0 0 12px 12px;
}

.app-sales_management .model a {
    color: #333 !important;
    font-weight: 500;
    padding: 15px 20px;
    display: block;
    transition: all 0.2s ease;
}

.app-sales_management .model a:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea !important;
    text-decoration: none;
}

/* Model-specific icons */
.app-sales_management .model-salecategory::before {
    content: "🏷️ ";
    font-size: 16px;
    margin-right: 8px;
}

.app-sales_management .model-productsaleassignment::before {
    content: "🔗 ";
    font-size: 16px;
    margin-right: 8px;
}

.app-sales_management .model-salecampaign::before {
    content: "📢 ";
    font-size: 16px;
    margin-right: 8px;
}

.app-sales_management .model-saleusagelog::before {
    content: "📊 ";
    font-size: 16px;
    margin-right: 8px;
}

.app-sales_management .model-saleperformancemetrics::before {
    content: "📈 ";
    font-size: 16px;
    margin-right: 8px;
}

.app-sales_management .model-salenotification::before {
    content: "🔔 ";
    font-size: 16px;
    margin-right: 8px;
}

/* Sale Category specific styling */
.sale-category-form .form-row {
    margin-bottom: 20px;
}

.sale-category-form .field-badge_color input {
    width: 100px;
    height: 50px;
    border: 3px solid #ddd;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.sale-category-form .field-badge_color input:hover {
    border-color: #667eea;
    transform: scale(1.05);
}

.sale-category-form .field-sale_type select {
    min-width: 250px;
    padding: 8px 12px;
    border-radius: 6px;
    border: 2px solid #ddd;
    font-size: 14px;
}

/* Badge preview styling */
.badge-preview-container {
    margin-top: 15px;
    padding: 15px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    border: 2px solid #e9ecef;
}

.badge-preview-label {
    font-weight: bold;
    margin-bottom: 8px;
    display: block;
    color: #495057;
}

.badge-preview {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.badge-preview:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Sale type helper */
.sale-type-helper {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border: 2px solid #2196f3;
    border-radius: 8px;
    padding: 12px;
    margin-top: 10px;
    font-size: 13px;
    color: #1565c0;
}

.sale-type-helper strong {
    color: #0d47a1;
}

/* Fieldset enhancements */
.sale-category-form fieldset {
    border: 2px solid #e1e4e8;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 25px;
    background: #fafbfc;
}

.sale-category-form fieldset h2 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin: -20px -20px 20px -20px;
    padding: 12px 20px;
    border-radius: 8px 8px 0 0;
    font-size: 16px;
    font-weight: 600;
}

/* List view enhancements */
.change-list .sale-category-row {
    transition: all 0.2s ease;
}

.change-list .sale-category-row:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.change-list .sale-category-row.featured {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-left: 4px solid #ffc107;
}

.change-list .sale-category-row.inactive {
    opacity: 0.6;
    background-color: #f8f9fa;
}

/* Priority indicators */
.field-priority .priority-1 { color: #6c757d; }
.field-priority .priority-2 { color: #007bff; }
.field-priority .priority-3 { color: #ffc107; }
.field-priority .priority-4 { color: #fd7e14; }
.field-priority .priority-5 { color: #dc3545; font-weight: bold; }

/* Status indicators */
.field-is_active .true {
    color: #28a745;
    font-weight: bold;
}

.field-is_active .false {
    color: #dc3545;
    font-weight: bold;
}

.field-is_featured .true {
    color: #ffc107;
    font-weight: bold;
}

.field-is_currently_active .true {
    color: #28a745;
}

.field-is_currently_active .false {
    color: #6c757d;
}

/* Campaign status styling */
.field-status .draft { color: #6c757d; }
.field-status .scheduled { color: #007bff; }
.field-status .active { color: #28a745; font-weight: bold; }
.field-status .paused { color: #ffc107; }
.field-status .completed { color: #17a2b8; }
.field-status .cancelled { color: #dc3545; }

/* Notification priority styling */
.field-priority .low { color: #6c757d; }
.field-priority .medium { color: #007bff; }
.field-priority .high { color: #ffc107; }
.field-priority .urgent { color: #dc3545; font-weight: bold; }

/* Action buttons */
.sale-management-actions {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
    border: 2px solid #dee2e6;
}

.sale-management-actions .button {
    margin-right: 10px;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.sale-management-actions .button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Dashboard widgets */
.sales-dashboard-widget {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.sales-dashboard-widget h3 {
    color: white;
    margin: 0 0 15px 0;
    font-size: 20px;
}

.sales-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.sales-stat-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    backdrop-filter: blur(10px);
}

.sales-stat-number {
    font-size: 24px;
    font-weight: bold;
    display: block;
    margin-bottom: 5px;
}

.sales-stat-label {
    font-size: 12px;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Responsive design */
@media (max-width: 768px) {
    .app-sales_management h2 {
        padding: 15px;
        font-size: 18px;
    }
    
    .sale-category-form fieldset {
        padding: 15px;
    }
    
    .sale-category-form fieldset h2 {
        margin: -15px -15px 15px -15px;
        padding: 10px 15px;
        font-size: 14px;
    }
    
    .sales-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }
}

/* Animation for new items */
@keyframes slideInFromTop {
    0% {
        opacity: 0;
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.app-sales_management {
    animation: slideInFromTop 0.5s ease-out;
}

/* Highlight active sales */
.change-list tr.active-sale {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-left: 4px solid #28a745;
}

/* Usage metrics styling */
.usage-metrics {
    display: flex;
    gap: 15px;
    margin: 10px 0;
}

.usage-metric {
    background: #f8f9fa;
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    font-size: 12px;
}

.usage-metric strong {
    color: #495057;
}
