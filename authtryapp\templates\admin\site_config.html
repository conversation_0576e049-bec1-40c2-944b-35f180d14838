{% extends 'admin/base_admin.html' %}

{% block title %}Site Configuration{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mt-4 mb-4">
        <h1>⚙️ Site Configuration</h1>
        <a href="{% url 'admin_hero_banners' %}" class="btn btn-outline-primary">
            <i class="fas fa-images"></i> Manage Banners
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <form method="post">
                {% csrf_token %}

                <!-- Site Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-info-circle"></i> Site Information
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="site_name" class="form-label">Site Name</label>
                                <input type="text" class="form-control" id="site_name" name="site_name" value="{% if config %}{{ config.site_name }}{% else %}iTechStore{% endif %}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="site_tagline" class="form-label">Site Tagline</label>
                                <input type="text" class="form-control" id="site_tagline" name="site_tagline" value="{% if config %}{{ config.site_tagline }}{% else %}Your Technology Destination{% endif %}">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hero Carousel Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-images"></i> Hero Carousel Settings
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="hero_auto_slide_interval" class="form-label">Auto Slide Interval</label>
                                <div class="mb-2">
                                    <input type="range" class="form-range" id="hero_auto_slide_range" min="2000" max="10000" step="500" value="{% if config %}{{ config.hero_auto_slide_interval }}{% else %}5000{% endif %}">
                                </div>
                                <input type="number" class="form-control" id="hero_auto_slide_interval" name="hero_auto_slide_interval" value="{% if config %}{{ config.hero_auto_slide_interval }}{% else %}5000{% endif %}" min="2000" max="10000" step="500">
                                <small class="form-text text-muted" id="intervalDisplay">5000ms = 5.0 seconds</small>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-check form-switch mt-4">
                                    <input class="form-check-input" type="checkbox" id="hero_show_arrows" name="hero_show_arrows" {% if not config or config.hero_show_arrows %}checked{% endif %}>
                                    <label class="form-check-label" for="hero_show_arrows">
                                        Show Navigation Arrows
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-check form-switch mt-4">
                                    <input class="form-check-input" type="checkbox" id="hero_show_dots" name="hero_show_dots" {% if not config or config.hero_show_dots %}checked{% endif %}>
                                    <label class="form-check-label" for="hero_show_dots">
                                        Show Navigation Dots
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Product Display Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-th"></i> Product Display Settings
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="products_per_row_desktop" class="form-label">Desktop (Products per Row)</label>
                                <select class="form-select" id="products_per_row_desktop" name="products_per_row_desktop">
                                    <option value="3" {% if config and config.products_per_row_desktop == 3 %}selected{% endif %}>3 Products</option>
                                    <option value="4" {% if not config or config.products_per_row_desktop == 4 %}selected{% endif %}>4 Products</option>
                                    <option value="5" {% if config and config.products_per_row_desktop == 5 %}selected{% endif %}>5 Products</option>
                                    <option value="6" {% if config and config.products_per_row_desktop == 6 %}selected{% endif %}>6 Products</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="products_per_row_tablet" class="form-label">Tablet (Products per Row)</label>
                                <select class="form-select" id="products_per_row_tablet" name="products_per_row_tablet">
                                    <option value="2" {% if not config or config.products_per_row_tablet == 2 %}selected{% endif %}>2 Products</option>
                                    <option value="3" {% if config and config.products_per_row_tablet == 3 %}selected{% endif %}>3 Products</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="products_per_row_mobile" class="form-label">Mobile (Products per Row)</label>
                                <select class="form-select" id="products_per_row_mobile" name="products_per_row_mobile">
                                    <option value="1" {% if not config or config.products_per_row_mobile == 1 %}selected{% endif %}>1 Product</option>
                                    <option value="2" {% if config and config.products_per_row_mobile == 2 %}selected{% endif %}>2 Products</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'admin_dashboard' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Configuration
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Preview/Info Section -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-info-circle"></i> Configuration Info
                </div>
                <div class="card-body">
                    <h6>Current Settings:</h6>
                    <ul class="list-unstyled">
                        <li><strong>Site Name:</strong> {{ config.site_name|default:"iTechStore" }}</li>
                        <li><strong>Carousel Speed:</strong> {{ config.hero_auto_slide_interval|default:"5000" }}ms</li>
                        <li><strong>Navigation:</strong>
                            {% if config.hero_show_arrows|default:True %}Arrows{% endif %}
                            {% if config.hero_show_dots|default:True %}{% if config.hero_show_arrows|default:True %} + {% endif %}Dots{% endif %}
                        </li>
                        <li><strong>Last Updated:</strong> {{ config.updated_at|date:"M d, Y H:i"|default:"Never" }}</li>
                    </ul>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-lightbulb"></i> Quick Tips
                </div>
                <div class="card-body">
                    <h6>Carousel Speed:</h6>
                    <ul class="small">
                        <li><strong>3000ms:</strong> Fast (3 seconds)</li>
                        <li><strong>5000ms:</strong> Normal (5 seconds)</li>
                        <li><strong>7000ms:</strong> Slow (7 seconds)</li>
                    </ul>

                    <h6 class="mt-3">Product Layout:</h6>
                    <ul class="small">
                        <li>Desktop: 4 products works best</li>
                        <li>Tablet: 2-3 products optimal</li>
                        <li>Mobile: 1 product recommended</li>
                    </ul>
                </div>
            </div>

            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <i class="fas fa-exclamation-triangle"></i> Important
                </div>
                <div class="card-body">
                    <p class="small mb-0">Changes take effect immediately on the website. Test your settings on different devices to ensure optimal user experience.</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.card-header {
    font-weight: 600;
}

.form-label {
    font-weight: 500;
    color: #495057;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
    transform: translateY(-1px);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-save indication
    const form = document.querySelector('form');
    const submitBtn = form.querySelector('button[type="submit"]');

    form.addEventListener('submit', function() {
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
        submitBtn.disabled = true;
    });

    // Interval slider and input sync
    const intervalInput = document.getElementById('hero_auto_slide_interval');
    const intervalRange = document.getElementById('hero_auto_slide_range');
    const intervalDisplay = document.getElementById('intervalDisplay');

    function updateIntervalDisplay(value) {
        const seconds = (value / 1000).toFixed(1);
        intervalDisplay.textContent = `${value}ms = ${seconds} seconds`;
    }

    intervalInput.addEventListener('input', function() {
        intervalRange.value = this.value;
        updateIntervalDisplay(this.value);
    });

    intervalRange.addEventListener('input', function() {
        intervalInput.value = this.value;
        updateIntervalDisplay(this.value);
    });

    // Initialize display
    updateIntervalDisplay(intervalInput.value);

    // Form validation
    form.addEventListener('submit', function(e) {
        const interval = parseInt(intervalInput.value);
        if (interval < 2000 || interval > 10000) {
            e.preventDefault();
            alert('Slide interval must be between 2000ms (2 seconds) and 10000ms (10 seconds)');
            return false;
        }
    });
});
</script>
{% endblock %}
