# Generated by Django 5.0.6 on 2025-05-26 11:48

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('authtryapp', '0010_salecategory_product_sale_categories'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SaleCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text="Sale category name (e.g., '10% Off', 'Free Headphones')", max_length=100)),
                ('slug', models.SlugField(blank=True, max_length=100, unique=True)),
                ('sale_type', models.CharField(choices=[('percentage', 'Percentage Discount'), ('fixed_amount', 'Fixed Amount Off'), ('free_item', 'Free Item with Purchase'), ('buy_x_get_y', 'Buy X Get Y Free'), ('bundle', 'Bundle Deal'), ('clearance', 'Clearance Sale'), ('flash_sale', 'Flash Sale'), ('seasonal', 'Seasonal Sale'), ('gift_with_purchase', 'Gift with Purchase'), ('free_shipping', 'Free Shipping'), ('loyalty_discount', 'Loyalty Member Discount'), ('student_discount', 'Student Discount'), ('bulk_discount', 'Bulk Purchase Discount'), ('referral_bonus', 'Referral Bonus'), ('first_time_buyer', 'First Time Buyer Discount')], default='percentage', max_length=20)),
                ('description', models.TextField(blank=True, help_text='Detailed description of the sale')),
                ('discount_percentage', models.DecimalField(blank=True, decimal_places=2, help_text='Discount percentage (0-100)', max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('discount_amount', models.DecimalField(blank=True, decimal_places=2, help_text='Fixed discount amount', max_digits=10, null=True)),
                ('minimum_purchase_amount', models.DecimalField(blank=True, decimal_places=2, help_text='Minimum purchase amount to qualify for this sale', max_digits=10, null=True)),
                ('maximum_discount_amount', models.DecimalField(blank=True, decimal_places=2, help_text='Maximum discount amount (for percentage discounts)', max_digits=10, null=True)),
                ('free_item_description', models.CharField(blank=True, help_text="Description of free item (e.g., 'Free wireless mouse')", max_length=200)),
                ('free_item_value', models.DecimalField(blank=True, decimal_places=2, help_text='Value of the free item', max_digits=10, null=True)),
                ('buy_quantity', models.PositiveIntegerField(blank=True, help_text='Buy X quantity (for Buy X Get Y deals)', null=True)),
                ('get_quantity', models.PositiveIntegerField(blank=True, help_text='Get Y quantity free (for Buy X Get Y deals)', null=True)),
                ('badge_text', models.CharField(help_text='Text to show on product badge', max_length=50)),
                ('badge_color', models.CharField(choices=[('#dc3545', 'Red - Urgent/Hot Deals'), ('#fd7e14', 'Orange - Special Offers'), ('#ffc107', 'Yellow - Attention'), ('#28a745', 'Green - Free Items/Eco'), ('#007bff', 'Blue - Trust/Premium'), ('#6f42c1', 'Purple - Exclusive/VIP'), ('#e83e8c', 'Pink - Flash/Limited'), ('#17a2b8', 'Teal - Fresh/New'), ('#6c757d', 'Gray - Clearance'), ('#343a40', 'Dark - Premium/Luxury'), ('#20c997', 'Mint - Fresh Deals'), ('#fd79a8', 'Rose - Valentine/Special')], default='#dc3545', max_length=7)),
                ('priority', models.IntegerField(choices=[(1, 'Low Priority'), (2, 'Normal Priority'), (3, 'High Priority'), (4, 'Urgent Priority'), (5, 'Critical Priority')], default=2, help_text='Display priority (higher numbers show first)')),
                ('start_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('end_date', models.DateTimeField(blank=True, help_text='Leave blank for ongoing sale', null=True)),
                ('max_uses_per_customer', models.PositiveIntegerField(blank=True, help_text='Maximum times a customer can use this sale (leave blank for unlimited)', null=True)),
                ('max_total_uses', models.PositiveIntegerField(blank=True, help_text='Maximum total uses across all customers (leave blank for unlimited)', null=True)),
                ('current_uses', models.PositiveIntegerField(default=0, help_text='Current number of uses')),
                ('is_active', models.BooleanField(default=True)),
                ('is_featured', models.BooleanField(default=False, help_text='Show prominently on homepage')),
                ('is_stackable', models.BooleanField(default=False, help_text='Can be combined with other sales')),
                ('requires_coupon_code', models.BooleanField(default=False, help_text='Requires a coupon code to activate')),
                ('coupon_code', models.CharField(blank=True, help_text='Coupon code (if required)', max_length=50)),
                ('is_member_only', models.BooleanField(default=False, help_text='Only for registered members')),
                ('is_first_time_buyer_only', models.BooleanField(default=False, help_text='Only for first-time buyers')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_sales', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Sale Category',
                'verbose_name_plural': 'Sale Categories',
                'ordering': ['-priority', '-is_featured', '-start_date'],
            },
        ),
        migrations.CreateModel(
            name='SaleCampaign',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('slug', models.SlugField(blank=True, max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('start_date', models.DateTimeField()),
                ('end_date', models.DateTimeField(blank=True, null=True)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('scheduled', 'Scheduled'), ('active', 'Active'), ('paused', 'Paused'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='draft', max_length=20)),
                ('is_featured', models.BooleanField(default=False)),
                ('target_revenue', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('target_conversions', models.PositiveIntegerField(blank=True, null=True)),
                ('budget', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_campaigns', to=settings.AUTH_USER_MODEL)),
                ('sale_categories', models.ManyToManyField(blank=True, related_name='campaigns', to='sales_management.salecategory')),
            ],
            options={
                'verbose_name': 'Sale Campaign',
                'verbose_name_plural': 'Sale Campaigns',
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='SaleNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notification_type', models.CharField(choices=[('sale_started', 'Sale Started'), ('sale_ending_soon', 'Sale Ending Soon'), ('sale_ended', 'Sale Ended'), ('usage_limit_reached', 'Usage Limit Reached'), ('low_performance', 'Low Performance Alert'), ('high_performance', 'High Performance Alert'), ('budget_exceeded', 'Budget Exceeded'), ('target_reached', 'Target Reached')], max_length=20)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='medium', max_length=10)),
                ('title', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('send_to_all_admins', models.BooleanField(default=False)),
                ('is_read', models.BooleanField(default=False)),
                ('is_sent', models.BooleanField(default=False)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('campaign', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='sales_management.salecampaign')),
                ('recipient', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('sale_category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='sales_management.salecategory')),
            ],
            options={
                'verbose_name': 'Sale Notification',
                'verbose_name_plural': 'Sale Notifications',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SaleUsageLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('original_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('discount_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('final_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('session_key', models.CharField(blank=True, max_length=40)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('used_at', models.DateTimeField(auto_now_add=True)),
                ('order_id', models.CharField(blank=True, help_text='Associated order ID if applicable', max_length=100)),
                ('product', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sale_usage_logs', to='authtryapp.product')),
                ('sale_category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='usage_logs', to='sales_management.salecategory')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Sale Usage Log',
                'verbose_name_plural': 'Sale Usage Logs',
                'ordering': ['-used_at'],
            },
        ),
        migrations.CreateModel(
            name='ProductSaleAssignment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('assigned_date', models.DateTimeField(auto_now_add=True)),
                ('is_active', models.BooleanField(default=True)),
                ('custom_discount_percentage', models.DecimalField(blank=True, decimal_places=2, help_text='Override discount percentage for this product', max_digits=5, null=True)),
                ('custom_discount_amount', models.DecimalField(blank=True, decimal_places=2, help_text='Override discount amount for this product', max_digits=10, null=True)),
                ('assigned_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='product_sale_assignments', to=settings.AUTH_USER_MODEL)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sale_assignments', to='authtryapp.product')),
                ('sale_category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_assignments', to='sales_management.salecategory')),
            ],
            options={
                'verbose_name': 'Product Sale Assignment',
                'verbose_name_plural': 'Product Sale Assignments',
                'ordering': ['-assigned_date'],
                'unique_together': {('product', 'sale_category')},
            },
        ),
        migrations.CreateModel(
            name='SalePerformanceMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('period_type', models.CharField(choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly')], max_length=10)),
                ('period_start', models.DateField()),
                ('period_end', models.DateField()),
                ('total_uses', models.PositiveIntegerField(default=0)),
                ('total_savings_given', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('total_revenue_impact', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('unique_users', models.PositiveIntegerField(default=0)),
                ('unique_products', models.PositiveIntegerField(default=0)),
                ('views', models.PositiveIntegerField(default=0, help_text='How many times products with this sale were viewed')),
                ('conversions', models.PositiveIntegerField(default=0, help_text='How many times the sale was actually used')),
                ('conversion_rate', models.DecimalField(decimal_places=2, default=0, help_text='Conversion rate percentage', max_digits=5)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('sale_category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='performance_metrics', to='sales_management.salecategory')),
            ],
            options={
                'verbose_name': 'Sale Performance Metric',
                'verbose_name_plural': 'Sale Performance Metrics',
                'ordering': ['-period_start'],
                'unique_together': {('sale_category', 'period_type', 'period_start')},
            },
        ),
    ]
