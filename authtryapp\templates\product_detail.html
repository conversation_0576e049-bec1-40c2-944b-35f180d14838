{% extends 'base_new.html' %}
{% load static %}

{% block title %}{{ product.name }} - iTechStore{% endblock %}

{% block extra_css %}
<style>
    .product-detail {
        padding: 30px 0 60px;
    }

    .breadcrumb {
        margin-bottom: 30px;
        padding: 12px 0;
        font-size: 14px;
    }

    .breadcrumb-item a {
        color: #6c757d;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #0070e0;
    }

    .breadcrumb-item.active {
        color: #212529;
        font-weight: 500;
    }

    .product-gallery {
        position: relative;
        background: #fff;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0,0,0,0.08);
        padding: 30px;
        height: 500px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .product-gallery img {
        max-height: 400px;
        max-width: 100%;
        object-fit: contain;
    }

    .product-thumbnails {
        display: flex;
        gap: 10px;
        margin-top: 20px;
    }

    .product-thumbnail {
        width: 80px;
        height: 80px;
        border-radius: 8px;
        padding: 5px;
        cursor: pointer;
        border: 2px solid transparent;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #fff;
        box-shadow: 0 1px 3px rgba(0,0,0,0.08);
    }

    .product-thumbnail img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }

    .product-thumbnail.active {
        border-color: #0070e0;
    }

    .product-info {
        padding: 0 0 0 30px;
    }

    .product-brand {
        font-size: 14px;
        color: #6c757d;
        text-transform: uppercase;
        font-weight: 600;
        margin-bottom: 8px;
        letter-spacing: 0.5px;
    }

    .product-title {
        font-size: 32px;
        font-weight: 700;
        margin-bottom: 20px;
        color: #212529;
        line-height: 1.2;
    }

    .product-price-section {
        margin-bottom: 25px;
    }

    .starting-from {
        font-size: 13px;
        color: #6c757d;
        margin-bottom: 5px;
    }

    .product-price {
        font-size: 28px;
        font-weight: 700;
        color: #000;
    }

    .stock-status {
        display: inline-block;
        padding: 5px 12px;
        font-size: 12px;
        font-weight: 500;
        border-radius: 4px;
        margin-left: 15px;
        text-transform: capitalize;
        vertical-align: middle;
    }

    .stock-status.available {
        background: #004277;
        color: white;
    }

    .stock-status.preorder {
        background: #ffc107;
        color: #212529;
    }

    .stock-status.notavailable {
        background: #dc3545;
        color: white;
    }

    .product-description {
        margin-bottom: 30px;
        color: #495057;
        line-height: 1.6;
    }

    .product-features {
        margin-bottom: 30px;
    }

    .product-features h3 {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 15px;
        color: #212529;
    }

    .feature-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .feature-list li {
        padding: 8px 0;
        position: relative;
        padding-left: 25px;
        color: #495057;
    }

    .feature-list li::before {
        content: "✓";
        position: absolute;
        left: 0;
        color: #0070e0;
        font-weight: bold;
    }

    .product-actions {
        margin-top: 30px;
        display: flex;
        gap: 15px;
    }

    .quantity-selector {
        display: flex;
        align-items: center;
        width: 120px;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        overflow: hidden;
    }

    .quantity-btn {
        width: 36px;
        height: 46px;
        border: none;
        background: #f8f9fa;
        font-size: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #495057;
    }

    .quantity-btn:hover {
        background: #e9ecef;
    }

    .quantity-input {
        flex: 1;
        height: 46px;
        border: none;
        text-align: center;
        font-size: 16px;
        font-weight: 600;
        color: #212529;
    }

    .add-to-cart {
        flex: 1;
        height: 46px;
        border: none;
        background: #0070e0;
        color: white;
        border-radius: 6px;
        font-weight: 600;
        cursor: pointer;
        transition: background 0.2s;
        font-size: 15px;
    }

    .add-to-cart:hover {
        background: #0058b0;
    }

    .add-to-cart.disabled {
        background: #6c757d;
        cursor: not-allowed;
        opacity: 0.7;
    }

    .preorder-btn {
        background: #ffc107;
        color: #212529;
    }

    .preorder-btn:hover {
        background: #e0a800;
    }

    .wishlist-btn {
        width: 46px;
        height: 46px;
        border: 1px solid #dee2e6;
        background: white;
        color: #495057;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s;
    }

    .wishlist-btn:hover {
        background: #f8f9fa;
        border-color: #0070e0;
        color: #0070e0;
    }

    .product-tabs {
        margin-top: 60px;
    }

    .tab-list {
        display: flex;
        gap: 5px;
        border-bottom: 1px solid #dee2e6;
        margin-bottom: 30px;
    }

    .tab-item {
        padding: 12px 20px;
        font-weight: 600;
        color: #6c757d;
        cursor: pointer;
        position: relative;
    }

    .tab-item.active {
        color: #0070e0;
    }

    .tab-item.active::after {
        content: "";
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        height: 2px;
        background: #0070e0;
    }

    .tab-content {
        display: none;
        padding: 0 15px;
    }

    .tab-content.active {
        display: block;
    }

    .specification-table {
        width: 100%;
        border-collapse: collapse;
    }

    .specification-table tr:nth-child(even) {
        background: #f8f9fa;
    }

    .specification-table th, .specification-table td {
        padding: 12px 15px;
        text-align: left;
        border-bottom: 1px solid #dee2e6;
    }

    .specification-table th {
        width: 30%;
        color: #495057;
        font-weight: 600;
    }

    .related-products {
        margin-top: 60px;
    }

    .related-products h2 {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 25px;
        color: #212529;
    }

    .product-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 24px;
        margin-bottom: 50px;
    }

    .product-card {
        background: #fff;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0,0,0,0.08);
        transition: transform 0.3s, box-shadow 0.3s;
        position: relative;
        display: flex;
        flex-direction: column;
        height: 100%;
        padding: 0;
        border: none;
    }

    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0,0,0,0.08);
    }

    .product-card-image {
        position: relative;
        height: 200px;
        padding: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #fff;
    }

    .product-card-image img {
        max-height: 160px;
        width: auto;
        max-width: 100%;
        object-fit: contain;
    }

    .product-card-info {
        padding: 16px 20px 20px;
        display: flex;
        flex-direction: column;
        flex-grow: 1;
    }

    .product-card-brand {
        font-size: 12px;
        color: #6c757d;
        text-transform: uppercase;
        font-weight: 600;
        margin-bottom: 4px;
        letter-spacing: 0.5px;
    }

    .product-card-info h3 {
        font-size: 15px;
        font-weight: 600;
        margin-bottom: 12px;
        color: #212529;
        line-height: 1.3;
    }

    .product-card-price {
        font-size: 18px;
        font-weight: 700;
        color: #000;
        margin-top: auto;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab functionality
    const tabItems = document.querySelectorAll('.tab-item');
    const tabContents = document.querySelectorAll('.tab-content');

    tabItems.forEach(tab => {
        tab.addEventListener('click', function() {
            // Remove active class from all tabs and contents
            tabItems.forEach(item => item.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // Add active class to clicked tab and corresponding content
            this.classList.add('active');
            const tabId = this.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
        });
    });

    // Quantity selector
    const minusBtn = document.querySelector('.quantity-minus');
    const plusBtn = document.querySelector('.quantity-plus');
    const quantityInput = document.querySelector('.quantity-input');

    minusBtn.addEventListener('click', function() {
        let quantity = parseInt(quantityInput.value);
        if (quantity > 1) {
            quantityInput.value = quantity - 1;
        }
    });

    plusBtn.addEventListener('click', function() {
        let quantity = parseInt(quantityInput.value);
        quantityInput.value = quantity + 1;
    });

    // Image gallery
    const thumbnails = document.querySelectorAll('.product-thumbnail');
    const mainImage = document.querySelector('.product-gallery img');

    thumbnails.forEach(thumbnail => {
        thumbnail.addEventListener('click', function() {
            // Remove active class from all thumbnails
            thumbnails.forEach(item => item.classList.remove('active'));

            // Add active class to clicked thumbnail
            this.classList.add('active');

            // Update main image
            const imgSrc = this.querySelector('img').getAttribute('src');
            mainImage.setAttribute('src', imgSrc);
        });
    });
});
</script>
{% endblock %}

{% block content %}
<div class="container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">Home</a></li>
            {% if product.category %}
            <li class="breadcrumb-item"><a href="{% url 'category_view' product.category.slug %}">{{ product.category.name }}</a></li>
            {% endif %}
            <li class="breadcrumb-item active">{{ product.name }}</li>
        </ol>
    </nav>

    <div class="product-detail">
        <div class="row">
            <!-- Product Gallery -->
            <div class="col-md-6">
                <div class="product-gallery">
                    {% if product.image %}
                    <img src="{{ product.image.url }}" alt="{{ product.name }}">
                    {% else %}
                    <img src="https://via.placeholder.com/500x500?text=Product+Image" alt="{{ product.name }}">
                    {% endif %}
                </div>
                <div class="product-thumbnails">
                    <div class="product-thumbnail active">
                        {% if product.image %}
                        <img src="{{ product.image.url }}" alt="{{ product.name }}">
                        {% else %}
                        <img src="https://via.placeholder.com/80x80?text=Thumbnail" alt="{{ product.name }}">
                        {% endif %}
                    </div>
                    <div class="product-thumbnail">
                        <img src="https://via.placeholder.com/80x80?text=Thumbnail+2" alt="{{ product.name }}">
                    </div>
                    <div class="product-thumbnail">
                        <img src="https://via.placeholder.com/80x80?text=Thumbnail+3" alt="{{ product.name }}">
                    </div>
                    <div class="product-thumbnail">
                        <img src="https://via.placeholder.com/80x80?text=Thumbnail+4" alt="{{ product.name }}">
                    </div>
                </div>
            </div>

            <!-- Product Info -->
            <div class="col-md-6">
                <div class="product-info">
                    <div class="product-brand">{{ product.brand|default:"LENOVO" }}</div>
                    <h1 class="product-title">{{ product.name }}</h1>

                    <div class="product-price-section">
                        <div class="starting-from">Starting from</div>
                        <span class="product-price">₨. {{ product.price|floatformat:2 }}</span>

                        {% if product.stock_status == 'available' %}
                        <span class="stock-status available">Available now</span>
                        {% elif product.stock_status == 'preorder' %}
                        <span class="stock-status preorder">Pre-order</span>
                        {% elif product.stock_status == 'notavailable' %}
                        <span class="stock-status notavailable">Out of stock</span>
                        {% endif %}
                    </div>

                    <div class="product-description">
                        {{ product.description|default:"Experience next-level performance with this high-quality product. Designed for both professional and personal use, this device offers exceptional value with its cutting-edge features and reliable performance." }}
                    </div>

                    <div class="product-features">
                        <h3>Key Features</h3>
                        <ul class="feature-list">
                            <li>High performance processor</li>
                            <li>Premium build quality</li>
                            <li>Extended battery life</li>
                            <li>Enhanced security features</li>
                            <li>12-month manufacturer warranty</li>
                        </ul>
                    </div>

                    <div class="product-actions">
                        <div class="quantity-selector">
                            <button type="button" class="quantity-btn quantity-minus">-</button>
                            <input type="number" class="quantity-input" value="1" min="1" max="10">
                            <button type="button" class="quantity-btn quantity-plus">+</button>
                        </div>

                        {% if product.stock_status == 'available' %}
                        <button class="add-to-cart" data-product-id="{{ product.id }}">Add to Cart</button>
                        {% elif product.stock_status == 'preorder' %}
                        <button class="add-to-cart preorder-btn" data-product-id="{{ product.id }}">Pre-order Now</button>
                        {% else %}
                        <button class="add-to-cart disabled" disabled>Out of Stock</button>
                        {% endif %}

                        <button class="wishlist-btn">
                            <i class="far fa-heart"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Product Tabs -->
        <div class="product-tabs">
            <div class="tab-list">
                <div class="tab-item active" data-tab="tab-description">Description</div>
                <div class="tab-item" data-tab="tab-specification">Specifications</div>
                <div class="tab-item" data-tab="tab-reviews">Reviews</div>
            </div>

            <div id="tab-description" class="tab-content active">
                <p>{{ product.description|default:"Experience next-level performance with this high-quality product. Designed for both professional and personal use, this device offers exceptional value with its cutting-edge features and reliable performance." }}</p>
                <p>This product features the latest technology to enhance your productivity and entertainment experience. With its sleek design and powerful capabilities, it's the perfect choice for tech enthusiasts and professionals alike.</p>
                <p>Key highlights include:</p>
                <ul>
                    <li>Exceptional performance for multitasking</li>
                    <li>Stunning visual display with vibrant colors</li>
                    <li>Enhanced audio for immersive entertainment</li>
                    <li>Long-lasting battery for all-day use</li>
                    <li>Compact and lightweight design for portability</li>
                </ul>
            </div>

            <div id="tab-specification" class="tab-content">
                <table class="specification-table">
                    <tr>
                        <th>Brand</th>
                        <td>{{ product.brand|default:"LENOVO" }}</td>
                    </tr>
                    <tr>
                        <th>Model</th>
                        <td>{{ product.name }}</td>
                    </tr>
                    <tr>
                        <th>Processor</th>
                        <td>Intel Core i7, 11th Generation</td>
                    </tr>
                    <tr>
                        <th>Memory</th>
                        <td>16GB DDR4</td>
                    </tr>
                    <tr>
                        <th>Storage</th>
                        <td>512GB SSD</td>
                    </tr>
                    <tr>
                        <th>Display</th>
                        <td>14-inch Full HD (1920 x 1080)</td>
                    </tr>
                    <tr>
                        <th>Graphics</th>
                        <td>NVIDIA GeForce RTX 3050</td>
                    </tr>
                    <tr>
                        <th>Operating System</th>
                        <td>Windows 11 Home</td>
                    </tr>
                    <tr>
                        <th>Battery</th>
                        <td>Up to 10 hours</td>
                    </tr>
                    <tr>
                        <th>Weight</th>
                        <td>1.6 kg</td>
                    </tr>
                    <tr>
                        <th>Warranty</th>
                        <td>1 Year Manufacturer Warranty</td>
                    </tr>
                </table>
            </div>

            <div id="tab-reviews" class="tab-content">
                <p>No reviews yet. Be the first to review this product!</p>
            </div>
        </div>

        <!-- Related Products -->
        <div class="related-products">
            <h2>You May Also Like</h2>
            <div class="sales-products-grid">
                {% for product in related_products|slice:":4" %}
                    {% include 'includes/sales_product_card.html' with product=product %}
                {% empty %}
                <div class="no-products-message" style="grid-column: 1 / -1; text-align: center; padding: 40px 20px;">
                    <p>No related products found.</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}