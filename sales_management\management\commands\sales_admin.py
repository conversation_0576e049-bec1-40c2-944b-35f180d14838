#!/usr/bin/env python
"""
Enhanced management command for sales management
"""
from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from django.db import transaction
from sales_management.models import SaleCategory, ProductSaleAssignment, SaleCampaign
from authtryapp.models import Product
from decimal import Decimal
from datetime import timedelta


class Command(BaseCommand):
    help = 'Advanced sales management operations'

    def add_arguments(self, parser):
        subparsers = parser.add_subparsers(dest='action', help='Available actions')
        
        # Create sale category
        create_parser = subparsers.add_parser('create-sale', help='Create a new sale category')
        create_parser.add_argument('--name', required=True, help='Sale category name')
        create_parser.add_argument('--type', required=True, choices=[
            'percentage', 'fixed_amount', 'free_item', 'buy_x_get_y', 
            'bundle', 'clearance', 'flash_sale', 'seasonal', 
            'gift_with_purchase', 'free_shipping', 'loyalty_discount',
            'student_discount', 'bulk_discount', 'referral_bonus', 'first_time_buyer'
        ], help='Sale type')
        create_parser.add_argument('--badge-text', required=True, help='Badge text to display')
        create_parser.add_argument('--badge-color', default='#dc3545', help='Badge color (hex)')
        create_parser.add_argument('--priority', type=int, default=2, choices=[1,2,3,4,5], help='Priority (1-5)')
        create_parser.add_argument('--discount-percentage', type=float, help='Discount percentage (0-100)')
        create_parser.add_argument('--discount-amount', type=float, help='Fixed discount amount')
        create_parser.add_argument('--min-purchase', type=float, help='Minimum purchase amount')
        create_parser.add_argument('--max-discount', type=float, help='Maximum discount amount')
        create_parser.add_argument('--free-item', help='Free item description')
        create_parser.add_argument('--free-item-value', type=float, help='Free item value')
        create_parser.add_argument('--buy-quantity', type=int, help='Buy X quantity')
        create_parser.add_argument('--get-quantity', type=int, help='Get Y quantity free')
        create_parser.add_argument('--description', help='Sale description')
        create_parser.add_argument('--featured', action='store_true', help='Mark as featured')
        create_parser.add_argument('--member-only', action='store_true', help='Members only')
        create_parser.add_argument('--first-time-only', action='store_true', help='First time buyers only')
        create_parser.add_argument('--stackable', action='store_true', help='Can be stacked with other sales')
        create_parser.add_argument('--coupon-code', help='Required coupon code')
        create_parser.add_argument('--max-uses', type=int, help='Maximum total uses')
        create_parser.add_argument('--max-uses-per-customer', type=int, help='Max uses per customer')
        create_parser.add_argument('--end-date', help='End date (YYYY-MM-DD HH:MM:SS)')
        
        # Assign products to sale
        assign_parser = subparsers.add_parser('assign-products', help='Assign products to sale category')
        assign_parser.add_argument('--sale-slug', required=True, help='Sale category slug')
        assign_parser.add_argument('--product-ids', nargs='+', type=int, help='Product IDs to assign')
        assign_parser.add_argument('--product-names', nargs='+', help='Product names to assign (partial match)')
        assign_parser.add_argument('--category-name', help='Assign all products from this category')
        assign_parser.add_argument('--brand-name', help='Assign all products from this brand')
        assign_parser.add_argument('--custom-discount-percentage', type=float, help='Custom discount for these products')
        assign_parser.add_argument('--custom-discount-amount', type=float, help='Custom discount amount for these products')
        
        # Remove products from sale
        remove_parser = subparsers.add_parser('remove-products', help='Remove products from sale category')
        remove_parser.add_argument('--sale-slug', required=True, help='Sale category slug')
        remove_parser.add_argument('--product-ids', nargs='+', type=int, help='Product IDs to remove')
        remove_parser.add_argument('--all', action='store_true', help='Remove all products from sale')
        
        # Create campaign
        campaign_parser = subparsers.add_parser('create-campaign', help='Create a sale campaign')
        campaign_parser.add_argument('--name', required=True, help='Campaign name')
        campaign_parser.add_argument('--description', help='Campaign description')
        campaign_parser.add_argument('--start-date', help='Start date (YYYY-MM-DD HH:MM:SS)')
        campaign_parser.add_argument('--end-date', help='End date (YYYY-MM-DD HH:MM:SS)')
        campaign_parser.add_argument('--target-revenue', type=float, help='Target revenue')
        campaign_parser.add_argument('--budget', type=float, help='Campaign budget')
        campaign_parser.add_argument('--featured', action='store_true', help='Mark as featured')
        campaign_parser.add_argument('--sale-slugs', nargs='+', help='Sale category slugs to include')
        
        # List operations
        list_parser = subparsers.add_parser('list-sales', help='List all sale categories')
        list_parser.add_argument('--active-only', action='store_true', help='Show only active sales')
        list_parser.add_argument('--featured-only', action='store_true', help='Show only featured sales')
        list_parser.add_argument('--type', help='Filter by sale type')
        
        # Analytics
        analytics_parser = subparsers.add_parser('analytics', help='Show sales analytics')
        analytics_parser.add_argument('--sale-slug', help='Analytics for specific sale')
        analytics_parser.add_argument('--days', type=int, default=30, help='Number of days to analyze')
        
        # Bulk operations
        bulk_parser = subparsers.add_parser('bulk-update', help='Bulk update sales')
        bulk_parser.add_argument('--activate-all', action='store_true', help='Activate all sales')
        bulk_parser.add_argument('--deactivate-all', action='store_true', help='Deactivate all sales')
        bulk_parser.add_argument('--cleanup-expired', action='store_true', help='Deactivate expired sales')

    def handle(self, *args, **options):
        action = options['action']
        
        if action == 'create-sale':
            self.create_sale_category(options)
        elif action == 'assign-products':
            self.assign_products_to_sale(options)
        elif action == 'remove-products':
            self.remove_products_from_sale(options)
        elif action == 'create-campaign':
            self.create_campaign(options)
        elif action == 'list-sales':
            self.list_sale_categories(options)
        elif action == 'analytics':
            self.show_analytics(options)
        elif action == 'bulk-update':
            self.bulk_update_sales(options)
        else:
            self.stdout.write(self.style.ERROR('Please specify an action'))

    @transaction.atomic
    def create_sale_category(self, options):
        """Create a new sale category with all options"""
        try:
            sale_data = {
                'name': options['name'],
                'sale_type': options['type'],
                'badge_text': options['badge_text'],
                'badge_color': options['badge_color'],
                'priority': options['priority'],
                'is_featured': options.get('featured', False),
                'is_member_only': options.get('member_only', False),
                'is_first_time_buyer_only': options.get('first_time_only', False),
                'is_stackable': options.get('stackable', False),
                'requires_coupon_code': bool(options.get('coupon_code')),
            }
            
            # Add optional fields
            if options.get('discount_percentage'):
                sale_data['discount_percentage'] = Decimal(str(options['discount_percentage']))
            if options.get('discount_amount'):
                sale_data['discount_amount'] = Decimal(str(options['discount_amount']))
            if options.get('min_purchase'):
                sale_data['minimum_purchase_amount'] = Decimal(str(options['min_purchase']))
            if options.get('max_discount'):
                sale_data['maximum_discount_amount'] = Decimal(str(options['max_discount']))
            if options.get('free_item'):
                sale_data['free_item_description'] = options['free_item']
            if options.get('free_item_value'):
                sale_data['free_item_value'] = Decimal(str(options['free_item_value']))
            if options.get('buy_quantity'):
                sale_data['buy_quantity'] = options['buy_quantity']
            if options.get('get_quantity'):
                sale_data['get_quantity'] = options['get_quantity']
            if options.get('description'):
                sale_data['description'] = options['description']
            if options.get('coupon_code'):
                sale_data['coupon_code'] = options['coupon_code']
            if options.get('max_uses'):
                sale_data['max_total_uses'] = options['max_uses']
            if options.get('max_uses_per_customer'):
                sale_data['max_uses_per_customer'] = options['max_uses_per_customer']
            if options.get('end_date'):
                sale_data['end_date'] = timezone.datetime.strptime(
                    options['end_date'], '%Y-%m-%d %H:%M:%S'
                ).replace(tzinfo=timezone.get_current_timezone())
            
            sale = SaleCategory.objects.create(**sale_data)
            self.stdout.write(
                self.style.SUCCESS(f'✅ Created sale category: {sale.name} (slug: {sale.slug})')
            )
            
            # Show configuration summary
            self.stdout.write('\n📋 Sale Configuration:')
            self.stdout.write(f'   Type: {sale.get_sale_type_display()}')
            self.stdout.write(f'   Badge: {sale.badge_text} ({sale.badge_color})')
            self.stdout.write(f'   Priority: {sale.priority}/5')
            if sale.discount_percentage:
                self.stdout.write(f'   Discount: {sale.discount_percentage}%')
            if sale.discount_amount:
                self.stdout.write(f'   Discount: ${sale.discount_amount}')
            if sale.minimum_purchase_amount:
                self.stdout.write(f'   Min Purchase: ${sale.minimum_purchase_amount}')
            self.stdout.write(f'   Featured: {"Yes" if sale.is_featured else "No"}')
            self.stdout.write(f'   Active: {"Yes" if sale.is_active else "No"}')
            
        except Exception as e:
            raise CommandError(f'Error creating sale category: {e}')

    def assign_products_to_sale(self, options):
        """Assign products to a sale category"""
        try:
            sale = SaleCategory.objects.get(slug=options['sale_slug'])
        except SaleCategory.DoesNotExist:
            raise CommandError(f'Sale category with slug "{options["sale_slug"]}" not found')
        
        products_assigned = 0
        products = []
        
        # Collect products from various sources
        if options.get('product_ids'):
            products.extend(Product.objects.filter(id__in=options['product_ids']))
        
        if options.get('product_names'):
            for name_part in options['product_names']:
                products.extend(Product.objects.filter(name__icontains=name_part))
        
        if options.get('category_name'):
            products.extend(Product.objects.filter(category__name__icontains=options['category_name']))
        
        if options.get('brand_name'):
            products.extend(Product.objects.filter(brand__name__icontains=options['brand_name']))
        
        # Remove duplicates
        products = list(set(products))
        
        for product in products:
            assignment_data = {
                'product': product,
                'sale_category': sale,
                'is_active': True,
            }
            
            if options.get('custom_discount_percentage'):
                assignment_data['custom_discount_percentage'] = Decimal(str(options['custom_discount_percentage']))
            if options.get('custom_discount_amount'):
                assignment_data['custom_discount_amount'] = Decimal(str(options['custom_discount_amount']))
            
            assignment, created = ProductSaleAssignment.objects.get_or_create(
                product=product,
                sale_category=sale,
                defaults=assignment_data
            )
            
            if created:
                products_assigned += 1
                self.stdout.write(f'  ✅ Assigned: {product.name}')
            else:
                # Update existing assignment
                for key, value in assignment_data.items():
                    if key not in ['product', 'sale_category']:
                        setattr(assignment, key, value)
                assignment.save()
                self.stdout.write(f'  🔄 Updated: {product.name}')
        
        self.stdout.write(
            self.style.SUCCESS(f'✅ Processed {len(products)} products for sale "{sale.name}"')
        )

    def remove_products_from_sale(self, options):
        """Remove products from a sale category"""
        try:
            sale = SaleCategory.objects.get(slug=options['sale_slug'])
        except SaleCategory.DoesNotExist:
            raise CommandError(f'Sale category with slug "{options["sale_slug"]}" not found')
        
        if options.get('all'):
            # Remove all products
            count = ProductSaleAssignment.objects.filter(sale_category=sale).delete()[0]
            self.stdout.write(
                self.style.SUCCESS(f'✅ Removed all {count} products from sale "{sale.name}"')
            )
        elif options.get('product_ids'):
            # Remove specific products
            assignments = ProductSaleAssignment.objects.filter(
                sale_category=sale,
                product__id__in=options['product_ids']
            )
            count = assignments.count()
            assignments.delete()
            self.stdout.write(
                self.style.SUCCESS(f'✅ Removed {count} products from sale "{sale.name}"')
            )

    def create_campaign(self, options):
        """Create a sale campaign"""
        campaign_data = {
            'name': options['name'],
            'status': 'draft',
            'is_featured': options.get('featured', False),
        }
        
        if options.get('description'):
            campaign_data['description'] = options['description']
        if options.get('target_revenue'):
            campaign_data['target_revenue'] = Decimal(str(options['target_revenue']))
        if options.get('budget'):
            campaign_data['budget'] = Decimal(str(options['budget']))
        
        # Handle dates
        if options.get('start_date'):
            campaign_data['start_date'] = timezone.datetime.strptime(
                options['start_date'], '%Y-%m-%d %H:%M:%S'
            ).replace(tzinfo=timezone.get_current_timezone())
        else:
            campaign_data['start_date'] = timezone.now()
            
        if options.get('end_date'):
            campaign_data['end_date'] = timezone.datetime.strptime(
                options['end_date'], '%Y-%m-%d %H:%M:%S'
            ).replace(tzinfo=timezone.get_current_timezone())
        
        campaign = SaleCampaign.objects.create(**campaign_data)
        
        # Add sale categories
        if options.get('sale_slugs'):
            sales = SaleCategory.objects.filter(slug__in=options['sale_slugs'])
            campaign.sale_categories.set(sales)
            self.stdout.write(f'   Added {sales.count()} sale categories to campaign')
        
        self.stdout.write(
            self.style.SUCCESS(f'✅ Created campaign: {campaign.name} (slug: {campaign.slug})')
        )

    def list_sale_categories(self, options):
        """List all sale categories with filters"""
        sales = SaleCategory.objects.all()
        
        if options.get('active_only'):
            sales = sales.filter(is_active=True)
        if options.get('featured_only'):
            sales = sales.filter(is_featured=True)
        if options.get('type'):
            sales = sales.filter(sale_type=options['type'])
        
        self.stdout.write(self.style.SUCCESS('🏷️ Sale Categories:'))
        self.stdout.write('=' * 80)
        
        for sale in sales:
            status = '🟢 Active' if sale.is_currently_active() else '🔴 Inactive'
            featured = '⭐ Featured' if sale.is_featured else ''
            product_count = ProductSaleAssignment.objects.filter(
                sale_category=sale, is_active=True
            ).count()
            
            self.stdout.write(f'\n{sale.name} ({sale.slug})')
            self.stdout.write(f'  Type: {sale.get_sale_type_display()}')
            self.stdout.write(f'  Badge: {sale.badge_text} ({sale.badge_color})')
            self.stdout.write(f'  Priority: {sale.priority}/5')
            self.stdout.write(f'  Status: {status} {featured}')
            self.stdout.write(f'  Products: {product_count}')
            self.stdout.write(f'  Uses: {sale.current_uses}')
            if sale.discount_percentage:
                self.stdout.write(f'  Discount: {sale.discount_percentage}%')
            if sale.discount_amount:
                self.stdout.write(f'  Discount: ${sale.discount_amount}')

    def show_analytics(self, options):
        """Show sales analytics"""
        days = options.get('days', 30)
        start_date = timezone.now() - timedelta(days=days)
        
        if options.get('sale_slug'):
            try:
                sale = SaleCategory.objects.get(slug=options['sale_slug'])
                self.stdout.write(self.style.SUCCESS(f'📊 Analytics for "{sale.name}" (Last {days} days):'))
                # Add specific sale analytics here
            except SaleCategory.DoesNotExist:
                raise CommandError(f'Sale category with slug "{options["sale_slug"]}" not found')
        else:
            self.stdout.write(self.style.SUCCESS(f'📊 Overall Sales Analytics (Last {days} days):'))
            
            total_sales = SaleCategory.objects.count()
            active_sales = SaleCategory.objects.filter(is_active=True).count()
            featured_sales = SaleCategory.objects.filter(is_featured=True).count()
            total_assignments = ProductSaleAssignment.objects.filter(is_active=True).count()
            
            self.stdout.write(f'   Total Sale Categories: {total_sales}')
            self.stdout.write(f'   Active Sales: {active_sales}')
            self.stdout.write(f'   Featured Sales: {featured_sales}')
            self.stdout.write(f'   Product Assignments: {total_assignments}')

    def bulk_update_sales(self, options):
        """Perform bulk operations on sales"""
        if options.get('activate_all'):
            updated = SaleCategory.objects.update(is_active=True)
            self.stdout.write(self.style.SUCCESS(f'✅ Activated {updated} sale categories'))
        
        if options.get('deactivate_all'):
            updated = SaleCategory.objects.update(is_active=False)
            self.stdout.write(self.style.SUCCESS(f'✅ Deactivated {updated} sale categories'))
        
        if options.get('cleanup_expired'):
            now = timezone.now()
            expired_sales = SaleCategory.objects.filter(
                is_active=True,
                end_date__lt=now
            )
            count = expired_sales.count()
            expired_sales.update(is_active=False)
            self.stdout.write(self.style.SUCCESS(f'✅ Deactivated {count} expired sales'))
