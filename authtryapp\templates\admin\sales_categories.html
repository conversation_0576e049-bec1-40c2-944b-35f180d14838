{% extends 'admin/base_admin.html' %}
{% load static %}

{% block title %}Sales Categories{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'admin/css/sales_management_admin.css' %}">
<style>
.sales-categories-page {
    padding: 20px;
}

.filters-section {
    background: white;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.filters-row {
    display: flex;
    gap: 15px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.filter-group select,
.filter-group input {
    width: 100%;
    padding: 8px 12px;
    border: 2px solid #dee2e6;
    border-radius: 6px;
    font-size: 14px;
}

.filter-group select:focus,
.filter-group input:focus {
    border-color: #667eea;
    outline: none;
}

.filter-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.sales-table-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.sales-table {
    width: 100%;
    border-collapse: collapse;
}

.sales-table th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.sales-table td {
    padding: 15px 12px;
    border-bottom: 1px solid #dee2e6;
    vertical-align: middle;
}

.sales-table tr:hover {
    background: #f8f9fa;
}

.sale-badge-preview {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: bold;
    color: white;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.sale-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 3px;
}

.sale-description {
    color: #6c757d;
    font-size: 13px;
    margin: 0;
}

.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-badge.active {
    background: #d4edda;
    color: #155724;
}

.status-badge.inactive {
    background: #f8d7da;
    color: #721c24;
}

.status-badge.featured {
    background: #fff3cd;
    color: #856404;
}

.priority-indicator {
    display: flex;
    gap: 2px;
}

.priority-star {
    width: 12px;
    height: 12px;
    background: #dee2e6;
    border-radius: 50%;
}

.priority-star.filled {
    background: #ffc107;
}

.actions-cell {
    white-space: nowrap;
}

.action-btn {
    display: inline-block;
    padding: 6px 12px;
    margin: 0 2px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.2s ease;
}

.action-btn.edit {
    background: #007bff;
    color: white;
}

.action-btn.edit:hover {
    background: #0056b3;
    color: white;
    text-decoration: none;
}

.action-btn.products {
    background: #28a745;
    color: white;
}

.action-btn.products:hover {
    background: #1e7e34;
    color: white;
    text-decoration: none;
}

.action-btn.delete {
    background: #dc3545;
    color: white;
}

.action-btn.delete:hover {
    background: #c82333;
    color: white;
    text-decoration: none;
}

.pagination-container {
    padding: 20px;
    text-align: center;
}

.add-sale-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.add-sale-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    color: white;
    text-decoration: none;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.5;
}
</style>
{% endblock %}

{% block content %}
<div class="sales-categories-page">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>🏷️ Sales Categories</h1>
        <a href="{% url 'admin_sale_category_add' %}" class="add-sale-btn">
            <i class="fas fa-plus"></i>
            Create New Sale
        </a>
    </div>

    <!-- Filters Section -->
    <div class="filters-section">
        <form method="get" class="filters-form">
            <div class="filters-row">
                <div class="filter-group">
                    <label for="search">Search Sales</label>
                    <input type="text" id="search" name="search" value="{{ search_query }}" 
                           placeholder="Search by name, badge text, or description...">
                </div>
                <div class="filter-group">
                    <label for="type">Sale Type</label>
                    <select id="type" name="type">
                        <option value="">All Types</option>
                        {% for value, label in sale_type_choices %}
                        <option value="{{ value }}" {% if current_type == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="filter-group">
                    <label for="status">Status</label>
                    <select id="status" name="status">
                        <option value="">All Status</option>
                        <option value="active" {% if current_status == 'active' %}selected{% endif %}>Active Only</option>
                        <option value="inactive" {% if current_status == 'inactive' %}selected{% endif %}>Inactive Only</option>
                        <option value="featured" {% if current_status == 'featured' %}selected{% endif %}>Featured Only</option>
                    </select>
                </div>
                <div class="filter-group">
                    <button type="submit" class="filter-btn">
                        <i class="fas fa-search"></i>
                        Filter
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Sales Table -->
    <div class="sales-table-container">
        {% if sales %}
        <table class="sales-table">
            <thead>
                <tr>
                    <th>Badge Preview</th>
                    <th>Sale Details</th>
                    <th>Type</th>
                    <th>Priority</th>
                    <th>Status</th>
                    <th>Usage</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for sale in sales %}
                <tr>
                    <td>
                        <span class="sale-badge-preview" style="background-color: {{ sale.badge_color }};">
                            {{ sale.badge_text }}
                        </span>
                    </td>
                    <td>
                        <div class="sale-name">{{ sale.name }}</div>
                        {% if sale.description %}
                        <p class="sale-description">{{ sale.description|truncatewords:10 }}</p>
                        {% endif %}
                        {% if sale.discount_percentage %}
                        <small class="text-success"><strong>{{ sale.discount_percentage }}% off</strong></small>
                        {% elif sale.discount_amount %}
                        <small class="text-success"><strong>${{ sale.discount_amount }} off</strong></small>
                        {% endif %}
                    </td>
                    <td>{{ sale.get_sale_type_display }}</td>
                    <td>
                        <div class="priority-indicator">
                            {% for i in "12345" %}
                            <div class="priority-star {% if forloop.counter <= sale.priority %}filled{% endif %}"></div>
                            {% endfor %}
                        </div>
                    </td>
                    <td>
                        {% if sale.is_active %}
                        <span class="status-badge active">Active</span>
                        {% else %}
                        <span class="status-badge inactive">Inactive</span>
                        {% endif %}
                        {% if sale.is_featured %}
                        <span class="status-badge featured">Featured</span>
                        {% endif %}
                    </td>
                    <td>
                        <div>{{ sale.current_uses }} uses</div>
                        {% if sale.max_total_uses %}
                        <small class="text-muted">/ {{ sale.max_total_uses }} max</small>
                        {% endif %}
                    </td>
                    <td class="actions-cell">
                        <a href="{% url 'admin_sale_category_edit' sale.id %}" class="action-btn edit" title="Edit Sale">
                            <i class="fas fa-edit"></i>
                        </a>
                        <a href="{% url 'admin_sale_category_products' sale.id %}" class="action-btn products" title="Manage Products">
                            <i class="fas fa-box"></i>
                        </a>
                        <a href="{% url 'admin_sale_category_delete' sale.id %}" class="action-btn delete" title="Delete Sale">
                            <i class="fas fa-trash"></i>
                        </a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- Pagination -->
        {% if sales.has_other_pages %}
        <div class="pagination-container">
            <nav aria-label="Sales pagination">
                <ul class="pagination justify-content-center">
                    {% if sales.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ sales.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_type %}&type={{ current_type }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}">Previous</a>
                    </li>
                    {% endif %}

                    {% for num in sales.paginator.page_range %}
                    {% if sales.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% elif num > sales.number|add:'-3' and num < sales.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_type %}&type={{ current_type }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}

                    {% if sales.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ sales.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_type %}&type={{ current_type }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}">Next</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}

        {% else %}
        <div class="empty-state">
            <i class="fas fa-tags"></i>
            <h3>No Sales Found</h3>
            <p>{% if search_query or current_type or current_status %}No sales match your current filters.{% else %}You haven't created any sales yet.{% endif %}</p>
            <a href="{% url 'admin_sale_category_add' %}" class="add-sale-btn">
                <i class="fas fa-plus"></i>
                Create Your First Sale
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit form on filter change
    const filterSelects = document.querySelectorAll('#type, #status');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });
    
    // Confirm delete actions
    const deleteButtons = document.querySelectorAll('.action-btn.delete');
    deleteButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            if (!confirm('Are you sure you want to delete this sale? This action cannot be undone.')) {
                e.preventDefault();
            }
        });
    });
});
</script>
{% endblock %}
