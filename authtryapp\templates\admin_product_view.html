{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>{{ product.name }} - Admin Dashboard - iTechStore</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        /* Admin Dashboard Styles */
        .admin-container {
            display: flex;
            min-height: calc(100vh - 150px);
        }

        .admin-sidebar {
            width: 250px;
            background-color: #1a1a1a;
            color: white;
            padding: 20px 0;
            flex-shrink: 0;
        }

        .admin-logo {
            padding: 0 20px 20px;
            border-bottom: 1px solid #333;
            margin-bottom: 20px;
        }

        .admin-logo h2 {
            font-size: 20px;
            display: flex;
            align-items: center;
        }

        .admin-logo i {
            margin-right: 10px;
            color: #4dabf7;
        }

        .admin-menu {
            list-style: none;
        }

        .admin-menu li {
            margin-bottom: 5px;
        }

        .admin-menu a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: #aaa;
            transition: all 0.3s;
        }

        .admin-menu a:hover, .admin-menu a.active {
            background-color: #333;
            color: white;
        }

        .admin-menu i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .admin-content {
            flex: 1;
            padding: 30px;
            background-color: #f5f7fa;
            overflow-y: auto;
        }

        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .admin-title h1 {
            font-size: 24px;
            color: #333;
        }

        .admin-user {
            display: flex;
            align-items: center;
        }

        .admin-user-info {
            margin-right: 15px;
            text-align: right;
        }

        .admin-user-name {
            font-weight: 500;
            color: #333;
        }

        .admin-user-role {
            font-size: 12px;
            color: #777;
        }

        .admin-user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #4dabf7;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
        }

        .admin-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 30px;
        }

        .admin-card-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-card-title {
            font-size: 18px;
            color: #333;
            font-weight: 500;
        }

        .admin-card-actions a {
            padding: 8px 15px;
            background-color: #4dabf7;
            color: white;
            border-radius: 5px;
            font-size: 14px;
            transition: background-color 0.3s;
            margin-left: 10px;
        }

        .admin-card-actions a:hover {
            background-color: #0c8599;
        }

        .admin-card-actions a.delete-btn {
            background-color: #f44336;
        }

        .admin-card-actions a.delete-btn:hover {
            background-color: #d32f2f;
        }

        .admin-card-body {
            padding: 20px;
        }

        /* Product View Styles */
        .product-view {
            display: flex;
            gap: 30px;
        }

        .product-image-container {
            flex: 1;
            max-width: 400px;
        }

        .product-image {
            width: 100%;
            height: 300px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .product-details {
            flex: 2;
        }

        .product-title {
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
        }

        .product-price {
            font-size: 20px;
            font-weight: 700;
            color: #4dabf7;
            margin-bottom: 20px;
        }

        .product-status {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 20px;
        }

        .status-available {
            background-color: #e8f5e9;
            color: #4caf50;
        }

        .status-preorder {
            background-color: #fff3e0;
            color: #ff9800;
        }

        .status-unavailable {
            background-color: #ffebee;
            color: #f44336;
        }

        .product-description {
            margin-bottom: 30px;
            line-height: 1.8;
            color: #555;
        }

        .product-meta {
            background-color: #f5f7fa;
            padding: 15px;
            border-radius: 5px;
        }

        .meta-item {
            display: flex;
            margin-bottom: 10px;
        }

        .meta-label {
            width: 120px;
            font-weight: 500;
            color: #333;
        }

        .meta-value {
            flex: 1;
            color: #555;
        }

        @media (max-width: 992px) {
            .admin-container {
                flex-direction: column;
            }

            .admin-sidebar {
                width: 100%;
                padding: 10px 0;
            }

            .admin-logo {
                padding: 0 15px 15px;
                margin-bottom: 10px;
            }

            .admin-menu a {
                padding: 10px 15px;
            }

            .product-view {
                flex-direction: column;
            }

            .product-image-container {
                max-width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- Top Navigation Bar -->
    <div class="top-nav">
        <div class="container">
            <div class="top-nav-left">
                <span>Admin Panel</span>
            </div>
            <div class="top-nav-right">
                <span>Welcome, {{ request.user.username }}</span>
                <a href="{% url 'admin_logout' %}" class="sign-in">Logout</a>
            </div>
        </div>
    </div>

    <!-- Admin Container -->
    <div class="admin-container">
        <!-- Admin Sidebar -->
        <div class="admin-sidebar">
            <div class="admin-logo">
                <h2><i class="fas fa-tachometer-alt"></i> Admin Panel</h2>
            </div>
            <ul class="admin-menu">
                <li><a href="{% url 'admin_dashboard' %}"><i class="fas fa-home"></i> Dashboard</a></li>
                <li><a href="{% url 'admin_products' %}" class="active"><i class="fas fa-box"></i> Products</a></li>
                <li><a href="{% url 'admin_users' %}"><i class="fas fa-users"></i> Users</a></li>
                <li><a href="{% url 'admin_orders' %}"><i class="fas fa-shopping-cart"></i> Orders</a></li>
                <li><a href="{% url 'admin_categories' %}"><i class="fas fa-tags"></i> Categories</a></li>
                <li><a href="{% url 'admin_settings' %}"><i class="fas fa-cog"></i> Settings</a></li>
                <li><a href="/django-admin/" target="_blank"><i class="fas fa-tools"></i> Django Admin</a></li>
                <li><a href="{% url 'home' %}"><i class="fas fa-store"></i> View Store</a></li>
                <li><a href="{% url 'admin_logout' %}"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
            </ul>
        </div>

        <!-- Admin Content -->
        <div class="admin-content">
            <div class="admin-header">
                <div class="admin-title">
                    <h1>Product Details</h1>
                </div>
                <div class="admin-user">
                    <div class="admin-user-info">
                        <div class="admin-user-name">{{ request.user.username }}</div>
                        <div class="admin-user-role">Administrator</div>
                    </div>
                    <div class="admin-user-avatar">
                        {{ request.user.username|first|upper }}
                    </div>
                </div>
            </div>

            <!-- Product View -->
            <div class="admin-card">
                <div class="admin-card-header">
                    <div class="admin-card-title">{{ product.name }}</div>
                    <div class="admin-card-actions">
                        <a href="{% url 'admin_product_edit' product.id %}"><i class="fas fa-edit"></i> Edit</a>
                        <a href="{% url 'admin_product_delete' product.id %}" class="delete-btn"><i class="fas fa-trash"></i> Delete</a>
                    </div>
                </div>
                <div class="admin-card-body">
                    <div class="product-view">
                        <div class="product-image-container">
                            <div class="product-image">
                                {% if product.image %}
                                    <img src="{{ product.image.url }}" alt="{{ product.name }}">
                                {% else %}
                                    <img src="https://via.placeholder.com/400x300?text=No+Image" alt="{{ product.name }}">
                                {% endif %}
                            </div>
                        </div>
                        <div class="product-details">
                            <h1 class="product-title">{{ product.name }}</h1>
                            <div class="product-price">${{ product.price }}</div>

                            {% if product.stock_status == 'available' %}
                                <div class="product-status status-available">Available</div>
                            {% elif product.stock_status == 'preorder' %}
                                <div class="product-status status-preorder">Pre-order</div>
                            {% else %}
                                <div class="product-status status-unavailable">Unavailable</div>
                            {% endif %}

                            <div class="product-description">
                                {{ product.description|linebreaks }}
                            </div>

                            <div class="product-meta">
                                <div class="meta-item">
                                    <div class="meta-label">Product ID:</div>
                                    <div class="meta-value">{{ product.id }}</div>
                                </div>
                                <div class="meta-item">
                                    <div class="meta-label">Created:</div>
                                    <div class="meta-value">{{ product.created_at|date:"F d, Y" }}</div>
                                </div>
                                <div class="meta-item">
                                    <div class="meta-label">Last Updated:</div>
                                    <div class="meta-value">{{ product.created_at|date:"F d, Y" }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{% static 'js/script.js' %}"></script>
</body>
</html>
