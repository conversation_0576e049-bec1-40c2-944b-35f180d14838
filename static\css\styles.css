/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    color: #333;
    line-height: 1.6;
    background-color: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

a {
    text-decoration: none;
    color: inherit;
}

ul {
    list-style: none;
}

/* Top Navigation Bar */
.top-nav {
    background-color: #fff;
    color: #333;
    padding: 8px 0;
    font-size: 14px;
    border-bottom: 1px solid #eee;
}

.top-nav .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.top-nav-left {
    display: flex;
    align-items: center;
}

.top-nav-left i {
    margin-left: 5px;
    font-size: 12px;
}

.top-nav-right a {
    margin-left: 20px;
    transition: color 0.3s;
    color: #333;
}

.top-nav-right a:hover {
    color: #0070f3;
}

/* Main Navigation */
.main-nav {
    background-color: white;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    padding: 15px 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.main-nav .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo a {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.logo h1 {
    font-size: 24px;
    font-weight: 700;
    letter-spacing: -0.5px;
}

.i-text {
    color: #0070f3;
}

.tech-text {
    color: #333;
}

.store-text {
    color: #0070f3;
}

.tagline {
    font-size: 12px;
    color: #777;
    margin-top: -5px;
}

.nav-links ul {
    display: flex;
}

.nav-links li {
    margin: 0 15px;
    position: relative;
}

.nav-links a {
    font-weight: 500;
    padding: 5px 0;
    position: relative;
    transition: color 0.3s;
    color: #333;
}

.nav-links a:hover, .nav-links a.active {
    color: #0070f3;
}

.nav-links a.active::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #0070f3;
}

.dropdown-content {
    display: none;
    position: absolute;
    background-color: white;
    min-width: 200px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    z-index: 1;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 10px;
    border: 1px solid #eee;
}

.dropdown-content a {
    color: #333;
    padding: 12px 16px;
    display: block;
    transition: background-color 0.3s;
    font-size: 14px;
}

.dropdown-content a:hover {
    background-color: #f9f9f9;
    color: #0070f3;
}

.dropdown:hover .dropdown-content {
    display: block;
}

.nav-icons {
    display: flex;
    align-items: center;
}

.nav-icons a {
    margin-left: 20px;
    font-size: 18px;
    position: relative;
    color: #333;
}

.nav-icons a:hover {
    color: #0070f3;
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: #0070f3;
    color: white;
    font-size: 12px;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Search Container */
.search-container {
    position: relative;
    margin-right: 15px;
}

#search-form {
    display: flex;
    align-items: center;
}

#search-input {
    width: 200px;
    padding: 8px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    font-size: 14px;
    transition: all 0.3s;
    outline: none;
}

#search-input:focus {
    border-color: #0070f3;
    box-shadow: 0 0 0 2px rgba(0, 112, 243, 0.2);
    width: 250px;
}

.search-btn {
    background: none;
    border: none;
    color: #666;
    font-size: 16px;
    cursor: pointer;
    margin-left: -35px;
    transition: color 0.3s;
}

.search-btn:hover {
    color: #0070f3;
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 100;
    max-height: 300px;
    overflow-y: auto;
    display: none;
}

.search-results.active {
    display: block;
}

.search-result-item {
    padding: 10px 15px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.3s;
}

.search-result-item:hover {
    background-color: #f9f9f9;
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-item .product-name {
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
}

.search-result-item .product-price {
    color: #0070f3;
    font-weight: 600;
    font-size: 14px;
}

.search-result-item .product-category {
    color: #777;
    font-size: 12px;
}

/* Hero Section */
.hero-section {
    background-color: #f7f9fc;
    padding: 50px 0;
    margin: 20px 0;
    position: relative;
    overflow: hidden;
}

.hero-slider {
    position: relative;
    width: 100%;
    height: 500px;
    overflow: hidden;
    margin-bottom: 20px;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.8s ease, transform 0.8s ease;
    visibility: hidden;
    transform: translateX(100%);
    overflow: hidden;
}

.hero-slide.active {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
    z-index: 1;
}

.hero-slide.prev {
    transform: translateX(-100%);
}

.hero-slide .hero-image img {
    transition: transform 8s ease, scale 0.8s ease;
}

.hero-slide.active .hero-image img {
    transform: scale(1.05);
}

.hero-slide.zoom-out .hero-image img {
    transform: scale(1.2);
}

.hero-slide.fade-in {
    opacity: 0;
    transform: translateX(0);
}

.hero-slide.fade-in.active {
    opacity: 1;
}

.hero-slide.slide-up {
    transform: translateY(100%);
}

.hero-slide.slide-up.active {
    transform: translateY(0);
}

.hero-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.hero-text {
    flex: 1;
    padding-right: 50px;
    z-index: 2;
}

.hero-text h1 {
    font-size: 42px;
    font-weight: 700;
    color: #333;
    line-height: 1.2;
    margin-bottom: 5px;
}

.hero-text h2 {
    font-size: 54px;
    font-weight: 800;
    color: #0070f3;
    margin-bottom: 15px;
}

.hero-text p {
    font-size: 18px;
    color: #555;
    margin-bottom: 25px;
    line-height: 1.5;
    max-width: 500px;
}

.order-btn {
    display: inline-block;
    background-color: #0070f3;
    color: white;
    padding: 12px 30px;
    border-radius: 5px;
    font-weight: 500;
    transition: background-color 0.3s, transform 0.3s;
}

.order-btn:hover {
    background-color: #0060df;
    transform: translateY(-2px);
}

.hero-image {
    flex: 1;
    height: 350px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.hero-image img {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    object-fit: contain;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    display: block;
    margin: 0 auto;
}

.hero-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    z-index: 10;
    padding: 0 20px;
    pointer-events: none;
}

.hero-controls button {
    background-color: rgba(255, 255, 255, 0.9);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s;
    pointer-events: auto;
}

.hero-controls button:hover {
    background-color: #0070f3;
    color: white;
    transform: translateY(-2px);
}

/* Slide counter styles removed */

.hero-dots {
    display: flex;
    justify-content: center;
    position: absolute;
    bottom: 30px;
    left: 0;
    right: 0;
    z-index: 10;
    background-color: rgba(0, 0, 0, 0.6);
    padding: 12px 20px;
    border-radius: 30px;
    width: fit-content;
    margin: 0 auto;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.dot {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    margin: 0 8px;
    cursor: pointer;
    transition: all 0.3s;
    border: 2px solid white;
    position: relative;
}

.dot:hover {
    background-color: rgba(255, 255, 255, 0.8);
    transform: scale(1.2);
}

.dot.active {
    background-color: #0070f3;
    transform: scale(1.3);
    border-color: white;
    box-shadow: 0 0 10px rgba(0, 112, 243, 0.5);
}

.dot::after {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-radius: 50%;
    border: 2px solid transparent;
    transition: all 0.3s;
}

.dot.active::after {
    border-color: rgba(0, 112, 243, 0.3);
}

/* Dot number styles removed */

/* Effect Controls */
.effect-controls {
    position: absolute;
    bottom: 20px;
    right: 20px;
    z-index: 10;
}

.effect-btn {
    background-color: rgba(255, 255, 255, 0.9);
    border: none;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.effect-btn:hover {
    background-color: #0070f3;
    color: white;
}

.effect-options {
    position: absolute;
    right: 0;
    bottom: 45px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 10px;
    display: none;
    flex-direction: column;
    width: 150px;
}

.effect-options button {
    background: none;
    border: none;
    padding: 8px 15px;
    text-align: left;
    cursor: pointer;
    transition: background-color 0.3s;
    border-radius: 5px;
    margin: 2px 0;
}

.effect-options button:hover {
    background-color: #f5f5f5;
    color: #0070f3;
}

.effect-controls:hover .effect-options {
    display: flex;
}

/* Featured Products */
.featured-products {
    padding: 60px 0;
}

.section-title {
    text-align: center;
    font-size: 32px;
    margin-bottom: 40px;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: #0070f3;
}

.category-description {
    text-align: center;
    color: #666;
    margin-bottom: 30px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    font-size: 16px;
    line-height: 1.6;
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 30px;
}

.product-card {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s, box-shadow 0.3s;
    display: flex;
    flex-direction: column;
    height: 100%;
    border: 1px solid #eee;
}

.product-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.product-image {
    height: 180px;
    overflow: hidden;
    position: relative;
    background-color: #fff;
    padding: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-image img {
    max-width: 100%;
    max-height: 150px;
    object-fit: contain;
    transition: transform 0.3s;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

/* Stock Status Labels */
.stock-status {
    position: absolute;
    top: 10px;
    left: 10px;
    padding: 4px 10px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    z-index: 1;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stock-status.available {
    background-color: #e6f7ee;
    color: #0a8043;
}

.stock-status.preorder {
    background-color: #fff4e5;
    color: #e67700;
}

.stock-status.notavailable {
    background-color: #ffebee;
    color: #d32f2f;
}

.product-info {
    padding: 15px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    border-top: 1px solid #f5f5f5;
}

.product-info h3 {
    font-size: 14px;
    margin-bottom: 8px;
    font-weight: 500;
    line-height: 1.4;
    color: #333;
    height: 40px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.price {
    color: #0070f3;
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 12px;
}

.starting-from {
    font-size: 11px;
    color: #777;
    margin-bottom: 3px;
    display: block;
}

.product-buttons {
    display: flex;
    gap: 8px;
    margin-top: auto;
}

.add-to-cart {
    flex: 2;
    padding: 8px 12px;
    background-color: #0070f3;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s;
    font-size: 13px;
}

.add-to-cart:hover {
    background-color: #0060df;
}

.view-cart-btn {
    flex: 1;
    padding: 8px 12px;
    background-color: #f5f7fa;
    color: #333;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    text-align: center;
    font-weight: 500;
    transition: all 0.3s;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13px;
}

.view-cart-btn:hover {
    background-color: #f0f0f0;
    border-color: #ccc;
}

/* Pre-order button */
.add-to-cart.preorder-btn {
    background-color: #e67700;
}

.add-to-cart.preorder-btn:hover {
    background-color: #d26900;
}

/* Disabled button */
.add-to-cart.disabled {
    background-color: #ccc;
    cursor: not-allowed;
    opacity: 0.7;
}

.add-to-cart.disabled:hover {
    background-color: #ccc;
}

/* Footer */
footer {
    background-color: #f7f9fc;
    color: #333;
    padding: 60px 0 20px;
    border-top: 1px solid #eee;
}

.footer-columns {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.footer-column h3 {
    font-size: 16px;
    margin-bottom: 20px;
    position: relative;
    font-weight: 600;
}

.footer-column h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 30px;
    height: 2px;
    background-color: #0070f3;
}

.footer-column ul li {
    margin-bottom: 10px;
}

.footer-column ul li a {
    color: #555;
    transition: color 0.3s;
    font-size: 14px;
}

.footer-column ul li a:hover {
    color: #0070f3;
}

.social-icons {
    display: flex;
    margin-bottom: 20px;
}

.social-icons a {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 36px;
    height: 36px;
    background-color: #333;
    border-radius: 50%;
    margin-right: 10px;
    transition: background-color 0.3s;
}

.social-icons a:hover {
    background-color: #4dabf7;
}

.newsletter h4 {
    margin-bottom: 15px;
    font-size: 16px;
}

.newsletter form {
    display: flex;
}

.newsletter input {
    flex: 1;
    padding: 10px;
    border: none;
    border-radius: 4px 0 0 4px;
}

.newsletter button {
    background-color: #4dabf7;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    transition: background-color 0.3s;
}

.newsletter button:hover {
    background-color: #0c8599;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #333;
    color: #aaa;
    font-size: 14px;
}

/* Mobile Menu Styles */
.mobile-menu-toggle {
    display: none;
    font-size: 24px;
    cursor: pointer;
}

.mobile-menu {
    position: fixed;
    top: 0;
    left: -100%;
    width: 80%;
    max-width: 320px;
    height: 100vh;
    background-color: white;
    z-index: 1000;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
    transition: left 0.3s ease-in-out;
    display: flex;
    flex-direction: column;
}

.mobile-menu.active {
    left: 0;
}

.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.mobile-menu-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
}

.mobile-search {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    position: relative;
}

#mobile-search-form {
    display: flex;
    align-items: center;
}

#mobile-search-input {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    font-size: 14px;
    outline: none;
}

#mobile-search-input:focus {
    border-color: #0070f3;
    box-shadow: 0 0 0 2px rgba(0, 112, 243, 0.2);
}

.mobile-search-btn {
    background: none;
    border: none;
    color: #666;
    font-size: 16px;
    cursor: pointer;
    margin-left: -35px;
    transition: color 0.3s;
}

.mobile-search-btn:hover {
    color: #0070f3;
}

.mobile-menu-links {
    flex: 1;
    overflow-y: auto;
    padding: 20px 0;
}

.mobile-menu-links li {
    padding: 10px 20px;
    border-bottom: 1px solid #f5f5f5;
}

.mobile-menu-links li a {
    display: block;
    font-weight: 500;
}

.mobile-dropdown {
    position: relative;
}

.mobile-dropdown > a {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.mobile-dropdown-content {
    display: none;
    padding-left: 20px;
    margin-top: 10px;
}

.mobile-dropdown-content.active {
    display: block;
}

.mobile-dropdown-content li {
    border-bottom: none;
    padding: 8px 0;
}

.mobile-menu-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.mobile-account-link {
    display: block;
    padding: 10px;
    background-color: #f5f7fa;
    border-radius: 4px;
    text-align: center;
    font-weight: 500;
    transition: background-color 0.3s;
}

.mobile-account-link:hover {
    background-color: #e9ecef;
}

.overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
}

.overlay.active {
    display: block;
}

/* Responsive Design */
@media (max-width: 992px) {
    .hero-slider {
        height: 700px;
    }

    .hero-content {
        flex-direction: column;
        padding: 20px;
    }

    .hero-text {
        text-align: center;
        padding-right: 0;
        margin-bottom: 30px;
        order: 2;
        z-index: 2;
        width: 100%;
    }

    .hero-image {
        text-align: center;
        order: 1;
        margin-bottom: 20px;
        height: 350px;
        width: 100%;
    }

    .hero-image img {
        max-height: 300px;
        max-width: 90%;
        object-position: center;
        margin: 0 auto;
    }

    .hero-text h1 {
        font-size: 32px;
    }

    .hero-text h2 {
        font-size: 42px;
    }

    .hero-text p {
        font-size: 16px;
        margin-left: auto;
        margin-right: auto;
    }

    .hero-controls {
        justify-content: space-between;
        padding: 0 20px;
    }
}

@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .product-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-columns {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Floating Cart Button */
.floating-cart {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background-color: #0070f3;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 99;
    transition: transform 0.3s, background-color 0.3s;
}

.floating-cart:hover {
    transform: scale(1.05);
    background-color: #0060df;
}

.floating-cart i {
    font-size: 20px;
}

.floating-cart .floating-cart-count {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #e53935;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 11px;
    font-weight: 600;
}

@media (max-width: 576px) {
    .product-grid {
        grid-template-columns: 1fr;
    }

    .hero-slider {
        height: 600px;
    }

    .hero-image {
        height: 250px;
    }

    .hero-image img {
        max-height: 220px;
        max-width: 100%;
    }

    .hero-content {
        padding: 10px;
    }

    .hero-text h1 {
        font-size: 28px;
    }

    .hero-text h2 {
        font-size: 36px;
    }

    .hero-text p {
        font-size: 14px;
    }

    .hero-controls button {
        width: 40px;
        height: 40px;
    }

    .footer-columns {
        grid-template-columns: 1fr;
    }

    .floating-cart {
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
    }
}
