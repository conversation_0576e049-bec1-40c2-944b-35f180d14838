#!/usr/bin/env python
"""
Test script to verify navigation visibility and functionality
"""
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'authtry.settings')
django.setup()

from authtryapp.models import HeroBanner

def test_navigation_visibility():
    print("🔍 Testing Navigation Visibility...")
    print("=" * 50)
    
    # Get active banners
    active_banners = HeroBanner.objects.filter(is_active=True).order_by('order')
    total_banners = active_banners.count()
    
    print(f"📊 Active Banners: {total_banners}")
    
    if total_banners == 0:
        print("❌ No active banners - Navigation will be hidden")
        return False
    elif total_banners == 1:
        print("⚠️  Only 1 banner - Navigation may be hidden (check CSS)")
        banner = active_banners.first()
        print(f"   Single Banner: {banner.title}")
        return False
    else:
        print("✅ Multiple banners - Navigation should be visible")
        
        print("\n📋 Banner List (in display order):")
        for i, banner in enumerate(active_banners, 1):
            print(f"   {i}. {banner.title}")
            print(f"      - Order: {banner.order}")
            print(f"      - Link: {banner.get_redirect_url('primary')}")
            print(f"      - Button: {banner.get_cta_button_text('primary')}")
            if banner.has_secondary_button():
                print(f"      - Secondary: {banner.get_cta_button_text('secondary')}")
            print()
        
        print("🎯 Navigation Elements Expected:")
        print("   ✅ Left arrow (‹) - Previous slide")
        print("   ✅ Right arrow (›) - Next slide")
        print(f"   ✅ {total_banners} navigation dots")
        print("   ✅ Auto-slide functionality")
        
        return True

def test_banner_functionality():
    print("\n🧪 Testing Banner Functionality...")
    print("=" * 50)
    
    # Test different link types
    link_types = ['product', 'category', 'anchor', 'custom']
    
    for link_type in link_types:
        banners = HeroBanner.objects.filter(link_type=link_type, is_active=True)
        count = banners.count()
        print(f"📌 {link_type.title()} Links: {count} banners")
        
        if count > 0:
            banner = banners.first()
            url = banner.get_redirect_url('primary')
            print(f"   Example: {banner.title} → {url}")
    
    print("\n🎨 Image Dimension Examples:")
    banners_with_dimensions = HeroBanner.objects.filter(
        is_active=True,
        image_width__isnull=False,
        image_height__isnull=False
    )
    
    for banner in banners_with_dimensions[:3]:
        css = banner.get_image_dimensions_css()
        print(f"   {banner.title}: {css}")

if __name__ == "__main__":
    navigation_ok = test_navigation_visibility()
    test_banner_functionality()
    
    print("\n" + "=" * 50)
    if navigation_ok:
        print("🎉 Navigation should be fully visible and functional!")
        print("💡 Check the homepage to see arrows and dots")
    else:
        print("⚠️  Navigation may be hidden - add more banners if needed")
    
    print("\n📝 Next Steps:")
    print("1. Visit the homepage to see the carousel")
    print("2. Check that arrows and dots are visible")
    print("3. Test clicking arrows to navigate")
    print("4. Test clicking dots to jump to slides")
    print("5. Verify all links work correctly")
