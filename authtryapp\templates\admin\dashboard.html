{% extends 'admin/base_admin.html' %}

{% block title %}Admin Dashboard{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="mt-4">Dashboard</h1>

    <!-- Stats Cards -->
    <div class="row mt-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h5 class="card-title">Products</h5>
                            <h2 class="display-6 mb-0">{{ products_count }}</h2>
                        </div>
                        <div>
                            <i class="fas fa-box fa-3x opacity-50"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="{% url 'admin_products' %}">View Details</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h5 class="card-title">Categories</h5>
                            <h2 class="display-6 mb-0">{{ categories.count }}</h2>
                        </div>
                        <div>
                            <i class="fas fa-tags fa-3x opacity-50"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="{% url 'admin_categories' %}">View Details</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h5 class="card-title">Users</h5>
                            <h2 class="display-6 mb-0">{{ users_count }}</h2>
                        </div>
                        <div>
                            <i class="fas fa-users fa-3x opacity-50"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="{% url 'admin_users' %}">View Details</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h5 class="card-title">Hero Banners</h5>
                            <h2 class="display-6 mb-0">{{ hero_banners_count }}</h2>
                        </div>
                        <div>
                            <i class="fas fa-images fa-3x opacity-50"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="{% url 'admin_hero_banners' %}">Manage Banners</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sales Management Section -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card mb-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none;">
                <div class="card-body text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="card-title mb-2">🏷️ Sales Management</h3>
                            <p class="card-text mb-0">Manage sales, promotions, and marketing campaigns</p>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="{% url 'admin_sales_dashboard' %}" class="btn btn-light">
                                <i class="fas fa-chart-line"></i> Dashboard
                            </a>
                            <a href="{% url 'admin_sales_categories' %}" class="btn btn-outline-light">
                                <i class="fas fa-tags"></i> Manage Sales
                            </a>
                            <a href="{% url 'admin_sale_category_add' %}" class="btn btn-success">
                                <i class="fas fa-plus"></i> Create Sale
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Products -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-box me-1"></i>
                    Recent Products
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Category</th>
                                    <th>Price</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in recent_products %}
                                <tr>
                                    <td>
                                        <a href="{% url 'admin_product_view' product.id %}">
                                            {{ product.name }}
                                        </a>
                                    </td>
                                    <td>
                                        {% if product.category %}
                                        <a href="{% url 'admin_products_by_category' product.category.id %}">
                                            {{ product.category.name }}
                                        </a>
                                        {% else %}
                                        <span class="text-muted">No category</span>
                                        {% endif %}
                                    </td>
                                    <td>${{ product.price|default:"N/A" }}</td>
                                    <td>
                                        <span class="badge {% if product.stock_status == 'available' %}bg-success{% elif product.stock_status == 'preorder' %}bg-warning{% else %}bg-danger{% endif %}">
                                            {{ product.get_stock_status_display }}
                                        </span>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center py-3">
                                        <span class="text-muted">No products yet</span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% if recent_products %}
                    <div class="text-end mt-3">
                        <a href="{% url 'admin_products' %}" class="btn btn-sm btn-outline-primary">View All Products</a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Users -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-users me-1"></i>
                    Recent Users
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Username</th>
                                    <th>Full Name</th>
                                    <th>Email</th>
                                    <th>Joined</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in recent_users %}
                                <tr>
                                    <td>
                                        <a href="{% url 'admin_user_view' user.id %}">
                                            {{ user.username }}
                                        </a>
                                    </td>
                                    <td>{{ user.get_full_name|default:"-" }}</td>
                                    <td>{{ user.email|default:"-" }}</td>
                                    <td>{{ user.date_joined|date:"M d, Y" }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center py-3">
                                        <span class="text-muted">No users yet</span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% if recent_users %}
                    <div class="text-end mt-3">
                        <a href="{% url 'admin_users' %}" class="btn btn-sm btn-outline-primary">View All Users</a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Hero Banner Management -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-images me-1"></i>
                Hero Banner Management
            </div>
            <a href="{% url 'admin_hero_banner_add' %}" class="btn btn-primary btn-sm">
                <i class="fas fa-plus"></i> Add New Banner
            </a>
        </div>
        <div class="card-body">
            {% if recent_banners %}
            <div class="row">
                {% for banner in recent_banners %}
                <div class="col-md-4 mb-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-header p-0 position-relative">
                            <div class="banner-preview" style="height: 120px; background: linear-gradient(135deg, {{ banner.background_gradient_start }} 0%, {{ banner.background_gradient_end }} 100%); border-radius: 8px 8px 0 0;">
                                <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center text-white">
                                    {% if banner.image %}
                                    <img src="{{ banner.image.url }}" alt="{{ banner.image_alt }}" class="img-fluid" style="max-height: 80px; max-width: 80px; object-fit: contain;">
                                    {% else %}
                                    <i class="fas fa-image fa-2x opacity-50"></i>
                                    {% endif %}
                                </div>
                                <div class="position-absolute top-0 end-0 p-2">
                                    <span class="badge {% if banner.is_active %}bg-success{% else %}bg-secondary{% endif %}">
                                        {% if banner.is_active %}Active{% else %}Inactive{% endif %}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <h6 class="card-title mb-2">{{ banner.title|truncatechars:30 }}</h6>
                            {% if banner.brand_text %}
                            <small class="text-muted d-block mb-1">{{ banner.brand_text }}</small>
                            {% endif %}
                            <p class="card-text small text-muted">{{ banner.subtitle|truncatechars:50 }}</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">Order: {{ banner.display_order }}</small>
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'admin_hero_banner_edit' banner.id %}" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'admin_hero_banner_delete' banner.id %}" class="btn btn-outline-danger btn-sm">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            <div class="text-end mt-3">
                <a href="{% url 'admin_hero_banners' %}" class="btn btn-outline-primary">
                    <i class="fas fa-images"></i> Manage All Banners
                </a>
                <a href="{% url 'admin_site_config' %}" class="btn btn-outline-secondary ms-2">
                    <i class="fas fa-cog"></i> Carousel Settings
                </a>
                <button class="btn btn-outline-success ms-2" onclick="testCarousel()">
                    <i class="fas fa-play"></i> Test Carousel
                </button>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-images fa-4x text-muted mb-3"></i>
                <h4>No Hero Banners Yet</h4>
                <p class="text-muted mb-4">Create stunning carousel banners to showcase your featured products and promotions.</p>
                <a href="{% url 'admin_hero_banner_add' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Create Your First Banner
                </a>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Categories Overview -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-tag me-1"></i>
            Category Management
        </div>
        <div class="card-body">
            <div class="row">
                {% for category in categories %}
                <div class="col-md-4 col-lg-3 mb-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                {% if category.image %}
                                <img src="{{ category.image.url }}" alt="{{ category.name }}" class="rounded-circle me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                {% else %}
                                <div class="bg-light rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 40px; height: 40px;">
                                    <i class="fas fa-tag text-muted"></i>
                                </div>
                                {% endif %}
                                <h5 class="card-title mb-0">{{ category.name }}</h5>
                            </div>
                            <p class="card-text">
                                <span class="badge bg-primary">{{ category.products.count }} products</span>
                            </p>
                            <div class="d-flex justify-content-between">
                                <a href="{% url 'admin_products_by_category' category.id %}" class="btn btn-sm btn-outline-primary">View Products</a>
                                <a href="{% url 'admin_category_edit' category.id %}" class="btn btn-sm btn-outline-secondary">Edit</a>
                            </div>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-12 text-center py-4">
                    <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                    <h4>No Categories Yet</h4>
                    <p class="text-muted">Get started by creating your first category</p>
                    <a href="{% url 'admin_category_add' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Category
                    </a>
                </div>
                {% endfor %}
            </div>

            {% if categories %}
            <div class="text-end mt-3">
                <a href="{% url 'admin_category_add' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add New Category
                </a>
                <a href="{% url 'admin_categories' %}" class="btn btn-outline-primary ms-2">
                    <i class="fas fa-th-list"></i> Manage All Categories
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.card {
    margin-bottom: 1.5rem;
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
}
.table th {
    background-color: #f8f9fa;
    font-weight: 600;
}
.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}

.btn-outline-success:hover {
    background-color: #28a745;
    border-color: #28a745;
}

.alert {
    border-radius: 8px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh stats every 30 seconds
    setInterval(function() {
        const now = new Date();
        const timeString = now.toLocaleTimeString();
        console.log('Stats refreshed at:', timeString);
    }, 30000);

    // Add loading states to action buttons
    const actionButtons = document.querySelectorAll('.btn[href]');
    actionButtons.forEach(button => {
        button.addEventListener('click', function() {
            if (!this.classList.contains('btn-outline-success')) {
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';

                // Reset after page navigation
                setTimeout(() => {
                    this.innerHTML = originalText;
                }, 100);
            }
        });
    });
});

// Test carousel functionality
function testCarousel() {
    const button = event.target;
    const originalText = button.innerHTML;

    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
    button.disabled = true;

    // Open homepage in new tab
    window.open('{% url "home" %}', '_blank');

    // Reset button after 2 seconds
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;

        // Show success message
        showNotification('Carousel test opened in new tab!', 'success');
    }, 2000);
}

// Notification system
function showNotification(message, type = 'info') {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alert.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i> ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    document.body.appendChild(alert);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + N = New Banner
    if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
        e.preventDefault();
        window.location.href = '{% url "admin_hero_banner_add" %}';
    }

    // Ctrl/Cmd + , = Site Config
    if ((e.ctrlKey || e.metaKey) && e.key === ',') {
        e.preventDefault();
        window.location.href = '{% url "admin_site_config" %}';
    }
});
</script>
{% endblock %}
