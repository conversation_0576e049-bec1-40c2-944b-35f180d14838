{% comment %}
    Reusable product card template
    Usage: {% include 'includes/product_card.html' with product=product %}
{% endcomment %}
{% load custom_filters %}

<div class="product-card">
    <!-- Availability Badge -->
    {% if product.stock_status == 'available' %}
        <div class="availability-badge available">Available</div>
    {% elif product.stock_status == 'preorder' %}
        <div class="availability-badge pre-order">Pre-order</div>
    {% elif product.stock_status == 'notavailable' %}
        <div class="availability-badge unavailable">Out of Stock</div>
    {% endif %}

    <!-- Sale Badge -->
    {% if product.is_on_sale %}
        <div class="sale-badge">Sale</div>
    {% endif %}

    <div class="product-image-container">
        {% if product.image %}
            <img src="{{ product.image.url }}" alt="{{ product.name }}" class="product-image">
        {% else %}
            <img src="https://via.placeholder.com/300x200?text=Product+Image" alt="{{ product.name }}" class="product-image">
        {% endif %}
    </div>

    <div class="product-info">
        {% if product.brand %}
            <div class="product-brand">
                <a href="{% url 'brand_detail' product.brand.slug %}" class="brand-link">
                    {{ product.brand.name }}
                </a>
            </div>
        {% else %}
            <div class="product-brand">LENOVO</div>
        {% endif %}

        <h3 class="product-title">
            <a href="{% url 'product_detail' product.slug %}" class="product-name-link">
                {{ product.name }}
            </a>
        </h3>

        <!-- Product Rating -->
        {% if product.average_rating %}
        <div class="product-rating">
            <div class="rating-stars">
                {% for i in "12345" %}
                    {% if forloop.counter <= product.average_rating %}
                        <i class="fas fa-star"></i>
                    {% elif forloop.counter <= product.average_rating|add:0.5 %}
                        <i class="fas fa-star-half-alt"></i>
                    {% else %}
                        <i class="far fa-star"></i>
                    {% endif %}
                {% endfor %}
            </div>
            <span class="rating-count">({{ product.review_count|default:0 }} Reviews)</span>
        </div>
        {% endif %}

        <!-- Price Section -->
        <div class="product-price">
            {% if product.original_price and product.original_price != product.price %}
                ₨. {{ product.price|floatformat:2 }}
                <span class="product-original-price">₨. {{ product.original_price|floatformat:2 }}</span>
                <div class="product-savings">
                    Save ₨. {{ product.original_price|sub:product.price|floatformat:2 }}
                </div>
            {% else %}
                ₨. {{ product.price|floatformat:2 }}
            {% endif %}
        </div>

        <!-- Product SKU -->
        {% if product.sku %}
        <div class="product-sku">SKU: {{ product.sku }}</div>
        {% endif %}

        <!-- Conditional Add to Cart Button -->
        <div class="product-buttons">
            {% if product.stock_status == 'notavailable' %}
                <button class="add-to-cart disabled" disabled>Out of Stock</button>
            {% elif product.stock_status == 'preorder' %}
                <button class="add-to-cart preorder-btn" data-product-id="{{ product.id }}">Pre-order Now</button>
            {% else %}
                <button class="add-to-cart" data-product-id="{{ product.id }}">Add to Cart</button>
            {% endif %}
            <a href="{% url 'product_detail' product.slug %}" class="view-cart-btn">
                <i class="fas fa-eye"></i>
            </a>
        </div>
    </div>
</div>
