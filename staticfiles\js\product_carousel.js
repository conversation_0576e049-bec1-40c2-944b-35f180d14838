/**
 * Product Carousel JavaScript
 * Handles the sliding functionality for product carousels
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all product carousels on the page
    initProductCarousels();
});

function initProductCarousels() {
    const carousels = document.querySelectorAll('.product-carousel');
    
    carousels.forEach(carousel => {
        const carouselId = carousel.id;
        const inner = carousel.querySelector('.product-carousel-inner');
        const slides = carousel.querySelectorAll('.product-slide');
        const prevBtn = document.querySelector(`[data-carousel-prev="${carouselId}"]`);
        const nextBtn = document.querySelector(`[data-carousel-next="${carouselId}"]`);
        const dotsContainer = document.querySelector(`[data-carousel-dots="${carouselId}"]`);
        
        if (!inner || slides.length === 0) return;
        
        // Calculate how many slides to show based on screen width
        let slidesToShow = 4; // Default for large screens
        
        function updateSlidesToShow() {
            if (window.innerWidth < 576) {
                slidesToShow = 1;
            } else if (window.innerWidth < 992) {
                slidesToShow = 2;
            } else if (window.innerWidth < 1200) {
                slidesToShow = 3;
            } else {
                slidesToShow = 4;
            }
            updateCarousel();
        }
        
        // Initial calculation
        updateSlidesToShow();
        
        // Update on window resize
        window.addEventListener('resize', updateSlidesToShow);
        
        let currentIndex = 0;
        const totalSlides = slides.length;
        
        // Create dots if container exists
        if (dotsContainer) {
            const totalDots = Math.ceil(totalSlides / slidesToShow);
            for (let i = 0; i < totalDots; i++) {
                const dot = document.createElement('div');
                dot.classList.add('carousel-dot');
                if (i === 0) dot.classList.add('active');
                dot.addEventListener('click', () => goToSlide(i * slidesToShow));
                dotsContainer.appendChild(dot);
            }
        }
        
        // Update carousel position and controls
        function updateCarousel() {
            // Calculate slide width including margins
            const slideWidth = slides[0].offsetWidth + 
                parseInt(window.getComputedStyle(slides[0]).marginLeft) + 
                parseInt(window.getComputedStyle(slides[0]).marginRight);
            
            // Update transform
            inner.style.transform = `translateX(-${currentIndex * slideWidth}px)`;
            
            // Update button states
            if (prevBtn) {
                prevBtn.disabled = currentIndex <= 0;
            }
            
            if (nextBtn) {
                nextBtn.disabled = currentIndex >= totalSlides - slidesToShow;
            }
            
            // Update dots
            if (dotsContainer) {
                const dots = dotsContainer.querySelectorAll('.carousel-dot');
                const activeDotIndex = Math.floor(currentIndex / slidesToShow);
                
                dots.forEach((dot, index) => {
                    dot.classList.toggle('active', index === activeDotIndex);
                });
            }
        }
        
        // Go to specific slide
        function goToSlide(index) {
            currentIndex = Math.max(0, Math.min(index, totalSlides - slidesToShow));
            updateCarousel();
        }
        
        // Previous slide
        function prevSlide() {
            goToSlide(currentIndex - slidesToShow);
        }
        
        // Next slide
        function nextSlide() {
            goToSlide(currentIndex + slidesToShow);
        }
        
        // Add event listeners to controls
        if (prevBtn) {
            prevBtn.addEventListener('click', prevSlide);
        }
        
        if (nextBtn) {
            nextBtn.addEventListener('click', nextSlide);
        }
        
        // Initialize
        updateCarousel();
        
        // Touch swipe functionality
        let touchStartX = 0;
        let touchEndX = 0;
        
        carousel.addEventListener('touchstart', e => {
            touchStartX = e.changedTouches[0].screenX;
        }, { passive: true });
        
        carousel.addEventListener('touchend', e => {
            touchEndX = e.changedTouches[0].screenX;
            handleSwipe();
        }, { passive: true });
        
        function handleSwipe() {
            const swipeThreshold = 50;
            if (touchEndX < touchStartX - swipeThreshold) {
                // Swipe left - go to next slide
                nextSlide();
            } else if (touchEndX > touchStartX + swipeThreshold) {
                // Swipe right - go to previous slide
                prevSlide();
            }
        }
    });
}
