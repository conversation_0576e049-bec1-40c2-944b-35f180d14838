/* UI Enhancements for iTechStore */

/* Top Navigation Bar - iTechStore.com.np style */
.top-nav {
    background: linear-gradient(90deg, #0070f3, #00a1ff);
    color: white;
    padding: 10px 0;
    font-size: 14px;
    border-bottom: none;
    box-shadow: 0 2px 10px rgba(0, 112, 243, 0.2);
}

.top-nav .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.top-nav-left {
    display: flex;
    align-items: center;
}

.top-nav-left i {
    margin-left: 5px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
}

.top-nav-right a {
    margin-left: 20px;
    transition: all 0.3s;
    color: white;
    position: relative;
}

.top-nav-right a:hover {
    color: rgba(255, 255, 255, 0.8);
}

.top-nav-right a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: white;
    transition: width 0.3s ease;
}

.top-nav-right a:hover::after {
    width: 100%;
}

/* Main Navigation - Transparent/Shadow Effect */
.main-nav {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    padding: 15px 0;
    position: sticky;
    top: 0;
    z-index: 100;
    transition: all 0.3s ease;
}

.main-nav:hover {
    background-color: rgba(255, 255, 255, 1);
}

/* Improved Dropdown Menu */
.dropdown-content {
    display: block;
    position: absolute;
    background-color: white;
    min-width: 220px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    z-index: 1;
    border-radius: 8px;
    overflow: hidden;
    margin-top: 15px;
    border: none;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: opacity 0.3s ease, transform 0.3s ease, visibility 0.3s ease;
}

.dropdown:hover .dropdown-content {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-content a {
    color: #333;
    padding: 12px 20px;
    display: block;
    transition: all 0.3s;
    font-size: 14px;
    border-left: 3px solid transparent;
}

.dropdown-content a:hover {
    background-color: #f5f9ff;
    color: #0070f3;
    border-left: 3px solid #0070f3;
    padding-left: 25px;
}

/* Dropdown indicator animation */
.nav-links .dropdown > a .fa-chevron-down {
    transition: transform 0.3s ease;
    font-size: 12px;
    margin-left: 5px;
}

.nav-links .dropdown:hover > a .fa-chevron-down {
    transform: rotate(180deg);
}

/* Product Card Enhancements */
.product-card {
    transition: all 0.3s ease;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    position: relative;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.product-image {
    position: relative;
    overflow: hidden;
    height: 220px;
}

.product-image img {
    transition: transform 0.5s ease;
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

/* Stock Status Labels */
.stock-status {
    position: absolute;
    top: 15px;
    right: 15px;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    z-index: 2;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.stock-status.available {
    background-color: #28a745;
    color: white;
}

.stock-status.preorder {
    background-color: #fd7e14;
    color: white;
}

.stock-status.notavailable {
    background-color: #dc3545;
    color: white;
}

.product-card:hover .stock-status {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

/* Add to Cart Button */
.add-to-cart {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.add-to-cart::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.7s ease;
}

.add-to-cart:hover::before {
    left: 100%;
}

/* Hero Section Enhancements */
.hero-section {
    background-color: #f7f9fc;
    padding: 50px 0;
    margin: 20px 0;
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
}

.hero-slider {
    border-radius: 15px;
    overflow: hidden;
}

/* Hero Dots Enhancement */
.hero-dots {
    display: flex;
    justify-content: center;
    position: absolute;
    bottom: 30px;
    left: 0;
    right: 0;
    z-index: 10;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 10px 15px;
    border-radius: 30px;
    width: fit-content;
    margin: 0 auto;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.4);
    margin: 0 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.dot:hover {
    background-color: rgba(255, 255, 255, 0.7);
}

.dot.active {
    background-color: white;
    transform: scale(1.2);
    border: 2px solid rgba(0, 112, 243, 0.5);
}

/* Remove slide numbers */
.dot::after {
    display: none;
}

/* Slower Slider Transitions */
.hero-slide {
    transition: opacity 1.2s ease, transform 1.2s ease;
}

/* Floating Cart Enhancement */
.floating-cart {
    transition: all 0.3s ease;
}

.floating-cart:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
}

/* Mobile Menu Enhancements */
.mobile-menu {
    transition: transform 0.4s ease-in-out;
}

.mobile-dropdown-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s ease;
}

.mobile-dropdown.active .mobile-dropdown-content {
    max-height: 500px;
}
