{% extends 'admin/base_admin.html' %}

{% block title %}Manage Promotions{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="mt-4">Manage Promotions</h1>

    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'admin_dashboard' %}">Dashboard</a></li>
            <li class="breadcrumb-item active">Promotions</li>
        </ol>
    </nav>

    {% if messages %}
    <div class="row">
        <div class="col-12">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-tags me-1"></i>
            Active Promotions
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <!-- Filters -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <form method="get" class="d-flex">
                            <select name="product" class="form-control me-2">
                                <option value="">All Products</option>
                                {% for product in products %}
                                <option value="{{ product.id }}" {% if current_product_id == product.id|stringformat:"s" %}selected{% endif %}>
                                    {{ product.name }}
                                </option>
                                {% endfor %}
                            </select>
                            <select name="type" class="form-control me-2">
                                <option value="">All Types</option>
                                <option value="featured" {% if current_promo_type == 'featured' %}selected{% endif %}>Featured</option>
                                <option value="best_seller" {% if current_promo_type == 'best_seller' %}selected{% endif %}>Best Seller</option>
                                <option value="new_arrival" {% if current_promo_type == 'new_arrival' %}selected{% endif %}>New Arrival</option>
                                <option value="top_deal" {% if current_promo_type == 'top_deal' %}selected{% endif %}>Top Deal</option>
                            </select>
                            <button type="submit" class="btn btn-primary">Filter</button>
                            {% if current_product_id or current_promo_type %}
                            <a href="{% url 'admin_promotions' %}" class="btn btn-secondary ms-2">Clear</a>
                            {% endif %}
                        </form>
                    </div>
                </div>

                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>Product</th>
                            <th>Original Price</th>
                            <th>Sale Price</th>
                            <th>Discount</th>
                            <th>Promotion Type</th>
                            <th>Period</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for promo in promotions %}
                        <tr>
                            <td>
                                <a href="{% url 'admin_product_edit' promo.product.id %}">
                                    {{ promo.product.name }}
                                </a>
                            </td>
                            <td>₨. {{ promo.product.price|floatformat:2 }}</td>
                            <td>₨. {{ promo.sale_price|floatformat:2 }}</td>
                            <td>
                                {% if promo.discount_percentage %}
                                {{ promo.discount_percentage|floatformat:0 }}%
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>
                                {% if promo.is_featured %}<span class="badge bg-primary">Featured</span>{% endif %}
                                {% if promo.is_best_seller %}<span class="badge bg-success">Best Seller</span>{% endif %}
                                {% if promo.is_new_arrival %}<span class="badge bg-info">New Arrival</span>{% endif %}
                                {% if promo.is_top_deal %}<span class="badge bg-danger">Top Deal</span>{% endif %}
                                {% if promo.is_special_offer %}<span class="badge bg-danger">Special Offer</span>{% endif %}
                            </td>
                            <td>
                                {{ promo.start_date|date:"M d, Y" }}
                                {% if promo.end_date %}
                                - {{ promo.end_date|date:"M d, Y" }}
                                {% else %}
                                - Ongoing
                                {% endif %}
                            </td>
                            <td>
                                {% if promo.is_active %}
                                <span class="badge bg-success">Active</span>
                                {% else %}
                                <span class="badge bg-secondary">Inactive</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{% url 'admin_promotion_edit' promo.id %}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                                <a href="{% url 'admin_promotion_delete' promo.id %}" class="btn btn-danger btn-sm">
                                    <i class="fas fa-trash"></i> Delete
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center">No promotions found. Create some below.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-plus-circle me-1"></i>
            Add New Promotion
        </div>
        <div class="card-body">
            <form method="post" action="{% url 'admin_promotions' %}">
                {% csrf_token %}

                {% if form.non_field_errors %}
                <div class="alert alert-danger">
                    {% for error in form.non_field_errors %}
                    {{ error }}
                    {% endfor %}
                </div>
                {% endif %}

                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="{{ form.product.id_for_label }}" class="form-label">Product*</label>
                            {{ form.product }}
                            {% if form.product.errors %}
                            <div class="text-danger">
                                {% for error in form.product.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="{{ form.sale_price.id_for_label }}" class="form-label">Sale Price*</label>
                            {{ form.sale_price }}
                            {% if form.sale_price.errors %}
                            <div class="text-danger">
                                {% for error in form.sale_price.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="{{ form.discount_percentage.id_for_label }}" class="form-label">Discount Percentage</label>
                            {{ form.discount_percentage }}
                            <small class="form-text text-muted">Leave blank to calculate automatically</small>
                            {% if form.discount_percentage.errors %}
                            <div class="text-danger">
                                {% for error in form.discount_percentage.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.start_date.id_for_label }}" class="form-label">Start Date*</label>
                            {{ form.start_date }}
                            {% if form.start_date.errors %}
                            <div class="text-danger">
                                {% for error in form.start_date.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.end_date.id_for_label }}" class="form-label">End Date</label>
                            {{ form.end_date }}
                            <small class="form-text text-muted">Leave blank for ongoing promotion</small>
                            {% if form.end_date.errors %}
                            <div class="text-danger">
                                {% for error in form.end_date.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-12">
                        <label class="form-label">Promotion Type</label>
                        <div class="form-check">
                            {{ form.is_featured }}
                            <label class="form-check-label" for="{{ form.is_featured.id_for_label }}">
                                Featured Product
                            </label>
                        </div>
                        <div class="form-check">
                            {{ form.is_best_seller }}
                            <label class="form-check-label" for="{{ form.is_best_seller.id_for_label }}">
                                Best Seller
                            </label>
                        </div>
                        <div class="form-check">
                            {{ form.is_new_arrival }}
                            <label class="form-check-label" for="{{ form.is_new_arrival.id_for_label }}">
                                New Arrival
                            </label>
                        </div>
                        <div class="form-check">
                            {{ form.is_top_deal }}
                            <label class="form-check-label" for="{{ form.is_top_deal.id_for_label }}">
                                Top Deal
                            </label>
                        </div>
                    </div>
                </div>

                <button type="submit" class="btn btn-success">
                    <i class="fas fa-save"></i> Save Promotion
                </button>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set default start date to today
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('start_date').value = today;
    });
</script>
{% endblock %}
