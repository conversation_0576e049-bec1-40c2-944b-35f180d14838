from django.core.management.base import BaseCommand
from authtryapp.models import Category

class Command(BaseCommand):
    help = 'Creates initial categories for the store'

    def handle(self, *args, **kwargs):
        # Define the categories to create
        categories = [
            {
                'name': 'Laptops',
                'slug': 'laptops',
                'description': 'Portable computing devices for work, study, and entertainment.'
            },
            {
                'name': 'Desktops',
                'slug': 'desktops',
                'description': 'Powerful desktop computers for home and office use.'
            },
            {
                'name': 'Gaming',
                'slug': 'gaming',
                'description': 'Gaming laptops, desktops, and accessories for the ultimate gaming experience.'
            },
            {
                'name': 'Audio',
                'slug': 'audio',
                'description': 'Headphones, speakers, and audio equipment for premium sound quality.'
            },
            {
                'name': 'Mobile',
                'slug': 'mobile',
                'description': 'Smartphones, tablets, and mobile accessories for staying connected on the go.'
            },
            {
                'name': 'Smart Home',
                'slug': 'smart-home',
                'description': 'Smart devices and systems to automate and enhance your home.'
            },
            {
                'name': 'Accessories',
                'slug': 'accessories',
                'description': 'Essential accessories for all your tech devices.'
            }
        ]

        # Create categories
        for category_data in categories:
            category, created = Category.objects.get_or_create(
                slug=category_data['slug'],
                defaults={
                    'name': category_data['name'],
                    'description': category_data['description']
                }
            )
            
            if created:
                self.stdout.write(self.style.SUCCESS(f"Created category: {category.name}"))
            else:
                self.stdout.write(self.style.WARNING(f"Category already exists: {category.name}"))
