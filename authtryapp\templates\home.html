{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>iTechStore - Your Technology Destination</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/ui-enhancements.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Top Navigation Bar -->
    <div class="top-nav">
        <div class="container">
            <div class="top-nav-left">
                <span>NPR</span>
                <i class="fas fa-chevron-down"></i>
            </div>
            <div class="top-nav-right">
                {% if user.is_authenticated %}
                    <span>Welcome, {{ user.username }}</span>
                    <a href="{% url 'logout' %}" class="sign-in">Logout</a>
                {% else %}
                    <a href="{% url 'register' %}" class="create-account">Create an account</a>
                    <a href="{% url 'login' %}" class="sign-in">Sign in</a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Main Navigation -->
    <nav class="main-nav">
        <div class="container">
            <div class="logo">
                <a href="{% url 'home' %}">
                    <h1><span class="i-text">i</span><span class="tech-text">Tech</span><span class="store-text">Store</span></h1>
                    <p class="tagline">Your Technology Destination</p>
                </a>
            </div>
            <div class="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </div>
            <div class="nav-links">
                <ul>
                    <li><a href="{% url 'home' %}" class="active">Home</a></li>
                    <li class="dropdown">
                        <a href="#">Shop <i class="fas fa-chevron-down"></i></a>
                        <div class="dropdown-content">
                            {% if categories %}
                                {% for category in categories %}
                                    <a href="#" data-category="{{ category.slug }}">{{ category.name }}</a>
                                {% endfor %}
                                <a href="#" data-category="all">View All Products</a>
                            {% else %}
                                <a href="#" data-category="laptops">Laptops</a>
                                <a href="#" data-category="desktops">Desktops</a>
                                <a href="#" data-category="gaming">Gaming</a>
                                <a href="#" data-category="audio">Audio & Video</a>
                                <a href="#" data-category="mobile">Phones & Tablets</a>
                                <a href="#" data-category="smart-home">Smart Home</a>
                                <a href="#" data-category="accessories">Accessories</a>
                                <a href="#" data-category="all">View All Products</a>
                            {% endif %}
                        </div>
                    </li>
                    <li><a href="#">Brands</a></li>
                    <li class="dropdown">
                        <a href="#">Blogs <i class="fas fa-chevron-down"></i></a>
                        <div class="dropdown-content">
                            <a href="#">Tech News</a>
                            <a href="#">Reviews</a>
                            <a href="#">Tutorials</a>
                        </div>
                    </li>
                    <li><a href="#">PC BLD</a></li>
                </ul>
            </div>
            <div class="nav-icons">
                <div class="search-container">
                    <form id="search-form" action="#" method="get">
                        <input type="text" id="search-input" placeholder="Search products...">
                        <button type="submit" class="search-btn"><i class="fas fa-search"></i></button>
                    </form>
                    <div class="search-results" id="search-results"></div>
                </div>
                <a href="{% url 'cart_summary' %}" class="cart-icon">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count">{% if cart %}{{ cart.total_items|default:"0" }}{% else %}0{% endif %}</span>
                </a>
            </div>
        </div>
    </nav>

    <!-- Mobile Menu (Hidden by default) -->
    <div class="mobile-menu">
        <div class="mobile-menu-header">
            <h3><span class="i-text">i</span><span class="tech-text">Tech</span><span class="store-text">Store</span></h3>
            <button class="mobile-menu-close"><i class="fas fa-times"></i></button>
        </div>
        <div class="mobile-search">
            <form id="mobile-search-form" action="#" method="get">
                <input type="text" id="mobile-search-input" placeholder="Search products...">
                <button type="submit" class="mobile-search-btn"><i class="fas fa-search"></i></button>
            </form>
            <div class="search-results" id="mobile-search-results"></div>
        </div>
        <ul class="mobile-menu-links">
            <li><a href="{% url 'home' %}" class="active">Home</a></li>
            <li class="mobile-dropdown">
                <a href="#">Shop <i class="fas fa-chevron-down"></i></a>
                <ul class="mobile-dropdown-content">
                    {% if categories %}
                        {% for category in categories %}
                            <li><a href="#" data-category="{{ category.slug }}">{{ category.name }}</a></li>
                        {% endfor %}
                        <li><a href="#" data-category="all">View All Products</a></li>
                    {% else %}
                        <li><a href="#" data-category="laptops">Laptops</a></li>
                        <li><a href="#" data-category="desktops">Desktops</a></li>
                        <li><a href="#" data-category="gaming">Gaming</a></li>
                        <li><a href="#" data-category="audio">Audio & Video</a></li>
                        <li><a href="#" data-category="mobile">Phones & Tablets</a></li>
                        <li><a href="#" data-category="smart-home">Smart Home</a></li>
                        <li><a href="#" data-category="accessories">Accessories</a></li>
                        <li><a href="#" data-category="all">View All Products</a></li>
                    {% endif %}
                </ul>
            </li>
            <li><a href="#">Brands</a></li>
            <li class="mobile-dropdown">
                <a href="#">Blogs <i class="fas fa-chevron-down"></i></a>
                <ul class="mobile-dropdown-content">
                    <li><a href="#">Tech News</a></li>
                    <li><a href="#">Reviews</a></li>
                    <li><a href="#">Tutorials</a></li>
                </ul>
            </li>
            <li><a href="#">PC BLD</a></li>
        </ul>
        <div class="mobile-menu-footer">
            {% if user.is_authenticated %}
                <a href="{% url 'profile' %}" class="mobile-account-link">My Profile</a>
                <a href="{% url 'logout' %}" class="mobile-account-link">Logout</a>
            {% else %}
                <a href="{% url 'register' %}" class="mobile-account-link">Create an account</a>
                <a href="{% url 'login' %}" class="mobile-account-link">Sign in</a>
            {% endif %}
            <a href="{% url 'cart_summary' %}" class="mobile-account-link">
                <i class="fas fa-shopping-cart"></i> Cart ({% if cart %}{{ cart.total_items|default:"0" }}{% else %}0{% endif %})
            </a>
        </div>
    </div>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="hero-slider">
                <!-- Slide 1 - Horizontal Slide -->
                <div class="hero-slide active" style="background: linear-gradient(to right, rgba(255,255,255,0.9) 40%, rgba(255,255,255,0.5));" data-effect="slide">
                    <div class="hero-content">
                        <div class="hero-text">
                            <h1>Premium Tech</h1>
                            <h2>For Everyone</h2>
                            <p>Discover the latest in cutting-edge technology with our premium selection of devices designed for every lifestyle.</p>
                            <a href="#" data-category="laptops" class="order-btn">SHOP NOW</a>
                        </div>
                        <div class="hero-image">
                            <img src="https://images.unsplash.com/photo-1603302576837-37561b2e2302?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80" alt="Premium Tech">
                        </div>
                    </div>
                </div>

                <!-- Slide 2 - Zoom Effect -->
                <div class="hero-slide zoom-out" style="background: linear-gradient(to right, rgba(255,255,255,0.9) 40%, rgba(255,255,255,0.5));" data-effect="zoom">
                    <div class="hero-content">
                        <div class="hero-text">
                            <h1>Gaming Gear</h1>
                            <h2>Level Up</h2>
                            <p>Elevate your gaming experience with our top-tier equipment designed for professional performance and immersive gameplay.</p>
                            <a href="#" data-category="gaming" class="order-btn">EXPLORE</a>
                        </div>
                        <div class="hero-image">
                            <img src="https://images.unsplash.com/photo-1593640408182-31c70c8268f5?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80" alt="Gaming Setup">
                        </div>
                    </div>
                </div>

                <!-- Slide 3 - Fade Effect -->
                <div class="hero-slide fade-in" style="background: linear-gradient(to right, rgba(255,255,255,0.9) 40%, rgba(255,255,255,0.5));" data-effect="fade">
                    <div class="hero-content">
                        <div class="hero-text">
                            <h1>Smart Home</h1>
                            <h2>Innovation</h2>
                            <p>Transform your living space with intelligent technology that seamlessly integrates comfort, convenience, and cutting-edge innovation.</p>
                            <a href="#" data-category="smart-home" class="order-btn">DISCOVER</a>
                        </div>
                        <div class="hero-image">
                            <img src="https://images.unsplash.com/photo-1550745165-9bc0b252726f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80" alt="Smart Home">
                        </div>
                    </div>
                </div>

                <!-- Slide 4 - Slide Up Effect -->
                <div class="hero-slide slide-up" style="background: linear-gradient(to right, rgba(255,255,255,0.9) 40%, rgba(255,255,255,0.5));" data-effect="slide-up">
                    <div class="hero-content">
                        <div class="hero-text">
                            <h1>Audio Excellence</h1>
                            <h2>Crystal Clear</h2>
                            <p>Experience sound like never before with our premium audio collection, designed for audiophiles and casual listeners alike.</p>
                            <a href="#" data-category="audio" class="order-btn">LISTEN NOW</a>
                        </div>
                        <div class="hero-image">
                            <img src="https://images.unsplash.com/photo-1546435770-a3e426bf472b?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80" alt="Premium Audio">
                        </div>
                    </div>
                </div>

                <!-- Slide 5 - Horizontal Slide -->
                <div class="hero-slide" style="background: linear-gradient(to right, rgba(255,255,255,0.9) 40%, rgba(255,255,255,0.5));" data-effect="slide">
                    <div class="hero-content">
                        <div class="hero-text">
                            <h1>Mobile Tech</h1>
                            <h2>On The Go</h2>
                            <p>Stay connected anywhere with our cutting-edge mobile devices that combine performance, style, and innovation.</p>
                            <a href="#" data-category="mobile" class="order-btn">BROWSE NOW</a>
                        </div>
                        <div class="hero-image">
                            <img src="https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80" alt="Mobile Devices">
                        </div>
                    </div>
                </div>
            </div>

            <div class="hero-controls">
                <button class="prev-btn" title="Previous"><i class="fas fa-chevron-left"></i></button>
                <button class="next-btn" title="Next"><i class="fas fa-chevron-right"></i></button>
            </div>

            <div class="hero-dots">
                <span class="dot active" data-slide="0" title="Premium Tech"></span>
                <span class="dot" data-slide="1" title="Gaming Gear"></span>
                <span class="dot" data-slide="2" title="Smart Home"></span>
                <span class="dot" data-slide="3" title="Audio Excellence"></span>
                <span class="dot" data-slide="4" title="Mobile Tech"></span>
            </div>

            <div class="effect-controls">
                <button class="effect-btn" data-effect="slide">Effect: Slide</button>
                <div class="effect-options">
                    <button data-effect="slide">Slide</button>
                    <button data-effect="fade">Fade</button>
                    <button data-effect="zoom">Zoom</button>
                    <button data-effect="slide-up">Slide Up</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Products -->
    <section class="featured-products">
        <div class="container">
            <h2 class="section-title">Featured Products</h2>
            <div class="product-grid">
                {% for product in featured_products %}
                <div class="product-card">
                    <div class="product-image">
                        {% if product.image %}
                            <img src="{{ product.image.url }}" alt="{{ product.name }}">
                        {% else %}
                            <img src="https://via.placeholder.com/300x200?text=Product+Image" alt="{{ product.name }}">
                        {% endif %}

                        <!-- Stock Status Label -->
                        {% if product.stock_status == 'available' %}
                            <div class="stock-status available">Available now</div>
                        {% elif product.stock_status == 'preorder' %}
                            <div class="stock-status preorder">Pre-order</div>
                        {% elif product.stock_status == 'notavailable' %}
                            <div class="stock-status notavailable">Out of stock</div>
                        {% endif %}
                    </div>
                    <div class="product-info">
                        <h3>{{ product.name }}</h3>
                        {% if product.has_variants %}
                            <span class="starting-from">Starting from</span>
                        {% endif %}
                        <p class="price">₨. {{ product.price }}</p>

                        <!-- Conditional Add to Cart Button -->
                        <div class="product-buttons">
                            {% if product.stock_status == 'notavailable' %}
                                <button class="add-to-cart disabled" disabled>Out of Stock</button>
                            {% elif product.stock_status == 'preorder' %}
                                <button class="add-to-cart preorder-btn" data-product-id="{{ product.id }}">Pre-order Now</button>
                            {% else %}
                                <button class="add-to-cart" data-product-id="{{ product.id }}">Add to Cart</button>
                            {% endif %}
                            <a href="{% url 'cart_summary' %}" class="view-cart-btn">View Cart</a>
                        </div>
                    </div>
                </div>
                {% empty %}
                <!-- Default products if no products in database -->
                <div class="product-card">
                    <div class="product-image">
                        <img src="https://media.itechstore.com.np/media/catalog/product/cache/1/small_image/300x/9df78eab33525d08d6e5fb8d27136e95/l/e/lenovo_ideapad_slim_5_14iil05_platinum_grey_01.jpg" alt="Lenovo Ideapad">
                        <div class="stock-status available">IN STOCK</div>
                    </div>
                    <div class="product-info">
                        <h3>Lenovo Ideapad Slim 5</h3>
                        <p class="price">₨. 79,999</p>
                        <div class="product-buttons">
                            <button class="add-to-cart" data-product-id="demo1">Add to Cart</button>
                            <a href="{% url 'cart_summary' %}" class="view-cart-btn">View Cart</a>
                        </div>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">
                        <img src="https://media.itechstore.com.np/media/catalog/product/cache/1/small_image/300x/9df78eab33525d08d6e5fb8d27136e95/m/a/macbook_air_m2_midnight_01.jpg" alt="MacBook Air">
                        <div class="stock-status preorder">PRE-ORDER</div>
                    </div>
                    <div class="product-info">
                        <h3>MacBook Air M2</h3>
                        <p class="price">₨. 1,19,999</p>
                        <div class="product-buttons">
                            <button class="add-to-cart preorder-btn" data-product-id="demo2">Pre-order Now</button>
                            <a href="{% url 'cart_summary' %}" class="view-cart-btn">View Cart</a>
                        </div>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">
                        <img src="https://media.itechstore.com.np/media/catalog/product/cache/1/small_image/300x/9df78eab33525d08d6e5fb8d27136e95/a/s/asus_rog_zephyrus_g14_ga401qm_eclipse_gray_01.jpg" alt="ASUS ROG">
                        <div class="stock-status notavailable">OUT OF STOCK</div>
                    </div>
                    <div class="product-info">
                        <h3>ASUS ROG Zephyrus</h3>
                        <p class="price">₨. 1,49,999</p>
                        <div class="product-buttons">
                            <button class="add-to-cart disabled" disabled data-product-id="demo3">Out of Stock</button>
                            <a href="{% url 'cart_summary' %}" class="view-cart-btn">View Cart</a>
                        </div>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">
                        <img src="https://media.itechstore.com.np/media/catalog/product/cache/1/small_image/300x/9df78eab33525d08d6e5fb8d27136e95/d/e/dell_xps_13_9310_silver_01.jpg" alt="Dell XPS">
                        <div class="stock-status available">IN STOCK</div>
                    </div>
                    <div class="product-info">
                        <h3>Dell XPS 13</h3>
                        <p class="price">₨. 1,29,999</p>
                        <div class="product-buttons">
                            <button class="add-to-cart" data-product-id="demo4">Add to Cart</button>
                            <a href="{% url 'cart_summary' %}" class="view-cart-btn">View Cart</a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-columns">
                <div class="footer-column">
                    <h3>Shop</h3>
                    <ul>
                        {% if categories %}
                            {% for category in categories %}
                                <li><a href="#" data-category="{{ category.slug }}">{{ category.name }}</a></li>
                            {% endfor %}
                        {% else %}
                            <li><a href="#" data-category="laptops">Laptops</a></li>
                            <li><a href="#" data-category="desktops">Desktops</a></li>
                            <li><a href="#" data-category="gaming">Gaming</a></li>
                            <li><a href="#" data-category="audio">Audio & Video</a></li>
                            <li><a href="#" data-category="mobile">Phones & Tablets</a></li>
                            <li><a href="#" data-category="smart-home">Smart Home</a></li>
                            <li><a href="#" data-category="accessories">Accessories</a></li>
                        {% endif %}
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Support</h3>
                    <ul>
                        <li><a href="#">Contact Us</a></li>
                        <li><a href="#">FAQs</a></li>
                        <li><a href="#">Shipping</a></li>
                        <li><a href="#">Returns</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Company</h3>
                    <ul>
                        <li><a href="#">About Us</a></li>
                        <li><a href="#">Careers</a></li>
                        <li><a href="#">Blog</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Connect</h3>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                    <div class="newsletter">
                        <h4>Subscribe to our newsletter</h4>
                        <form>
                            <input type="email" placeholder="Your email address">
                            <button type="submit">Subscribe</button>
                        </form>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 iTechStore. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Floating Cart Button -->
    <a href="{% url 'cart_summary' %}" class="floating-cart">
        <i class="fas fa-shopping-cart"></i>
        <span class="floating-cart-count">{% if cart %}{{ cart.total_items|default:"0" }}{% else %}0{% endif %}</span>
    </a>

    <script src="{% static 'js/script.js' %}"></script>
    <script src="{% static 'js/ui-enhancements.js' %}"></script>
</body>
</html>
