from django.core.management.base import BaseCommand
from django.core.management import call_command

class Command(BaseCommand):
    help = 'Create and apply migrations for the Cart and CartItem models'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Creating migrations...'))
        call_command('makemigrations', 'authtryapp')
        
        self.stdout.write(self.style.SUCCESS('Applying migrations...'))
        call_command('migrate', 'authtryapp')
        
        self.stdout.write(self.style.SUCCESS('Migrations completed successfully!'))
