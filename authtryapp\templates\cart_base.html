{% extends 'base_new.html' %}
{% load static %}

{% block title %}Shopping Cart - iTechStore{% endblock %}

{% block extra_css %}
<style>
    /* Modern Cart Page Styles */
    .cart-container {
        padding: 40px 0;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        min-height: 100vh;
    }

    .cart-header {
        text-align: center;
        margin-bottom: 40px;
        padding: 30px;
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .cart-header h1 {
        font-size: 36px;
        margin-bottom: 10px;
        color: #333;
        font-weight: 700;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .cart-header p {
        color: #6c757d;
        font-size: 18px;
        margin: 0;
    }

    .cart-content {
        display: grid;
        grid-template-columns: 1fr 400px;
        gap: 30px;
        align-items: start;
    }

    @media (max-width: 768px) {
        .cart-content {
            grid-template-columns: 1fr;
            gap: 20px;
        }
    }

    .cart-items {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .cart-items-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 25px 30px;
        font-size: 20px;
        font-weight: 600;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .cart-items-count {
        background: rgba(255, 255, 255, 0.2);
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 14px;
    }

    .cart-item {
        display: grid;
        grid-template-columns: 120px 1fr auto;
        gap: 20px;
        padding: 25px 30px;
        border-bottom: 1px solid #f1f3f4;
        align-items: center;
        transition: all 0.3s ease;
        position: relative;
    }

    .cart-item:hover {
        background: #f8f9ff;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);
    }

    .cart-item:last-child {
        border-bottom: none;
    }

    .cart-item-image {
        width: 120px;
        height: 120px;
        border-radius: 12px;
        overflow: hidden;
        position: relative;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .cart-item-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .cart-item:hover .cart-item-image img {
        transform: scale(1.05);
    }

    .cart-item-details {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .cart-item-name {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin: 0;
        line-height: 1.4;
    }

    .cart-item-category {
        font-size: 14px;
        color: #6c757d;
        margin: 0;
    }

    .cart-item-price {
        font-size: 20px;
        color: #28a745;
        font-weight: 700;
        margin: 0;
    }

    .cart-item-original-price {
        font-size: 16px;
        color: #6c757d;
        text-decoration: line-through;
        margin: 0;
    }

    .cart-item-savings {
        font-size: 14px;
        color: #dc3545;
        font-weight: 600;
        margin: 0;
    }

    .cart-item-actions {
        display: flex;
        flex-direction: column;
        gap: 15px;
        align-items: flex-end;
    }

    .quantity-control {
        display: flex;
        align-items: center;
        background: #f8f9fa;
        border: 2px solid #e9ecef;
        border-radius: 25px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .quantity-btn {
        background: transparent;
        border: none;
        padding: 10px 15px;
        cursor: pointer;
        font-size: 18px;
        font-weight: 700;
        color: #667eea;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 40px;
    }

    .quantity-btn:hover {
        background: #667eea;
        color: white;
        transform: scale(1.1);
    }

    .quantity-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
    }

    .quantity-input {
        border: none;
        background: transparent;
        text-align: center;
        width: 60px;
        padding: 10px 5px;
        font-size: 16px;
        font-weight: 600;
        color: #333;
    }

    .quantity-input:focus {
        outline: none;
    }

    .remove-btn {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 25px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 600;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    }

    .remove-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
    }

    .item-total {
        font-size: 18px;
        font-weight: 700;
        color: #333;
        text-align: right;
    }

    .cart-summary {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        padding: 30px;
        height: fit-content;
        position: sticky;
        top: 20px;
    }

    .cart-summary h2 {
        font-size: 24px;
        font-weight: 700;
        color: #333;
        margin-bottom: 25px;
        text-align: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .cart-summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 0;
        border-bottom: 1px solid #f1f3f4;
        font-size: 16px;
    }

    .cart-summary-item:last-child {
        border-bottom: none;
    }

    .cart-summary-item.total {
        font-size: 20px;
        font-weight: 700;
        color: #333;
        border-top: 2px solid #667eea;
        border-bottom: none;
        padding-top: 20px;
        margin-top: 15px;
    }

    .cart-summary-item .label {
        color: #6c757d;
        font-weight: 500;
    }

    .cart-summary-item .value {
        font-weight: 600;
        color: #333;
    }

    .cart-summary-item.total .value {
        color: #28a745;
        font-size: 24px;
    }

    .checkout-btn {
        width: 100%;
        padding: 18px;
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border: none;
        border-radius: 25px;
        font-size: 18px;
        font-weight: 700;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-top: 25px;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .checkout-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    }

    .continue-shopping {
        display: inline-flex;
        align-items: center;
        gap: 10px;
        margin-top: 25px;
        padding: 15px 25px;
        color: #667eea;
        text-decoration: none;
        font-weight: 600;
        border: 2px solid #667eea;
        border-radius: 25px;
        transition: all 0.3s ease;
    }

    .continue-shopping:hover {
        background: #667eea;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    .empty-cart {
        text-align: center;
        padding: 80px 40px;
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .empty-cart i {
        font-size: 80px;
        color: #e9ecef;
        margin-bottom: 30px;
        display: block;
    }

    .empty-cart h2 {
        font-size: 28px;
        margin-bottom: 15px;
        color: #333;
        font-weight: 700;
    }

    .empty-cart p {
        color: #6c757d;
        margin-bottom: 40px;
        font-size: 18px;
        line-height: 1.6;
    }

    .shop-now-btn {
        display: inline-flex;
        align-items: center;
        gap: 10px;
        padding: 18px 35px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 25px;
        text-decoration: none;
        font-weight: 700;
        font-size: 16px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .shop-now-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .clear-cart-btn {
        width: 100%;
        padding: 12px;
        background: transparent;
        color: #dc3545;
        border: 2px solid #dc3545;
        border-radius: 25px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-top: 15px;
    }

    .clear-cart-btn:hover {
        background: #dc3545;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<!-- Modern Cart Section -->
<section class="cart-container">
    <div class="container">
        <div class="cart-header">
            <h1>🛒 Shopping Cart</h1>
            <p>Review and modify your items before checkout</p>
        </div>

        {% if cart and cart.items.all %}
            <div class="cart-content">
                <div class="cart-items">
                    <div class="cart-items-header">
                        <span>Your Items</span>
                        <span class="cart-items-count">{{ cart.total_items }} item{{ cart.total_items|pluralize }}</span>
                    </div>

                    {% for item in cart.items.all %}
                        <div class="cart-item" data-item-id="{{ item.id }}">
                            <div class="cart-item-image">
                                {% if item.product.image %}
                                    <img src="{{ item.product.image.url }}" alt="{{ item.product.name }}">
                                {% else %}
                                    <img src="https://via.placeholder.com/120x120/667eea/ffffff?text=Product" alt="{{ item.product.name }}">
                                {% endif %}
                            </div>

                            <div class="cart-item-details">
                                <h3 class="cart-item-name">{{ item.product.name }}</h3>
                                <p class="cart-item-category">{{ item.product.category.name }}</p>

                                <!-- Price Display -->
                                <div class="cart-item-price">₨{{ item.product.price|floatformat:2 }}</div>
                            </div>

                            <div class="cart-item-actions">
                                <!-- Quantity Control with CSRF -->
                                <form class="quantity-form" data-item-id="{{ item.id }}">
                                    {% csrf_token %}
                                    <div class="quantity-control">
                                        <button type="button" class="quantity-btn decrease" {% if item.quantity <= 1 %}disabled{% endif %}>
                                            <i class="fas fa-minus"></i>
                                        </button>
                                        <input type="number" class="quantity-input" value="{{ item.quantity }}" min="1" max="99" readonly>
                                        <button type="button" class="quantity-btn increase">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </form>

                                <!-- Remove Button with CSRF -->
                                <form class="remove-form" data-item-id="{{ item.id }}">
                                    {% csrf_token %}
                                    <button type="submit" class="remove-btn">
                                        <i class="fas fa-trash-alt"></i>
                                        Remove
                                    </button>
                                </form>

                                <!-- Item Total -->
                                <div class="item-total">₨{{ item.total_price|floatformat:2 }}</div>
                            </div>
                        </div>
                    {% endfor %}

                    <div style="padding: 25px 30px;">
                        <a href="{% url 'home' %}" class="continue-shopping">
                            <i class="fas fa-arrow-left"></i>
                            Continue Shopping
                        </a>
                    </div>
                </div>

                <div class="cart-summary">
                    <h2>Order Summary</h2>

                    <div class="cart-summary-item">
                        <span class="label">Subtotal ({{ cart.total_items }} item{{ cart.total_items|pluralize }})</span>
                        <span class="value cart-subtotal">₨{{ cart.total_price|floatformat:2 }}</span>
                    </div>

                    <div class="cart-summary-item">
                        <span class="label">Shipping</span>
                        <span class="value">Free</span>
                    </div>

                    <div class="cart-summary-item">
                        <span class="label">Tax</span>
                        <span class="value">₨0.00</span>
                    </div>

                    <div class="cart-summary-item total">
                        <span class="label">Total</span>
                        <span class="value cart-total">₨{{ cart.total_price|floatformat:2 }}</span>
                    </div>

                    <button class="checkout-btn">
                        <i class="fas fa-lock"></i>
                        Proceed to Checkout
                    </button>

                    <!-- Clear Cart Button -->
                    <form class="clear-cart-form">
                        {% csrf_token %}
                        <button type="submit" class="clear-cart-btn">
                            <i class="fas fa-trash"></i>
                            Clear Cart
                        </button>
                    </form>
                </div>
            </div>
        {% else %}
            <div class="empty-cart">
                <i class="fas fa-shopping-cart"></i>
                <h2>Your Cart is Empty</h2>
                <p>Looks like you haven't added anything to your cart yet.<br>Start exploring our amazing products!</p>
                <a href="{% url 'home' %}" class="shop-now-btn">
                    <i class="fas fa-shopping-bag"></i>
                    Start Shopping
                </a>
            </div>
        {% endif %}
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🛒 Modern Cart System Initialized');

    // Get CSRF token
    function getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]').value;
    }

    // Show loading state
    function showLoading(element) {
        element.style.opacity = '0.6';
        element.style.pointerEvents = 'none';
    }

    // Hide loading state
    function hideLoading(element) {
        element.style.opacity = '1';
        element.style.pointerEvents = 'auto';
    }

    // Show success message
    function showMessage(message, type = 'success') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `alert alert-${type}`;
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            padding: 15px 25px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            animation: slideInFromRight 0.3s ease-out;
            max-width: 300px;
        `;

        if (type === 'success') {
            messageDiv.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
        } else if (type === 'error') {
            messageDiv.style.background = 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)';
        }

        messageDiv.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
            ${message}
        `;

        document.body.appendChild(messageDiv);

        setTimeout(() => {
            messageDiv.style.animation = 'slideOutToRight 0.3s ease-in';
            setTimeout(() => messageDiv.remove(), 300);
        }, 3000);
    }

    // Update cart totals
    function updateCartTotals(data) {
        if (data.cart_total !== undefined) {
            const subtotalElements = document.querySelectorAll('.cart-subtotal');
            const totalElements = document.querySelectorAll('.cart-total');

            subtotalElements.forEach(el => el.textContent = `₨${data.cart_total.toFixed(2)}`);
            totalElements.forEach(el => el.textContent = `₨${data.cart_total.toFixed(2)}`);
        }

        if (data.cart_count !== undefined) {
            const countElements = document.querySelectorAll('.cart-items-count');
            countElements.forEach(el => {
                el.textContent = `${data.cart_count} item${data.cart_count !== 1 ? 's' : ''}`;
            });

            // Update header cart count if exists
            const headerCartCount = document.querySelector('.cart-count');
            if (headerCartCount) {
                headerCartCount.textContent = data.cart_count;
            }
        }
    }

    // Remove cart item
    function removeCartItem(itemId) {
        const cartItem = document.querySelector(`[data-item-id="${itemId}"]`);
        if (cartItem) {
            cartItem.style.animation = 'slideOutToRight 0.3s ease-in';
            setTimeout(() => {
                cartItem.remove();

                // Check if cart is empty
                const remainingItems = document.querySelectorAll('.cart-item');
                if (remainingItems.length === 0) {
                    location.reload(); // Reload to show empty cart state
                }
            }, 300);
        }
    }

    // Update item quantity in UI
    function updateItemQuantity(itemId, newQuantity, newTotal) {
        const cartItem = document.querySelector(`[data-item-id="${itemId}"]`);
        if (cartItem) {
            const quantityInput = cartItem.querySelector('.quantity-input');
            const itemTotal = cartItem.querySelector('.item-total');
            const decreaseBtn = cartItem.querySelector('.quantity-btn.decrease');

            quantityInput.value = newQuantity;
            itemTotal.textContent = `₨${newTotal.toFixed(2)}`;

            // Enable/disable decrease button
            decreaseBtn.disabled = newQuantity <= 1;
        }
    }

    // Handle quantity changes
    document.addEventListener('click', function(e) {
        if (e.target.closest('.quantity-btn')) {
            e.preventDefault();

            const btn = e.target.closest('.quantity-btn');
            const form = btn.closest('.quantity-form');
            const itemId = form.dataset.itemId;
            const quantityInput = form.querySelector('.quantity-input');
            const currentQuantity = parseInt(quantityInput.value);

            let newQuantity;
            if (btn.classList.contains('increase')) {
                newQuantity = currentQuantity + 1;
            } else if (btn.classList.contains('decrease') && currentQuantity > 1) {
                newQuantity = currentQuantity - 1;
            } else {
                return; // Don't proceed if trying to decrease below 1
            }

            showLoading(btn.closest('.cart-item'));

            // Send AJAX request to update quantity
            fetch('{% url "update_cart_item" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCSRFToken(),
                },
                body: JSON.stringify({
                    item_id: itemId,
                    quantity: newQuantity
                })
            })
            .then(response => response.json())
            .then(data => {
                hideLoading(btn.closest('.cart-item'));

                if (data.success) {
                    updateItemQuantity(itemId, newQuantity, data.item_total);
                    updateCartTotals(data);
                    showMessage(`Quantity updated to ${newQuantity}`, 'success');
                } else {
                    showMessage(data.message || 'Failed to update quantity', 'error');
                }
            })
            .catch(error => {
                hideLoading(btn.closest('.cart-item'));
                console.error('Error:', error);
                showMessage('An error occurred while updating quantity', 'error');
            });
        }
    });

    // Handle item removal
    document.addEventListener('submit', function(e) {
        if (e.target.classList.contains('remove-form')) {
            e.preventDefault();

            const form = e.target;
            const itemId = form.dataset.itemId;
            const cartItem = form.closest('.cart-item');
            const productName = cartItem.querySelector('.cart-item-name').textContent;

            if (confirm(`Remove "${productName}" from your cart?`)) {
                showLoading(cartItem);

                fetch('{% url "remove_from_cart" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCSRFToken(),
                    },
                    body: JSON.stringify({
                        item_id: itemId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        removeCartItem(itemId);
                        updateCartTotals(data);
                        showMessage(`"${productName}" removed from cart`, 'success');
                    } else {
                        hideLoading(cartItem);
                        showMessage(data.message || 'Failed to remove item', 'error');
                    }
                })
                .catch(error => {
                    hideLoading(cartItem);
                    console.error('Error:', error);
                    showMessage('An error occurred while removing item', 'error');
                });
            }
        }
    });

    // Handle clear cart
    document.addEventListener('submit', function(e) {
        if (e.target.classList.contains('clear-cart-form')) {
            e.preventDefault();

            if (confirm('Are you sure you want to clear your entire cart? This action cannot be undone.')) {
                const form = e.target;
                showLoading(document.querySelector('.cart-content'));

                fetch('{% url "clear_cart" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCSRFToken(),
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showMessage('Cart cleared successfully', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        hideLoading(document.querySelector('.cart-content'));
                        showMessage(data.message || 'Failed to clear cart', 'error');
                    }
                })
                .catch(error => {
                    hideLoading(document.querySelector('.cart-content'));
                    console.error('Error:', error);
                    showMessage('An error occurred while clearing cart', 'error');
                });
            }
        }
    });

    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInFromRight {
            0% { opacity: 0; transform: translateX(100px); }
            100% { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideOutToRight {
            0% { opacity: 1; transform: translateX(0); }
            100% { opacity: 0; transform: translateX(100px); }
        }

        .cart-item {
            transition: all 0.3s ease;
        }
    `;
    document.head.appendChild(style);

    console.log('✅ Cart functionality ready!');
});
</script>
{% endblock %}
