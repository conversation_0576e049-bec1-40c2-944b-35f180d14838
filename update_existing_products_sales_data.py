#!/usr/bin/env python
"""
Script to update existing products with sales data (ratings, reviews, discounts)
"""
import os
import sys
import django
from decimal import Decimal
import random

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'authtry.settings')
django.setup()

from authtryapp.models import Product

def update_existing_products():
    print("🔄 Updating Existing Products with Sales Data...")

    # Get all existing products that don't have sales data
    products = Product.objects.filter(
        rating=0.0,  # Products without ratings
        review_count=0  # Products without reviews
    )

    if not products.exists():
        print("ℹ️ No products found that need sales data updates.")
        return

    updated_count = 0

    for product in products:
        # Generate random sales data
        rating = round(random.uniform(3.5, 5.0), 1)  # Rating between 3.5 and 5.0
        review_count = random.randint(15, 250)  # Reviews between 15 and 250
        is_sponsored = random.choice([True, <PERSON>als<PERSON>, <PERSON>alse, False])  # 25% chance of being sponsored
        has_gift = random.choice([True, False, False])  # 33% chance of having a gift

        # Generate gift description if has_gift is True
        gift_descriptions = [
            'Free wireless mouse',
            'Free laptop bag',
            'Free screen protector',
            'Free USB cable',
            'Free headphones',
            'Free keyboard',
            'Free mouse pad',
            'Free cleaning kit',
            'Free carrying case',
            'Free adapter'
        ]
        gift_description = random.choice(gift_descriptions) if has_gift else ''

        # Generate short specs if not present
        if not product.short_specs:
            if product.category:
                category_name = product.category.name.lower()
                if 'laptop' in category_name:
                    specs = [
                        f"{random.choice(['13.3', '14', '15.6', '17.3'])}\" Display, Intel i{random.choice([5, 7])}, {random.choice([8, 16, 32])}GB RAM, {random.choice([256, 512, 1024])}GB SSD",
                        f"{random.choice(['FHD', '4K', 'QHD'])} Display, AMD Ryzen {random.choice([5, 7])}, {random.choice([8, 16])}GB RAM, {random.choice([512, 1024])}GB SSD",
                        f"{random.choice(['Touch', 'Non-touch'])} Display, {random.choice(['Intel', 'AMD'])} Processor, {random.choice([8, 16])}GB RAM"
                    ]
                elif 'monitor' in category_name:
                    specs = [
                        f"{random.choice(['24', '27', '32', '34'])}\" {random.choice(['FHD', '4K', 'QHD'])}, {random.choice(['60Hz', '75Hz', '144Hz'])}, {random.choice(['IPS', 'VA', 'TN'])} Panel",
                        f"{random.choice(['Curved', 'Flat'])} Display, {random.choice(['HDR10', 'HDR400'])}, USB-C, {random.choice(['1ms', '5ms'])} Response Time",
                        f"{random.choice(['Gaming', 'Professional'])} Monitor, {random.choice(['G-Sync', 'FreeSync'])}, Multiple Ports"
                    ]
                elif 'gaming' in category_name:
                    specs = [
                        f"RGB Lighting, {random.choice(['Mechanical', 'Membrane'])} Keys, {random.choice(['Wired', 'Wireless'])}, Gaming Grade",
                        f"High DPI, {random.choice(['Ergonomic', 'Ambidextrous'])} Design, {random.choice(['7', '9', '12'])} Buttons",
                        f"Surround Sound, Noise Cancelling, {random.choice(['Wired', 'Wireless'])}, Gaming Headset"
                    ]
                elif 'audio' in category_name:
                    specs = [
                        f"Wireless, Noise Cancelling, {random.choice(['20hr', '30hr', '40hr'])} Battery, Premium Sound",
                        f"Bluetooth 5.0, {random.choice(['Over-ear', 'On-ear', 'In-ear'])}, High-Res Audio",
                        f"Active Noise Cancellation, Quick Charge, Voice Assistant"
                    ]
                else:
                    specs = [
                        f"High Quality, Durable Design, {random.choice(['Compact', 'Portable'])}, Premium Materials",
                        f"Advanced Features, User-Friendly, {random.choice(['Wireless', 'Wired'])}, Modern Design",
                        f"Professional Grade, Reliable Performance, {random.choice(['Lightweight', 'Robust'])}"
                    ]
                product.short_specs = random.choice(specs)

        # Randomly add discounts to some products (30% chance)
        if random.random() < 0.3 and product.price:
            # Create original price that's 15-40% higher than current price
            discount_percentage = random.uniform(15, 40)
            discount_multiplier = Decimal(str(1 - discount_percentage / 100))
            original_price = product.price / discount_multiplier
            product.original_price = round(original_price, 2)

        # Update the product
        product.rating = Decimal(str(rating))
        product.review_count = review_count
        product.is_sponsored = is_sponsored
        product.has_gift = has_gift
        product.gift_description = gift_description

        product.save()

        updated_count += 1

        # Show update info
        discount_info = ""
        if product.original_price and product.price:
            discount = ((product.original_price - product.price) / product.original_price) * 100
            discount_info = f" | {discount:.0f}% OFF"

        sponsor_info = " | SPONSORED" if product.is_sponsored else ""
        gift_info = f" | GIFT: {product.gift_description}" if product.has_gift else ""

        print(f"✅ Updated: {product.name}")
        print(f"   Rating: {product.rating} ({product.review_count} reviews){discount_info}{sponsor_info}{gift_info}")

    print(f"\n📊 Summary:")
    print(f"✅ Updated: {updated_count} products")
    print(f"🎯 Products now have ratings, reviews, and enhanced sales features!")

if __name__ == "__main__":
    update_existing_products()
    print("\n🎉 Existing products updated successfully!")
    print("🌐 Visit the homepage or category pages to see the enhanced product displays!")
