{% extends 'base_new.html' %}
{% load static %}

{% block title %}{{ section_title }} - iTechStore{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/sales_products.css' %}">
<style>
    .filter-sort-options {
        display: flex;
        gap: 20px;
        margin: 20px 0;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        align-items: center;
        flex-wrap: wrap;
    }

    .filter-group, .sort-group {
        flex: 1;
        min-width: 200px;
    }

    .form-control {
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background-color: white;
        width: 100%;
        max-width: 200px;
    }

    label {
        margin-right: 10px;
        font-weight: 600;
        display: inline-block;
        margin-bottom: 5px;
    }

    .products-header {
        background-color: #ffffff;
        padding: 30px 0 10px;
        text-align: center;
        margin-bottom: 20px;
    }

    .products-title {
        font-size: 36px;
        font-weight: 700;
        color: #333;
        margin-bottom: 10px;
    }

    .products-subtitle {
        font-size: 18px;
        color: #666;
        font-weight: 400;
        max-width: 800px;
        margin: 0 auto;
        line-height: 1.6;
    }

    .no-products-message {
        text-align: center;
        padding: 80px 20px;
        background: #f8f9fa;
        border-radius: 12px;
        border: 2px dashed #dee2e6;
        margin: 40px 0;
    }

    .view-all-btn {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 12px 24px;
        background: #007bff;
        color: white;
        text-decoration: none;
        border-radius: 6px;
        font-weight: 600;
        transition: background 0.3s ease;
    }

    .view-all-btn:hover {
        background: #0056b3;
        color: white;
        text-decoration: none;
    }

    @media (max-width: 768px) {
        .filter-sort-options {
            flex-direction: column;
            gap: 15px;
        }

        .filter-group, .sort-group {
            width: 100%;
        }

        .form-control {
            max-width: 100%;
        }

        .products-title {
            font-size: 28px;
        }

        .products-subtitle {
            font-size: 16px;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
function updateFilters() {
    const stockFilter = document.getElementById('stock-filter').value;
    const sortOption = document.getElementById('sort-options').value;

    // Get current URL and parameters
    const url = new URL(window.location.href);

    // Update or add parameters
    if (stockFilter) {
        url.searchParams.set('stock', stockFilter);
    } else {
        url.searchParams.delete('stock');
    }

    if (sortOption) {
        url.searchParams.set('sort', sortOption);
    } else {
        url.searchParams.delete('sort');
    }

    // Navigate to new URL
    window.location.href = url.toString();
}
</script>
{% endblock %}

{% block content %}
<!-- Products Header -->
<section class="products-header">
    <div class="container">
        <h1 class="products-title">{{ section_title }}</h1>
        <p class="products-subtitle">{{ section_subtitle }}</p>
        
        <!-- Filter and Sort Options -->
        <div class="filter-sort-options">
            <div class="filter-group">
                <label for="stock-filter">Filter by:</label>
                <select id="stock-filter" class="form-control" onchange="updateFilters()">
                    <option value="">All Products</option>
                    <option value="available" {% if current_stock == 'available' %}selected{% endif %}>Available Now</option>
                    <option value="preorder" {% if current_stock == 'preorder' %}selected{% endif %}>Pre-order</option>
                    <option value="notavailable" {% if current_stock == 'notavailable' %}selected{% endif %}>Out of Stock</option>
                </select>
            </div>

            <div class="sort-group">
                <label for="sort-options">Sort by:</label>
                <select id="sort-options" class="form-control" onchange="updateFilters()">
                    <option value="newest" {% if current_sort == 'newest' %}selected{% endif %}>Newest First</option>
                    <option value="discount_desc" {% if current_sort == 'discount_desc' %}selected{% endif %}>Highest Discount</option>
                    <option value="price_asc" {% if current_sort == 'price_asc' %}selected{% endif %}>Price: Low to High</option>
                    <option value="price_desc" {% if current_sort == 'price_desc' %}selected{% endif %}>Price: High to Low</option>
                    <option value="name_asc" {% if current_sort == 'name_asc' %}selected{% endif %}>Name: A to Z</option>
                    <option value="name_desc" {% if current_sort == 'name_desc' %}selected{% endif %}>Name: Z to A</option>
                </select>
            </div>
        </div>
    </div>
</section>

<!-- Products Grid -->
<section class="sales-section">
    <div class="container">
        {% if products %}
            <div class="sales-products-grid">
                {% for product in products %}
                    {% include 'includes/sales_product_card.html' with product=product %}
                {% endfor %}
            </div>
        {% else %}
            <!-- No Products Message -->
            <div class="no-products-message">
                <i class="fas fa-box-open" style="font-size: 64px; color: #ddd; margin-bottom: 20px;"></i>
                <h3 style="color: #666; margin-bottom: 15px; font-size: 24px;">No Products Found</h3>
                <p style="color: #999; font-size: 16px; margin-bottom: 20px;">
                    {% if current_stock or current_sort %}
                        Try adjusting your filters or check back later for new products.
                    {% else %}
                        No products are currently available. Please check back later.
                    {% endif %}
                </p>
                <a href="{% url 'home' %}" class="view-all-btn">
                    <i class="fas fa-home"></i>
                    Back to Home
                </a>
            </div>
        {% endif %}
    </div>
</section>
{% endblock %}
