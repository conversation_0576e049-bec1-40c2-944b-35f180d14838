#!/usr/bin/env python
"""
<PERSON>ript to create comprehensive sample data for the Sales Management app
"""
import os
import sys
import django
from decimal import Decimal
from datetime import timedelta

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'authtry.settings')
django.setup()

from sales_management.models import SaleCategory, ProductSaleAssignment, SaleCampaign
from authtryapp.models import Product
from django.utils import timezone

def create_comprehensive_sale_categories():
    print("🏷️ Creating Comprehensive Sale Categories...")

    # Enhanced sale categories with all new features
    sale_categories = [
        {
            'name': 'Black Friday Mega Sale',
            'sale_type': 'flash_sale',
            'badge_text': 'BLACK FRIDAY',
            'badge_color': '#343a40',
            'priority': 5,
            'discount_percentage': Decimal('50.00'),
            'minimum_purchase_amount': Decimal('100.00'),
            'maximum_discount_amount': Decimal('500.00'),
            'description': 'Biggest sale of the year - up to 50% off everything!',
            'is_featured': True,
            'end_date': timezone.now() + timedelta(days=7),
            'max_total_uses': 1000,
            'max_uses_per_customer': 3,
        },
        {
            'name': 'Student Discount Program',
            'sale_type': 'student_discount',
            'badge_text': 'STUDENT 15%',
            'badge_color': '#6f42c1',
            'priority': 3,
            'discount_percentage': Decimal('15.00'),
            'description': 'Special discount for students with valid ID',
            'is_featured': False,
            'is_first_time_buyer_only': True,
            'requires_coupon_code': True,
            'coupon_code': 'STUDENT15',
            'max_uses_per_customer': 1,
        },
        {
            'name': 'VIP Member Exclusive',
            'sale_type': 'loyalty_discount',
            'badge_text': 'VIP 20%',
            'badge_color': '#fd79a8',
            'priority': 4,
            'discount_percentage': Decimal('20.00'),
            'minimum_purchase_amount': Decimal('200.00'),
            'description': 'Exclusive discount for VIP members',
            'is_featured': True,
            'is_member_only': True,
            'is_stackable': False,
        },
        {
            'name': 'Free Premium Headphones',
            'sale_type': 'free_item',
            'badge_text': 'FREE HEADPHONES',
            'badge_color': '#28a745',
            'priority': 4,
            'free_item_description': 'Premium wireless headphones',
            'free_item_value': Decimal('150.00'),
            'minimum_purchase_amount': Decimal('500.00'),
            'description': 'Get free premium headphones with laptop purchase over $500',
            'is_featured': True,
            'max_total_uses': 100,
        },
        {
            'name': 'Buy 2 Get 1 Free Accessories',
            'sale_type': 'buy_x_get_y',
            'badge_text': 'BUY 2 GET 1 FREE',
            'badge_color': '#17a2b8',
            'priority': 3,
            'buy_quantity': 2,
            'get_quantity': 1,
            'description': 'Buy any 2 accessories and get the cheapest one free',
            'is_featured': False,
            'is_stackable': True,
        },
        {
            'name': 'Bulk Purchase Discount',
            'sale_type': 'bulk_discount',
            'badge_text': 'BULK 25%',
            'badge_color': '#fd7e14',
            'priority': 3,
            'discount_percentage': Decimal('25.00'),
            'minimum_purchase_amount': Decimal('1000.00'),
            'description': 'Save 25% on orders over $1000',
            'is_featured': False,
            'max_uses_per_customer': 5,
        },
        {
            'name': 'First Time Buyer Special',
            'sale_type': 'first_time_buyer',
            'badge_text': 'WELCOME 10%',
            'badge_color': '#20c997',
            'priority': 2,
            'discount_percentage': Decimal('10.00'),
            'maximum_discount_amount': Decimal('100.00'),
            'description': 'Welcome discount for first-time customers',
            'is_featured': True,
            'is_first_time_buyer_only': True,
            'max_uses_per_customer': 1,
        },
        {
            'name': 'Referral Bonus',
            'sale_type': 'referral_bonus',
            'badge_text': 'REFER & SAVE',
            'badge_color': '#6c757d',
            'priority': 2,
            'discount_amount': Decimal('50.00'),
            'description': 'Get $50 off when you refer a friend',
            'is_featured': False,
            'requires_coupon_code': True,
            'coupon_code': 'REFER50',
        },
        {
            'name': 'Free Shipping Weekend',
            'sale_type': 'free_shipping',
            'badge_text': 'FREE SHIPPING',
            'badge_color': '#007bff',
            'priority': 2,
            'description': 'Free shipping on all orders this weekend',
            'is_featured': True,
            'end_date': timezone.now() + timedelta(days=3),
        },
        {
            'name': 'Clearance Blowout',
            'sale_type': 'clearance',
            'badge_text': 'CLEARANCE 70%',
            'badge_color': '#dc3545',
            'priority': 5,
            'discount_percentage': Decimal('70.00'),
            'maximum_discount_amount': Decimal('300.00'),
            'description': 'Final clearance - up to 70% off selected items',
            'is_featured': True,
            'end_date': timezone.now() + timedelta(days=14),
            'max_total_uses': 500,
        }
    ]

    created_count = 0

    for sale_data in sale_categories:
        # Check if sale category already exists
        if not SaleCategory.objects.filter(name=sale_data['name']).exists():
            sale = SaleCategory.objects.create(**sale_data)
            created_count += 1
            print(f"✅ Created: {sale.name} ({sale.badge_text})")
        else:
            print(f"⚠️ Already exists: {sale_data['name']}")

    print(f"\n🎉 Created {created_count} new sale categories!")
    return created_count

def create_sale_campaigns():
    print("\n📢 Creating Sale Campaigns...")

    campaigns = [
        {
            'name': 'Holiday Season 2024',
            'description': 'Complete holiday shopping campaign with multiple sales',
            'status': 'active',
            'is_featured': True,
            'start_date': timezone.now(),
            'end_date': timezone.now() + timedelta(days=30),
            'target_revenue': Decimal('100000.00'),
            'budget': Decimal('20000.00'),
            'sale_slugs': ['black-friday-mega-sale', 'free-premium-headphones', 'clearance-blowout']
        },
        {
            'name': 'Student Back to School',
            'description': 'Special campaign targeting students',
            'status': 'active',
            'is_featured': False,
            'start_date': timezone.now(),
            'end_date': timezone.now() + timedelta(days=60),
            'target_revenue': Decimal('50000.00'),
            'budget': Decimal('10000.00'),
            'sale_slugs': ['student-discount-program', 'first-time-buyer-special']
        },
        {
            'name': 'VIP Member Appreciation',
            'description': 'Exclusive campaign for VIP members',
            'status': 'scheduled',
            'is_featured': True,
            'start_date': timezone.now() + timedelta(days=7),
            'end_date': timezone.now() + timedelta(days=21),
            'target_revenue': Decimal('75000.00'),
            'budget': Decimal('15000.00'),
            'sale_slugs': ['vip-member-exclusive', 'bulk-purchase-discount']
        }
    ]

    created_count = 0

    for campaign_data in campaigns:
        sale_slugs = campaign_data.pop('sale_slugs', [])

        if not SaleCampaign.objects.filter(name=campaign_data['name']).exists():
            campaign = SaleCampaign.objects.create(**campaign_data)

            # Add sale categories to campaign
            if sale_slugs:
                sales = SaleCategory.objects.filter(slug__in=sale_slugs)
                campaign.sale_categories.set(sales)
                print(f"   Added {sales.count()} sales to campaign")

            created_count += 1
            print(f"✅ Created campaign: {campaign.name}")
        else:
            print(f"⚠️ Campaign already exists: {campaign_data['name']}")

    print(f"\n🎉 Created {created_count} new campaigns!")
    return created_count

def assign_products_to_sales():
    print("\n🔗 Assigning Products to Sales...")

    # Get some products and sales
    products = Product.objects.all()[:15]  # First 15 products
    sales = SaleCategory.objects.all()

    if not products.exists():
        print("⚠️ No products found. Please create some products first.")
        return 0

    assignments_created = 0

    # Smart assignment based on sale types
    laptop_products = Product.objects.filter(name__icontains='laptop')[:3]

    assignment_rules = [
        ('black-friday-mega-sale', products[:8]),  # Major sale gets many products
        ('student-discount-program', products[2:6]),  # Student discount on mid-range
        ('vip-member-exclusive', products[5:10]),  # VIP gets premium products
        ('free-premium-headphones', laptop_products),  # Free item with laptops
        ('buy-2-get-1-free-accessories', products[8:12]),  # Accessories deal
        ('bulk-purchase-discount', products[10:15]),  # Bulk discount on various items
        ('first-time-buyer-special', products[:5]),  # Welcome discount on popular items
        ('clearance-blowout', products[12:15]),  # Clearance on last items
    ]

    for sale_slug, product_list in assignment_rules:
        try:
            sale = SaleCategory.objects.get(slug=sale_slug)
            for product in product_list:
                assignment, created = ProductSaleAssignment.objects.get_or_create(
                    product=product,
                    sale_category=sale,
                    defaults={'is_active': True}
                )
                if created:
                    assignments_created += 1
                    print(f"  ✅ Assigned {product.name} to {sale.name}")
        except SaleCategory.DoesNotExist:
            print(f"  ⚠️ Sale '{sale_slug}' not found")

    print(f"\n🎉 Created {assignments_created} product assignments!")
    return assignments_created

def show_summary():
    print("\n" + "="*80)
    print("📊 SALES MANAGEMENT SYSTEM SUMMARY")
    print("="*80)

    # Sale Categories
    total_sales = SaleCategory.objects.count()
    active_sales = SaleCategory.objects.filter(is_active=True).count()
    featured_sales = SaleCategory.objects.filter(is_featured=True).count()

    print(f"🏷️ Sale Categories:")
    print(f"   Total: {total_sales}")
    print(f"   Active: {active_sales}")
    print(f"   Featured: {featured_sales}")

    # Campaigns
    total_campaigns = SaleCampaign.objects.count()
    active_campaigns = SaleCampaign.objects.filter(status='active').count()

    print(f"\n📢 Campaigns:")
    print(f"   Total: {total_campaigns}")
    print(f"   Active: {active_campaigns}")

    # Product Assignments
    total_assignments = ProductSaleAssignment.objects.count()
    active_assignments = ProductSaleAssignment.objects.filter(is_active=True).count()
    unique_products = ProductSaleAssignment.objects.filter(
        is_active=True
    ).values('product').distinct().count()

    print(f"\n🔗 Product Assignments:")
    print(f"   Total Assignments: {total_assignments}")
    print(f"   Active Assignments: {active_assignments}")
    print(f"   Products in Sales: {unique_products}")

    # Sale Types
    print(f"\n📋 Sale Types Available:")
    sale_types = SaleCategory.objects.values_list('sale_type', flat=True).distinct()
    for sale_type in sale_types:
        count = SaleCategory.objects.filter(sale_type=sale_type).count()
        print(f"   {sale_type}: {count}")

    print(f"\n🚀 Access Points:")
    print(f"   Admin Panel: http://127.0.0.1:8000/admin/sales_management/")
    print(f"   Sale Categories: http://127.0.0.1:8000/admin/sales_management/salecategory/")
    print(f"   Campaigns: http://127.0.0.1:8000/admin/sales_management/salecampaign/")
    print(f"   Product Assignments: http://127.0.0.1:8000/admin/sales_management/productsaleassignment/")

    print(f"\n💡 Management Commands:")
    print(f"   python manage.py sales_admin list-sales")
    print(f"   python manage.py sales_admin create-sale --name 'New Sale' --type percentage --badge-text '20% OFF' --discount-percentage 20")
    print(f"   python manage.py sales_admin assign-products --sale-slug 'new-sale' --product-ids 1 2 3")
    print(f"   python manage.py sales_admin analytics")

if __name__ == '__main__':
    print("🚀 Setting up comprehensive Sales Management system...")

    # Create all the data
    sales_created = create_comprehensive_sale_categories()
    campaigns_created = create_sale_campaigns()
    assignments_created = assign_products_to_sales()

    # Show summary
    show_summary()

    print("\n" + "="*80)
    print("🎉 SALES MANAGEMENT SYSTEM READY!")
    print("="*80)
    print("✅ Your comprehensive sales management system is now fully operational!")
    print("✅ All models are created and populated with sample data")
    print("✅ Admin interface is enhanced and ready to use")
    print("✅ Management commands are available for advanced operations")
    print("✅ The system supports 15 different sale types with full customization")
    print("="*80)
