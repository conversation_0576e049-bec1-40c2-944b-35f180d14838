{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>Delete Product - Admin Dashboard - iTechStore</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        /* Admin Dashboard Styles */
        .admin-container {
            display: flex;
            min-height: calc(100vh - 150px);
        }

        .admin-sidebar {
            width: 250px;
            background-color: #1a1a1a;
            color: white;
            padding: 20px 0;
            flex-shrink: 0;
        }

        .admin-logo {
            padding: 0 20px 20px;
            border-bottom: 1px solid #333;
            margin-bottom: 20px;
        }

        .admin-logo h2 {
            font-size: 20px;
            display: flex;
            align-items: center;
        }

        .admin-logo i {
            margin-right: 10px;
            color: #4dabf7;
        }

        .admin-menu {
            list-style: none;
        }

        .admin-menu li {
            margin-bottom: 5px;
        }

        .admin-menu a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: #aaa;
            transition: all 0.3s;
        }

        .admin-menu a:hover, .admin-menu a.active {
            background-color: #333;
            color: white;
        }

        .admin-menu i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .admin-content {
            flex: 1;
            padding: 30px;
            background-color: #f5f7fa;
            overflow-y: auto;
        }

        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .admin-title h1 {
            font-size: 24px;
            color: #333;
        }

        .admin-user {
            display: flex;
            align-items: center;
        }

        .admin-user-info {
            margin-right: 15px;
            text-align: right;
        }

        .admin-user-name {
            font-weight: 500;
            color: #333;
        }

        .admin-user-role {
            font-size: 12px;
            color: #777;
        }

        .admin-user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #4dabf7;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
        }

        .admin-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 30px;
        }

        .admin-card-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-card-title {
            font-size: 18px;
            color: #333;
            font-weight: 500;
        }

        .admin-card-body {
            padding: 20px;
        }

        /* Delete Confirmation Styles */
        .delete-confirmation {
            text-align: center;
            padding: 30px;
        }

        .delete-icon {
            font-size: 60px;
            color: #f44336;
            margin-bottom: 20px;
        }

        .delete-title {
            font-size: 24px;
            color: #333;
            margin-bottom: 10px;
        }

        .delete-message {
            font-size: 16px;
            color: #777;
            margin-bottom: 30px;
        }

        .product-info {
            background-color: #f5f7fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
            text-align: left;
        }

        .product-info-row {
            display: flex;
            margin-bottom: 10px;
        }

        .product-info-label {
            width: 120px;
            font-weight: 500;
            color: #333;
        }

        .product-info-value {
            flex: 1;
            color: #555;
        }

        .delete-actions {
            display: flex;
            justify-content: center;
            gap: 15px;
        }

        .btn {
            padding: 12px 25px;
            border-radius: 5px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
        }

        .btn-danger {
            background-color: #f44336;
            color: white;
        }

        .btn-danger:hover {
            background-color: #d32f2f;
        }

        .btn-secondary {
            background-color: #f5f5f5;
            color: #333;
        }

        .btn-secondary:hover {
            background-color: #e0e0e0;
        }

        @media (max-width: 992px) {
            .admin-container {
                flex-direction: column;
            }

            .admin-sidebar {
                width: 100%;
                padding: 10px 0;
            }

            .admin-logo {
                padding: 0 15px 15px;
                margin-bottom: 10px;
            }

            .admin-menu a {
                padding: 10px 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Top Navigation Bar -->
    <div class="top-nav">
        <div class="container">
            <div class="top-nav-left">
                <span>Admin Panel</span>
            </div>
            <div class="top-nav-right">
                <span>Welcome, {{ request.user.username }}</span>
                <a href="{% url 'admin_logout' %}" class="sign-in">Logout</a>
            </div>
        </div>
    </div>

    <!-- Admin Container -->
    <div class="admin-container">
        <!-- Admin Sidebar -->
        <div class="admin-sidebar">
            <div class="admin-logo">
                <h2><i class="fas fa-tachometer-alt"></i> Admin Panel</h2>
            </div>
            <ul class="admin-menu">
                <li><a href="{% url 'admin_dashboard' %}"><i class="fas fa-home"></i> Dashboard</a></li>
                <li><a href="{% url 'admin_products' %}" class="active"><i class="fas fa-box"></i> Products</a></li>
                <li><a href="{% url 'admin_users' %}"><i class="fas fa-users"></i> Users</a></li>
                <li><a href="{% url 'admin_orders' %}"><i class="fas fa-shopping-cart"></i> Orders</a></li>
                <li><a href="{% url 'admin_categories' %}"><i class="fas fa-tags"></i> Categories</a></li>
                <li><a href="{% url 'admin_settings' %}"><i class="fas fa-cog"></i> Settings</a></li>
                <li><a href="/django-admin/" target="_blank"><i class="fas fa-tools"></i> Django Admin</a></li>
                <li><a href="{% url 'home' %}"><i class="fas fa-store"></i> View Store</a></li>
                <li><a href="{% url 'admin_logout' %}"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
            </ul>
        </div>

        <!-- Admin Content -->
        <div class="admin-content">
            <div class="admin-header">
                <div class="admin-title">
                    <h1>Delete Product</h1>
                </div>
                <div class="admin-user">
                    <div class="admin-user-info">
                        <div class="admin-user-name">{{ request.user.username }}</div>
                        <div class="admin-user-role">Administrator</div>
                    </div>
                    <div class="admin-user-avatar">
                        {{ request.user.username|first|upper }}
                    </div>
                </div>
            </div>

            <!-- Delete Confirmation -->
            <div class="admin-card">
                <div class="admin-card-header">
                    <div class="admin-card-title">Confirm Deletion</div>
                </div>
                <div class="admin-card-body">
                    <div class="delete-confirmation">
                        <div class="delete-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <h2 class="delete-title">Are you sure you want to delete this product?</h2>
                        <p class="delete-message">This action cannot be undone. All data associated with this product will be permanently removed.</p>

                        <div class="product-info">
                            <div class="product-info-row">
                                <div class="product-info-label">Product Name:</div>
                                <div class="product-info-value">{{ product.name }}</div>
                            </div>
                            <div class="product-info-row">
                                <div class="product-info-label">Price:</div>
                                <div class="product-info-value">${{ product.price }}</div>
                            </div>
                            <div class="product-info-row">
                                <div class="product-info-label">Status:</div>
                                <div class="product-info-value">
                                    {% if product.stock_status == 'available' %}
                                        Available
                                    {% elif product.stock_status == 'preorder' %}
                                        Pre-order
                                    {% else %}
                                        Unavailable
                                    {% endif %}
                                </div>
                            </div>
                            <div class="product-info-row">
                                <div class="product-info-label">Created:</div>
                                <div class="product-info-value">{{ product.created_at|date:"M d, Y" }}</div>
                            </div>
                        </div>

                        <form method="post" action="{% url 'admin_product_delete' product.id %}">
                            {% csrf_token %}
                            <div class="delete-actions">
                                <a href="{% url 'admin_products' %}" class="btn btn-secondary">Cancel</a>
                                <button type="submit" class="btn btn-danger">Delete Product</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{% static 'js/script.js' %}"></script>
</body>
</html>
