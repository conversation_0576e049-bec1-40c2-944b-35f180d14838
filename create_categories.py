import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'authtry.settings')
django.setup()

from authtryapp.models import Category

# Define the categories to create
categories = [
    {
        'name': 'Laptops',
        'slug': 'laptops',
        'description': 'Portable computing devices for work, study, and entertainment.'
    },
    {
        'name': 'Desktops',
        'slug': 'desktops',
        'description': 'Powerful desktop computers for home and office use.'
    },
    {
        'name': 'Gaming',
        'slug': 'gaming',
        'description': 'Gaming laptops, desktops, and accessories for the ultimate gaming experience.'
    },
    {
        'name': 'Audio',
        'slug': 'audio',
        'description': 'Headphones, speakers, and audio equipment for premium sound quality.'
    },
    {
        'name': 'Mobile',
        'slug': 'mobile',
        'description': 'Smartphones, tablets, and mobile accessories for staying connected on the go.'
    },
    {
        'name': 'Smart Home',
        'slug': 'smart-home',
        'description': 'Smart devices and systems to automate and enhance your home.'
    },
    {
        'name': 'Accessories',
        'slug': 'accessories',
        'description': 'Computer peripherals, cables, and other tech accessories.'
    }
]

def create_categories():
    """Create categories if they don't exist"""
    for category_data in categories:
        category, created = Category.objects.get_or_create(
            slug=category_data['slug'],
            defaults={
                'name': category_data['name'],
                'description': category_data['description']
            }
        )
        
        if created:
            print(f"Created category: {category.name}")
        else:
            print(f"Category already exists: {category.name}")

if __name__ == '__main__':
    print("Creating categories...")
    create_categories()
    print("Done!")
