# Generated by Django 5.0.6 on 2025-05-25 07:37

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('authtryapp', '0005_herobanner_image_aspect_ratio_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='herobanner',
            name='carousel_coverage',
            field=models.IntegerField(default=90, help_text='Percentage of carousel width to cover (10-100)'),
        ),
        migrations.AddField(
            model_name='herobanner',
            name='manual_height',
            field=models.IntegerField(blank=True, help_text='Override image height (pixels) - leave blank for auto-detect', null=True),
        ),
        migrations.AddField(
            model_name='herobanner',
            name='manual_width',
            field=models.IntegerField(blank=True, help_text='Override image width (pixels) - leave blank for auto-detect', null=True),
        ),
    ]
