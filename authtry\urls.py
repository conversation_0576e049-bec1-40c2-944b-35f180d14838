from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from authtryapp.admin_views import admin_login, admin_dashboard

# Override the default Django admin site header and title
admin.site.site_header = 'iTechStore Admin'
admin.site.site_title = 'iTechStore Admin Portal'
admin.site.index_title = 'Welcome to iTechStore Admin Portal'

urlpatterns = [
    # Our custom admin panel takes precedence over Django's admin
    path('admin/', admin_login, name='admin_login'),  # Custom admin login
    path('django-admin/', admin.site.urls),  # Django admin panel (moved to a different URL)
    path('', include('authtryapp.urls')),  # Include app URLs
]

# Serve static and media files in development
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
