{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block title %}Remove Products from Sale Category{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">Home</a>
    &rsaquo; <a href="{% url 'admin:authtryapp_product_changelist' %}">Products</a>
    &rsaquo; Remove from Sale Category
</div>
{% endblock %}

{% block content %}
<h1>Remove Products from Sale Category</h1>

<p>You are about to remove the following products from a sale category:</p>

<ul>
    {% for product in products %}
        <li><strong>{{ product.name }}</strong> - ${{ product.price }}</li>
    {% endfor %}
</ul>

{% if sale_categories %}
<form method="post">
    {% csrf_token %}
    
    <div class="form-row">
        <div>
            <label for="sale_category">Select Sale Category to Remove From:</label>
            <select name="sale_category" id="sale_category" required>
                <option value="">Choose a sale category...</option>
                {% for category in sale_categories %}
                    <option value="{{ category.id }}">
                        {{ category.name }} ({{ category.badge_text }})
                        {% if category.is_featured %} - Featured{% endif %}
                    </option>
                {% endfor %}
            </select>
        </div>
    </div>
    
    <div class="submit-row">
        <input type="submit" value="Remove from Sale Category" name="apply" class="default">
        <a href="{% url 'admin:authtryapp_product_changelist' %}" class="button cancel-link">Cancel</a>
    </div>
</form>
{% else %}
<p><strong>Note:</strong> None of the selected products are currently in any sale categories.</p>
<div class="submit-row">
    <a href="{% url 'admin:authtryapp_product_changelist' %}" class="button cancel-link">Back to Products</a>
</div>
{% endif %}

<style>
.form-row {
    margin-bottom: 20px;
}

.form-row label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-row select {
    width: 400px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.submit-row {
    margin-top: 20px;
    padding: 10px 0;
    border-top: 1px solid #ddd;
}

.submit-row input[type="submit"] {
    background: #ba2121;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 10px;
}

.submit-row input[type="submit"]:hover {
    background: #a41515;
}

.cancel-link {
    color: #666;
    text-decoration: none;
    padding: 10px 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.cancel-link:hover {
    background: #f8f8f8;
}
</style>
{% endblock %}
