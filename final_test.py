#!/usr/bin/env python
"""
Final comprehensive test to verify all Hero Banner functionality
"""
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'authtry.settings')
django.setup()

from authtryapp.models import HeroBanner
from authtryapp.templatetags.custom_filters import (
    get_primary_redirect_url,
    get_secondary_redirect_url,
    get_primary_button_text,
    get_secondary_button_text,
    has_secondary_button
)

def test_complete_functionality():
    print("🎯 FINAL COMPREHENSIVE TEST")
    print("=" * 60)
    
    # Test 1: Database and Model
    print("\n1. 📊 Database & Model Test:")
    banners = HeroBanner.objects.filter(is_active=True).order_by('order')
    print(f"   ✅ Active Banners: {banners.count()}")
    
    if banners.count() == 0:
        print("   ❌ No active banners - creating test banner...")
        banner = HeroBanner.objects.create(
            title="Test Banner",
            description="Test description",
            link_type="custom",
            link_target="#test",
            cta_label="Test Button",
            order=1
        )
        print(f"   ✅ Created test banner: {banner.title}")
        banners = HeroBanner.objects.filter(is_active=True)
    
    # Test 2: Template Filters
    print("\n2. 🎨 Template Filters Test:")
    test_banner = banners.first()
    
    try:
        primary_url = get_primary_redirect_url(test_banner)
        primary_text = get_primary_button_text(test_banner)
        secondary_url = get_secondary_redirect_url(test_banner)
        secondary_text = get_secondary_button_text(test_banner)
        has_secondary = has_secondary_button(test_banner)
        
        print(f"   ✅ Primary URL: {primary_url}")
        print(f"   ✅ Primary Text: {primary_text}")
        print(f"   ✅ Secondary URL: {secondary_url}")
        print(f"   ✅ Secondary Text: {secondary_text}")
        print(f"   ✅ Has Secondary: {has_secondary}")
        
    except Exception as e:
        print(f"   ❌ Template filters failed: {e}")
        return False
    
    # Test 3: Template Rendering
    print("\n3. 📄 Template Rendering Test:")
    from django.template import Template, Context
    
    template_string = """
    {% load custom_filters %}
    {% for banner in banners %}
    <div class="banner">
        <h1>{{ banner.title }}</h1>
        <p>{{ banner.description }}</p>
        <a href="{{ banner|get_primary_redirect_url }}">{{ banner|get_primary_button_text }}</a>
        {% if banner|has_secondary_button %}
        <a href="{{ banner|get_secondary_redirect_url }}">{{ banner|get_secondary_button_text }}</a>
        {% endif %}
    </div>
    {% endfor %}
    """
    
    try:
        template = Template(template_string)
        context = Context({'banners': banners[:2]})
        rendered = template.render(context)
        print("   ✅ Template rendered successfully!")
        
    except Exception as e:
        print(f"   ❌ Template rendering failed: {e}")
        return False
    
    # Test 4: Different Link Types
    print("\n4. 🔗 Link Types Test:")
    link_types = ['product', 'category', 'anchor', 'custom']
    
    for link_type in link_types:
        test_banners = banners.filter(link_type=link_type)
        if test_banners.exists():
            banner = test_banners.first()
            url = get_primary_redirect_url(banner)
            print(f"   ✅ {link_type.title()}: {banner.title} → {url}")
        else:
            print(f"   ⚠️  {link_type.title()}: No banners found")
    
    # Test 5: Navigation Visibility
    print("\n5. 🧭 Navigation Test:")
    active_count = banners.count()
    
    if active_count > 1:
        print(f"   ✅ {active_count} banners - Navigation should be visible")
        print("   ✅ Left arrow (‹) should be visible")
        print("   ✅ Right arrow (›) should be visible")
        print(f"   ✅ {active_count} navigation dots should be visible")
    else:
        print(f"   ⚠️  Only {active_count} banner - Navigation may be hidden")
    
    # Test 6: Image Dimensions
    print("\n6. 🖼️  Image Dimensions Test:")
    banners_with_dims = banners.filter(
        image_width__isnull=False,
        image_height__isnull=False
    )
    
    if banners_with_dims.exists():
        for banner in banners_with_dims[:3]:
            css = banner.get_image_dimensions_css()
            print(f"   ✅ {banner.title}: {css}")
    else:
        print("   ⚠️  No banners with custom dimensions")
    
    return True

def test_admin_functionality():
    print("\n7. 🔧 Admin Functionality Test:")
    
    # Test admin methods
    banner = HeroBanner.objects.first()
    if banner:
        try:
            info = banner.get_image_info()
            print(f"   ✅ Image Info: {info}")
            
            # Test URL preview (admin method)
            from authtryapp.admin import HeroBannerAdmin
            from django.contrib.admin.sites import site
            
            admin_instance = HeroBannerAdmin(HeroBanner, site)
            preview = admin_instance.get_redirect_url_preview(banner)
            print(f"   ✅ URL Preview: {preview}")
            
        except Exception as e:
            print(f"   ❌ Admin methods failed: {e}")
            return False
    
    return True

if __name__ == "__main__":
    print("🚀 Starting Final Comprehensive Test...")
    
    model_ok = test_complete_functionality()
    admin_ok = test_admin_functionality()
    
    print("\n" + "=" * 60)
    print("📋 FINAL RESULTS:")
    
    if model_ok and admin_ok:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Template syntax error RESOLVED")
        print("✅ All filters working correctly")
        print("✅ Navigation should be visible")
        print("✅ All link types functional")
        print("✅ Admin panel ready")
        
        print("\n🚀 READY TO USE:")
        print("1. Visit homepage - should load without errors")
        print("2. See carousel with navigation arrows and dots")
        print("3. Test all button links")
        print("4. Access admin panel for banner management")
        
    else:
        print("❌ Some tests failed - check errors above")
    
    print("\n📍 Admin Panel: /admin/authtryapp/herobanner/")
    print("📍 Homepage: /")
    print("=" * 60)
