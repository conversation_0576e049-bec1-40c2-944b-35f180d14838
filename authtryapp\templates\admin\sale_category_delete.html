{% extends 'admin/base_admin.html' %}
{% load static %}

{% block title %}Delete Sale Category - {{ sale.name }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'admin/css/sales_management_admin.css' %}">
<style>
.delete-page {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
}

.delete-container {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 3px solid #dc3545;
}

.warning-header {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 25px;
    text-align: center;
}

.warning-header i {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
}

.sale-info {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 25px;
}

.sale-badge-display {
    display: inline-block;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: bold;
    color: white;
    margin-right: 15px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.impact-section {
    background: #fff3cd;
    border: 2px solid #ffc107;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 25px;
}

.impact-section h4 {
    color: #856404;
    margin-bottom: 15px;
}

.impact-list {
    list-style: none;
    padding: 0;
}

.impact-list li {
    padding: 8px 0;
    border-bottom: 1px solid #f0e68c;
    display: flex;
    justify-content: between;
    align-items: center;
}

.impact-list li:last-child {
    border-bottom: none;
}

.impact-list li i {
    color: #856404;
    margin-right: 10px;
    width: 20px;
}

.products-list {
    max-height: 200px;
    overflow-y: auto;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 10px;
    margin-top: 10px;
}

.product-item {
    padding: 8px 12px;
    border-bottom: 1px solid #f8f9fa;
    display: flex;
    justify-content: between;
    align-items: center;
}

.product-item:last-child {
    border-bottom: none;
}

.product-name {
    font-weight: 600;
    color: #333;
}

.product-price {
    color: #28a745;
    font-weight: bold;
}

.confirmation-section {
    background: #f8d7da;
    border: 2px solid #dc3545;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 25px;
}

.confirmation-checkbox {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 15px;
}

.confirmation-checkbox input[type="checkbox"] {
    transform: scale(1.3);
}

.confirmation-checkbox label {
    font-weight: 600;
    color: #721c24;
    margin: 0;
    cursor: pointer;
}

.action-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
    color: white;
    text-decoration: none;
}

.btn-danger:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.stat-item {
    background: white;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #dee2e6;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #dc3545;
    display: block;
}

.stat-label {
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}
</style>
{% endblock %}

{% block content %}
<div class="delete-page">
    <div class="delete-container">
        <div class="warning-header">
            <i class="fas fa-exclamation-triangle"></i>
            <h2>⚠️ Delete Sale Category</h2>
            <p class="mb-0">This action cannot be undone!</p>
        </div>

        <!-- Sale Information -->
        <div class="sale-info">
            <h3>
                <span class="sale-badge-display" style="background-color: {{ sale.badge_color }};">
                    {{ sale.badge_text }}
                </span>
                {{ sale.name }}
            </h3>
            <div class="row mt-3">
                <div class="col-md-6">
                    <p><strong>Type:</strong> {{ sale.get_sale_type_display }}</p>
                    <p><strong>Priority:</strong> {{ sale.priority }}/5</p>
                    <p><strong>Status:</strong> 
                        {% if sale.is_active %}
                            <span class="badge badge-success">Active</span>
                        {% else %}
                            <span class="badge badge-secondary">Inactive</span>
                        {% endif %}
                        {% if sale.is_featured %}
                            <span class="badge badge-warning">Featured</span>
                        {% endif %}
                    </p>
                </div>
                <div class="col-md-6">
                    {% if sale.discount_percentage %}
                    <p><strong>Discount:</strong> {{ sale.discount_percentage }}%</p>
                    {% elif sale.discount_amount %}
                    <p><strong>Discount:</strong> ₨{{ sale.discount_amount }}</p>
                    {% endif %}
                    <p><strong>Uses:</strong> {{ sale.current_uses }}</p>
                    <p><strong>Created:</strong> {{ sale.created_at|date:"M d, Y" }}</p>
                </div>
            </div>
            {% if sale.description %}
            <p><strong>Description:</strong> {{ sale.description }}</p>
            {% endif %}
        </div>

        <!-- Impact Assessment -->
        <div class="impact-section">
            <h4>🔍 Deletion Impact Assessment</h4>
            <ul class="impact-list">
                <li>
                    <span><i class="fas fa-box"></i> <strong>{{ assigned_products.count }}</strong> product assignments will be removed</span>
                </li>
                <li>
                    <span><i class="fas fa-chart-line"></i> All usage logs and analytics data will be lost</span>
                </li>
                <li>
                    <span><i class="fas fa-bell"></i> Related notifications will be deleted</span>
                </li>
                <li>
                    <span><i class="fas fa-users"></i> Customer cart items with this sale will be affected</span>
                </li>
                <li>
                    <span><i class="fas fa-campaign"></i> Sale will be removed from any campaigns</span>
                </li>
            </ul>

            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-number">{{ assigned_products.count }}</span>
                    <span class="stat-label">Products Affected</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{{ sale.current_uses }}</span>
                    <span class="stat-label">Total Uses</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{{ sale.created_at|timesince }}</span>
                    <span class="stat-label">Age</span>
                </div>
            </div>
        </div>

        <!-- Assigned Products -->
        {% if assigned_products %}
        <div class="impact-section">
            <h4>📦 Products Currently Assigned ({{ assigned_products.count }})</h4>
            <p>These products will lose their sale assignment:</p>
            <div class="products-list">
                {% for assignment in assigned_products %}
                <div class="product-item">
                    <div>
                        <div class="product-name">{{ assignment.product.name }}</div>
                        <small class="text-muted">{{ assignment.product.category.name }}</small>
                    </div>
                    <div class="product-price">₨{{ assignment.product.price }}</div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Confirmation Section -->
        <div class="confirmation-section">
            <h4>✋ Confirmation Required</h4>
            <p>Please confirm that you understand the consequences of deleting this sale category:</p>
            
            <div class="confirmation-checkbox">
                <input type="checkbox" id="confirmDelete" required>
                <label for="confirmDelete">
                    I understand that this action is permanent and cannot be undone
                </label>
            </div>
        </div>

        <!-- Action Buttons -->
        <form method="post" id="deleteForm">
            {% csrf_token %}
            <div class="action-buttons">
                <a href="{% url 'admin_sales_categories' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    Cancel - Keep Sale
                </a>
                <button type="submit" class="btn btn-danger" id="deleteButton" disabled>
                    <i class="fas fa-trash"></i>
                    Delete Sale Category
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const confirmCheckbox = document.getElementById('confirmDelete');
    const deleteButton = document.getElementById('deleteButton');
    const deleteForm = document.getElementById('deleteForm');
    
    // Enable/disable delete button based on confirmation
    confirmCheckbox.addEventListener('change', function() {
        deleteButton.disabled = !this.checked;
        if (this.checked) {
            deleteButton.style.opacity = '1';
            deleteButton.style.cursor = 'pointer';
        } else {
            deleteButton.style.opacity = '0.5';
            deleteButton.style.cursor = 'not-allowed';
        }
    });
    
    // Final confirmation on form submit
    deleteForm.addEventListener('submit', function(e) {
        if (!confirmCheckbox.checked) {
            e.preventDefault();
            alert('Please confirm that you understand the consequences of deletion.');
            return;
        }
        
        const finalConfirm = confirm(
            '🚨 FINAL WARNING 🚨\n\n' +
            'You are about to permanently delete the sale category "{{ sale.name }}".\n\n' +
            'This will:\n' +
            '• Remove {{ assigned_products.count }} product assignments\n' +
            '• Delete all usage logs and analytics\n' +
            '• Cannot be undone\n\n' +
            'Are you absolutely sure you want to proceed?'
        );
        
        if (!finalConfirm) {
            e.preventDefault();
        }
    });
    
    // Add warning when user tries to leave page
    let formSubmitted = false;
    deleteForm.addEventListener('submit', function() {
        formSubmitted = true;
    });
    
    window.addEventListener('beforeunload', function(e) {
        if (confirmCheckbox.checked && !formSubmitted) {
            e.preventDefault();
            e.returnValue = 'You have confirmed deletion but not submitted the form. Are you sure you want to leave?';
        }
    });
});
</script>
{% endblock %}
