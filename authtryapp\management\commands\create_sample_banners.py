from django.core.management.base import BaseCommand
from authtryapp.models import HeroBanner, SiteConfiguration


class Command(BaseCommand):
    help = 'Create sample hero banners and site configuration'

    def handle(self, *args, **options):
        # Create site configuration if it doesn't exist
        site_config, created = SiteConfiguration.objects.get_or_create(
            defaults={
                'site_name': 'iTechStore',
                'site_tagline': 'Your Technology Destination',
                'hero_auto_slide_interval': 5000,
                'hero_show_arrows': True,
                'hero_show_dots': True,
                'products_per_row_desktop': 4,
                'products_per_row_tablet': 2,
                'products_per_row_mobile': 1,
            }
        )
        
        if created:
            self.stdout.write(
                self.style.SUCCESS('Successfully created site configuration')
            )
        else:
            self.stdout.write('Site configuration already exists')

        # Create sample hero banners
        banners_data = [
            {
                'title': 'AMD Radeon™ RX\n9070 XT GPU',
                'subtitle': 'Experience next-generation gaming with cutting-edge performance and stunning visuals.',
                'brand_text': 'SAPPHIRE PULSE',
                'primary_button_text': 'AVAILABLE NOW',
                'primary_button_url': '#featured-products',
                'secondary_button_text': 'ITECHSTORE',
                'secondary_button_url': '#',
                'background_gradient_start': '#8B5CF6',
                'background_gradient_end': '#EC4899',
                'image_alt': 'AMD GPU',
                'display_order': 1,
            },
            {
                'title': 'Ultimate Gaming\nExperience',
                'subtitle': 'High-performance gaming laptops designed for competitive gaming and content creation.',
                'brand_text': 'GAMING SERIES',
                'primary_button_text': 'SHOP NOW',
                'primary_button_url': '#featured-products',
                'secondary_button_text': 'LEARN MORE',
                'secondary_button_url': '#',
                'background_gradient_start': '#667eea',
                'background_gradient_end': '#764ba2',
                'image_alt': 'Gaming Laptop',
                'display_order': 2,
            },
            {
                'title': 'Connected\nLifestyle',
                'subtitle': 'Transform your home with intelligent devices that adapt to your daily routine.',
                'brand_text': 'SMART TECH',
                'primary_button_text': 'EXPLORE',
                'primary_button_url': '#featured-products',
                'secondary_button_text': 'DISCOVER',
                'secondary_button_url': '#',
                'background_gradient_start': '#f093fb',
                'background_gradient_end': '#f5576c',
                'image_alt': 'Smart Devices',
                'display_order': 3,
            },
        ]

        created_count = 0
        for banner_data in banners_data:
            banner, created = HeroBanner.objects.get_or_create(
                title=banner_data['title'],
                defaults=banner_data
            )
            if created:
                created_count += 1

        self.stdout.write(
            self.style.SUCCESS(f'Successfully created {created_count} hero banners')
        )
        
        total_banners = HeroBanner.objects.count()
        self.stdout.write(f'Total hero banners in database: {total_banners}')
