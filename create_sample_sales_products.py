#!/usr/bin/env python
"""
<PERSON>ript to create sample sales products with ratings, reviews, and discounts
"""
import os
import sys
import django
from decimal import Decimal
import random

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'authtry.settings')
django.setup()

from authtryapp.models import Product, Category, Brand

def create_sample_sales_products():
    print("🛍️ Creating Sample Sales Products...")

    # Get or create categories
    categories_data = [
        {'name': 'Laptops', 'description': 'High-performance laptops'},
        {'name': 'Monitors', 'description': 'Professional displays'},
        {'name': 'Gaming', 'description': 'Gaming equipment'},
        {'name': 'Audio', 'description': 'Audio equipment'},
        {'name': 'Accessories', 'description': 'Tech accessories'},
    ]

    categories = {}
    for cat_data in categories_data:
        category, created = Category.objects.get_or_create(
            name=cat_data['name'],
            defaults={'description': cat_data['description']}
        )
        categories[cat_data['name']] = category
        if created:
            print(f"✅ Created category: {category.name}")

    # Get or create brands
    brands_data = [
        {'name': 'Dell', 'description': 'Dell Technologies'},
        {'name': 'HP', 'description': 'HP Inc.'},
        {'name': 'Lenovo', 'description': 'Lenovo Group'},
        {'name': 'ASUS', 'description': 'ASUSTeK Computer'},
        {'name': 'Samsung', 'description': 'Samsung Electronics'},
        {'name': 'LG', 'description': 'LG Electronics'},
        {'name': 'Sony', 'description': 'Sony Corporation'},
        {'name': 'Logitech', 'description': 'Logitech International'},
    ]

    brands = {}
    for brand_data in brands_data:
        try:
            # Try to get existing brand first
            brand = Brand.objects.get(name=brand_data['name'])
            brands[brand_data['name']] = brand
            print(f"🔍 Found existing brand: {brand.name}")
        except Brand.DoesNotExist:
            # Create new brand if it doesn't exist
            try:
                brand = Brand.objects.create(
                    name=brand_data['name'],
                    description=brand_data['description']
                )
                brands[brand_data['name']] = brand
                print(f"✅ Created brand: {brand.name}")
            except Exception as e:
                print(f"❌ Error creating brand {brand_data['name']}: {e}")
                # Try to find by slug if name creation failed
                try:
                    from django.utils.text import slugify
                    slug = slugify(brand_data['name'])
                    brand = Brand.objects.get(slug=slug)
                    brands[brand_data['name']] = brand
                    print(f"🔍 Found brand by slug: {brand.name}")
                except Brand.DoesNotExist:
                    print(f"⚠️ Skipping brand {brand_data['name']} - could not create or find")
                    brands[brand_data['name']] = None

    # Sample sales products data
    sales_products_data = [
        {
            'name': 'Dell XPS 13 Laptop',
            'short_specs': '13.3" FHD, Intel i7, 16GB RAM, 512GB SSD',
            'category': 'Laptops',
            'brand': 'Dell',
            'original_price': Decimal('1299.99'),
            'price': Decimal('999.99'),
            'rating': Decimal('4.5'),
            'review_count': 127,
            'is_sponsored': False,
            'has_gift': True,
            'gift_description': 'Free wireless mouse',
            'description': 'Ultra-portable laptop with stunning display and all-day battery life.'
        },
        {
            'name': 'HP Spectre x360 14',
            'short_specs': '14" OLED Touch, Intel i7, 16GB RAM, 1TB SSD',
            'category': 'Laptops',
            'brand': 'HP',
            'original_price': Decimal('1599.99'),
            'price': Decimal('1199.99'),
            'rating': Decimal('4.7'),
            'review_count': 89,
            'is_sponsored': True,
            'has_gift': False,
            'gift_description': '',
            'description': 'Premium 2-in-1 laptop with OLED display and versatile design.'
        },
        {
            'name': 'Samsung 27" 4K Monitor',
            'short_specs': '27" 4K UHD, HDR10, USB-C, 60Hz',
            'category': 'Monitors',
            'brand': 'Samsung',
            'original_price': Decimal('449.99'),
            'price': Decimal('329.99'),
            'rating': Decimal('4.3'),
            'review_count': 203,
            'is_sponsored': False,
            'has_gift': True,
            'gift_description': 'Free HDMI cable',
            'description': 'Professional 4K monitor with vibrant colors and multiple connectivity options.'
        },
        {
            'name': 'LG UltraWide 34" Monitor',
            'short_specs': '34" QHD Curved, 144Hz, G-Sync Compatible',
            'category': 'Monitors',
            'brand': 'LG',
            'original_price': Decimal('699.99'),
            'price': Decimal('549.99'),
            'rating': Decimal('4.6'),
            'review_count': 156,
            'is_sponsored': False,
            'has_gift': False,
            'gift_description': '',
            'description': 'Immersive ultrawide gaming monitor with high refresh rate.'
        },
        {
            'name': 'ASUS ROG Gaming Laptop',
            'short_specs': '15.6" FHD 144Hz, RTX 4060, AMD Ryzen 7, 16GB',
            'category': 'Gaming',
            'brand': 'ASUS',
            'original_price': Decimal('1399.99'),
            'price': Decimal('1099.99'),
            'rating': Decimal('4.4'),
            'review_count': 94,
            'is_sponsored': True,
            'has_gift': True,
            'gift_description': 'Free gaming headset',
            'description': 'High-performance gaming laptop with RTX graphics and fast display.'
        },
        {
            'name': 'Sony WH-1000XM5 Headphones',
            'short_specs': 'Wireless, Noise Canceling, 30hr Battery',
            'category': 'Audio',
            'brand': 'Sony',
            'original_price': Decimal('399.99'),
            'price': Decimal('299.99'),
            'rating': Decimal('4.8'),
            'review_count': 312,
            'is_sponsored': False,
            'has_gift': False,
            'gift_description': '',
            'description': 'Industry-leading noise canceling headphones with premium sound quality.'
        },
        {
            'name': 'Logitech MX Master 3S Mouse',
            'short_specs': 'Wireless, Ergonomic, Multi-device, USB-C',
            'category': 'Accessories',
            'brand': 'Logitech',
            'original_price': Decimal('99.99'),
            'price': Decimal('79.99'),
            'rating': Decimal('4.5'),
            'review_count': 178,
            'is_sponsored': False,
            'has_gift': True,
            'gift_description': 'Free mouse pad',
            'description': 'Advanced wireless mouse with precision tracking and customizable buttons.'
        },
        {
            'name': 'Dell UltraSharp 32" 4K Monitor',
            'short_specs': '32" 4K IPS, 99% sRGB, USB-C Hub, Height Adjustable',
            'category': 'Monitors',
            'brand': 'Dell',
            'original_price': Decimal('799.99'),
            'price': Decimal('599.99'),
            'rating': Decimal('4.7'),
            'review_count': 145,
            'is_sponsored': True,
            'has_gift': False,
            'gift_description': '',
            'description': 'Professional 4K monitor with accurate colors and comprehensive connectivity.'
        },
    ]

    created_count = 0
    updated_count = 0

    for product_data in sales_products_data:
        # Get category and brand
        category = categories.get(product_data['category'])
        brand = brands.get(product_data['brand'])

        # Skip if brand is None (couldn't be created/found)
        if brand is None:
            print(f"⚠️ Skipping product {product_data['name']} - brand not available")
            continue

        # Check if product already exists
        product, created = Product.objects.get_or_create(
            name=product_data['name'],
            defaults={
                'short_specs': product_data['short_specs'],
                'category': category,
                'brand': brand,
                'original_price': product_data['original_price'],
                'price': product_data['price'],
                'rating': product_data['rating'],
                'review_count': product_data['review_count'],
                'is_sponsored': product_data['is_sponsored'],
                'has_gift': product_data['has_gift'],
                'gift_description': product_data['gift_description'],
                'description': product_data['description'],
                'stock_status': 'available'
            }
        )

        if created:
            created_count += 1
            print(f"✅ Created: {product.name} - ${product.price} (was ${product.original_price})")
        else:
            # Update existing product with new sales data
            product.short_specs = product_data['short_specs']
            product.category = category
            product.brand = brand
            product.original_price = product_data['original_price']
            product.price = product_data['price']
            product.rating = product_data['rating']
            product.review_count = product_data['review_count']
            product.is_sponsored = product_data['is_sponsored']
            product.has_gift = product_data['has_gift']
            product.gift_description = product_data['gift_description']
            product.description = product_data['description']
            product.save()
            updated_count += 1
            print(f"🔄 Updated: {product.name} - ${product.price} (was ${product.original_price})")

    print(f"\n📊 Summary:")
    print(f"✅ Created: {created_count} products")
    print(f"🔄 Updated: {updated_count} products")
    print(f"💰 Total sales products: {created_count + updated_count}")

    # Show discount summary
    print(f"\n💸 Discount Summary:")
    for product_data in sales_products_data:
        original = product_data['original_price']
        current = product_data['price']
        discount = original - current
        percentage = (discount / original) * 100
        print(f"   {product_data['name']}: {percentage:.0f}% off (Save ${discount})")

if __name__ == "__main__":
    create_sample_sales_products()
    print("\n🎉 Sample sales products created successfully!")
    print("🌐 Visit the homepage to see the sales products section!")
    print("🛒 Visit /sales/ for the dedicated sales products page!")
