{% extends 'admin/base_admin.html' %}

{% block title %}Manage Section Headings{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="mt-4">Manage Section Headings</h1>
    
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'admin_dashboard' %}">Dashboard</a></li>
            <li class="breadcrumb-item active">Section Headings</li>
        </ol>
    </nav>
    
    {% if messages %}
    <div class="row">
        <div class="col-12">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-heading me-1"></i>
            Section Headings
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>Section Type</th>
                            <th>Title</th>
                            <th>Subtitle</th>
                            <th>Status</th>
                            <th>Last Updated</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for heading in headings %}
                        <tr>
                            <td>{{ heading.get_section_type_display }}</td>
                            <td>{{ heading.title }}</td>
                            <td>{{ heading.subtitle|default:"-" }}</td>
                            <td>
                                {% if heading.is_active %}
                                <span class="badge bg-success">Active</span>
                                {% else %}
                                <span class="badge bg-secondary">Inactive</span>
                                {% endif %}
                            </td>
                            <td>{{ heading.updated_at|date:"M d, Y H:i" }}</td>
                            <td>
                                <a href="{% url 'admin_section_heading_edit' heading.id %}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">No section headings found. Create some below.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-plus-circle me-1"></i>
            Add New Section Heading
        </div>
        <div class="card-body">
            <form method="post" action="{% url 'admin_section_headings' %}">
                {% csrf_token %}
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="section_type" class="form-label">Section Type*</label>
                            <select name="section_type" id="section_type" class="form-control" required>
                                <option value="">Select a section type</option>
                                {% for value, display in section_types %}
                                <option value="{{ value }}">{{ display }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="title" class="form-label">Title*</label>
                            <input type="text" name="title" id="title" class="form-control" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="subtitle" class="form-label">Subtitle</label>
                            <input type="text" name="subtitle" id="subtitle" class="form-control">
                        </div>
                    </div>
                </div>
                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" name="is_active" id="is_active" checked>
                    <label class="form-check-label" for="is_active">
                        Active
                    </label>
                </div>
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-save"></i> Save Section Heading
                </button>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Check for existing section types and disable them in the dropdown
        const existingTypes = [
            {% for heading in headings %}
            "{{ heading.section_type }}",
            {% endfor %}
        ];
        
        const sectionTypeSelect = document.getElementById('section_type');
        if (sectionTypeSelect) {
            Array.from(sectionTypeSelect.options).forEach(option => {
                if (existingTypes.includes(option.value) && option.value !== '') {
                    option.disabled = true;
                    option.text += ' (Already exists)';
                }
            });
        }
    });
</script>
{% endblock %}
