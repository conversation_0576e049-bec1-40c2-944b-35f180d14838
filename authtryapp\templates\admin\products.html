{% extends 'admin/base_admin.html' %}

{% block title %}Products Management{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mt-4 mb-4">
        <h1>Products Management</h1>
        <a href="{% url 'admin_product_add' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add New Product
        </a>
    </div>
    
    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-filter me-1"></i>
            Filter Products
        </div>
        <div class="card-body">
            <form method="get" action="{% url 'admin_products' %}" class="row g-3">
                <div class="col-md-4">
                    <label for="category" class="form-label">Category</label>
                    <select name="category" id="category" class="form-select">
                        <option value="">All Categories</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}" {% if current_category_id == category.id|stringformat:"s" %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" name="search" id="search" class="form-control" placeholder="Search products..." value="{{ search_query|default:'' }}">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search"></i> Apply Filters
                    </button>
                    <a href="{% url 'admin_products' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-undo"></i> Reset
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Products Table -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            All Products {% if current_category_id %}in Selected Category{% endif %}
            {% if products %}({{ products.paginator.count }}){% endif %}
        </div>
        <div class="card-body">
            {% if products %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Image</th>
                            <th>Name</th>
                            <th>Category</th>
                            <th>Price</th>
                            <th>Stock Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for product in products %}
                        <tr>
                            <td style="width: 80px;">
                                {% if product.image %}
                                <img src="{{ product.image.url }}" alt="{{ product.name }}" style="width: 50px; height: 50px; object-fit: cover;">
                                {% else %}
                                <div class="bg-light d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                    <i class="fas fa-box text-muted"></i>
                                </div>
                                {% endif %}
                            </td>
                            <td>{{ product.name }}</td>
                            <td>
                                {% if product.category %}
                                <a href="{% url 'admin_products_by_category' product.category.id %}">
                                    {{ product.category.name }}
                                </a>
                                {% else %}
                                <span class="text-muted">No category</span>
                                {% endif %}
                            </td>
                            <td>${{ product.price|default:"N/A" }}</td>
                            <td>
                                <span class="badge {% if product.stock_status == 'available' %}bg-success{% elif product.stock_status == 'preorder' %}bg-warning text-dark{% else %}bg-danger{% endif %}">
                                    {{ product.get_stock_status_display }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="{% url 'admin_product_edit' product.id %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'admin_product_view' product.id %}" class="btn btn-sm btn-outline-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'admin_product_delete' product.id %}" class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if products.paginator.num_pages > 1 %}
            <nav aria-label="Product pagination">
                <ul class="pagination justify-content-center mt-4">
                    {% if products.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if current_category_id %}&category={{ current_category_id }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ products.previous_page_number }}{% if current_category_id %}&category={{ current_category_id }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% endif %}

                    {% for i in products.paginator.page_range %}
                    {% if products.number == i %}
                    <li class="page-item active">
                        <a class="page-link" href="?page={{ i }}{% if current_category_id %}&category={{ current_category_id }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}">{{ i }}</a>
                    </li>
                    {% elif i > products.number|add:"-3" and i < products.number|add:"3" %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ i }}{% if current_category_id %}&category={{ current_category_id }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}">{{ i }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}

                    {% if products.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ products.next_page_number }}{% if current_category_id %}&category={{ current_category_id }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ products.paginator.num_pages }}{% if current_category_id %}&category={{ current_category_id }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                <h4>No Products Found</h4>
                {% if current_category_id or search_query %}
                <p class="text-muted">No products match your filter criteria</p>
                <a href="{% url 'admin_products' %}" class="btn btn-outline-secondary mt-2">
                    <i class="fas fa-undo"></i> Reset Filters
                </a>
                {% else %}
                <p class="text-muted">Get started by adding your first product</p>
                <a href="{% url 'admin_product_add' %}" class="btn btn-primary mt-2">
                    <i class="fas fa-plus"></i> Add Product
                </a>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
    
    <!-- Categories Quick Links -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-tags me-1"></i>
            Categories
        </div>
        <div class="card-body">
            <div class="row">
                {% for category in categories %}
                <div class="col-md-3 col-sm-6 mb-3">
                    <a href="{% url 'admin_products_by_category' category.id %}" class="text-decoration-none">
                        <div class="card h-100 border-0 shadow-sm {% if current_category_id == category.id|stringformat:"s" %}border border-primary{% endif %}">
                            <div class="card-body d-flex align-items-center">
                                {% if category.image %}
                                <img src="{{ category.image.url }}" alt="{{ category.name }}" class="rounded-circle me-3" style="width: 40px; height: 40px; object-fit: cover;">
                                {% else %}
                                <div class="bg-light rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                    <i class="fas fa-tag text-muted"></i>
                                </div>
                                {% endif %}
                                <div>
                                    <h6 class="mb-0">{{ category.name }}</h6>
                                    <small class="text-muted">{{ category.products.count }} products</small>
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}
.btn-group .btn {
    padding: 0.25rem 0.5rem;
}
.table th {
    background-color: #f8f9fa;
    font-weight: 600;
}
.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}
</style>
{% endblock %} 