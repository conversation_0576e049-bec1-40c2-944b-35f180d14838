// Sale Category Admin JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Badge preview functionality
    function updateBadgePreview() {
        const badgeTextInput = document.querySelector('#id_badge_text');
        const badgeColorInput = document.querySelector('#id_badge_color');
        
        if (badgeTextInput && badgeColorInput) {
            // Create preview container if it doesn't exist
            let previewContainer = document.querySelector('.badge-preview-container');
            if (!previewContainer) {
                previewContainer = document.createElement('div');
                previewContainer.className = 'badge-preview-container';
                previewContainer.innerHTML = '<span class="badge-preview-label">Badge Preview:</span><span class="badge-preview"></span>';
                
                // Insert after the badge color field
                const badgeColorField = badgeColorInput.closest('.form-row');
                if (badgeColorField) {
                    badgeColorField.parentNode.insertBefore(previewContainer, badgeColorField.nextSibling);
                }
            }
            
            const previewBadge = previewContainer.querySelector('.badge-preview');
            
            function updatePreview() {
                const text = badgeTextInput.value || 'SAMPLE';
                const color = badgeColorInput.value || '#dc3545';
                
                previewBadge.style.backgroundColor = color;
                previewBadge.style.color = 'white';
                previewBadge.style.padding = '4px 8px';
                previewBadge.style.borderRadius = '4px';
                previewBadge.style.fontSize = '11px';
                previewBadge.style.fontWeight = 'bold';
                previewBadge.style.textTransform = 'uppercase';
                previewBadge.style.letterSpacing = '0.5px';
                previewBadge.textContent = text;
            }
            
            // Update preview on input change
            badgeTextInput.addEventListener('input', updatePreview);
            badgeColorInput.addEventListener('input', updatePreview);
            
            // Initial preview
            updatePreview();
        }
    }
    
    // Sale type field helper
    function setupSaleTypeHelper() {
        const saleTypeSelect = document.querySelector('#id_sale_type');
        if (saleTypeSelect) {
            // Create helper text container
            const helperContainer = document.createElement('div');
            helperContainer.className = 'sale-type-helper';
            helperContainer.style.marginTop = '10px';
            helperContainer.style.padding = '10px';
            helperContainer.style.backgroundColor = '#f8f9fa';
            helperContainer.style.border = '1px solid #e9ecef';
            helperContainer.style.borderRadius = '4px';
            helperContainer.style.fontSize = '13px';
            
            saleTypeSelect.parentNode.appendChild(helperContainer);
            
            const saleTypeHelp = {
                'percentage': 'Fill in the "Discount percentage" field (e.g., 10 for 10% off)',
                'fixed_amount': 'Fill in the "Discount amount" field (e.g., 100 for $100 off)',
                'free_item': 'Fill in the "Free item description" field (e.g., "Free wireless mouse")',
                'buy_x_get_y': 'Fill in both "Buy quantity" and "Get quantity" fields (e.g., Buy 2, Get 1)',
                'bundle': 'Use "Discount percentage" for bundle savings',
                'clearance': 'Use "Discount percentage" for clearance discount',
                'flash_sale': 'Use "Discount percentage" and set an end date',
                'seasonal': 'Use "Discount percentage" for seasonal discount',
                'gift_with_purchase': 'Fill in "Free item description" for the gift',
                'free_shipping': 'No additional fields needed'
            };
            
            function updateHelper() {
                const selectedType = saleTypeSelect.value;
                const helpText = saleTypeHelp[selectedType] || 'Select a sale type to see relevant fields';
                helperContainer.innerHTML = `<strong>💡 Tip:</strong> ${helpText}`;
            }
            
            saleTypeSelect.addEventListener('change', updateHelper);
            updateHelper(); // Initial call
        }
    }
    
    // Highlight relevant fields based on sale type
    function setupFieldHighlighting() {
        const saleTypeSelect = document.querySelector('#id_sale_type');
        if (saleTypeSelect) {
            const fieldMappings = {
                'percentage': ['#id_discount_percentage'],
                'fixed_amount': ['#id_discount_amount'],
                'free_item': ['#id_free_item_description'],
                'buy_x_get_y': ['#id_buy_quantity', '#id_get_quantity'],
                'bundle': ['#id_discount_percentage'],
                'clearance': ['#id_discount_percentage'],
                'flash_sale': ['#id_discount_percentage', '#id_end_date_0', '#id_end_date_1'],
                'seasonal': ['#id_discount_percentage'],
                'gift_with_purchase': ['#id_free_item_description'],
                'free_shipping': []
            };
            
            function highlightFields() {
                // Remove all highlights first
                Object.values(fieldMappings).flat().forEach(selector => {
                    const field = document.querySelector(selector);
                    if (field) {
                        field.style.border = '';
                        field.style.backgroundColor = '';
                    }
                });
                
                // Add highlights for current selection
                const selectedType = saleTypeSelect.value;
                const relevantFields = fieldMappings[selectedType] || [];
                
                relevantFields.forEach(selector => {
                    const field = document.querySelector(selector);
                    if (field) {
                        field.style.border = '2px solid #007bff';
                        field.style.backgroundColor = '#f8f9ff';
                    }
                });
            }
            
            saleTypeSelect.addEventListener('change', highlightFields);
            highlightFields(); // Initial call
        }
    }
    
    // Add row classes for styling
    function addRowClasses() {
        const rows = document.querySelectorAll('.change-list tbody tr');
        rows.forEach(row => {
            row.classList.add('sale-category-row');
            
            // Check if featured
            const featuredCell = row.querySelector('.field-is_featured');
            if (featuredCell && featuredCell.textContent.includes('True')) {
                row.classList.add('featured');
            }
            
            // Check if inactive
            const activeCell = row.querySelector('.field-is_active');
            if (activeCell && featuredCell.textContent.includes('False')) {
                row.classList.add('inactive');
            }
        });
    }
    
    // Initialize all functionality
    updateBadgePreview();
    setupSaleTypeHelper();
    setupFieldHighlighting();
    addRowClasses();
    
    // Add some visual enhancements
    const pageTitle = document.querySelector('.change-list h1');
    if (pageTitle && pageTitle.textContent.includes('Sale categories')) {
        pageTitle.innerHTML = '🏷️ ' + pageTitle.innerHTML;
    }
    
    const addButton = document.querySelector('.addlink');
    if (addButton && addButton.href && addButton.href.includes('salecategory')) {
        addButton.innerHTML = '🏷️ ' + addButton.innerHTML;
    }
});

// Add some utility functions for admin actions
window.SaleCategoryAdmin = {
    confirmBulkAction: function(actionName, count) {
        return confirm(`Are you sure you want to ${actionName} ${count} sale categories?`);
    },
    
    showSuccessMessage: function(message) {
        // Create a temporary success message
        const messageDiv = document.createElement('div');
        messageDiv.className = 'alert alert-success';
        messageDiv.style.position = 'fixed';
        messageDiv.style.top = '20px';
        messageDiv.style.right = '20px';
        messageDiv.style.zIndex = '9999';
        messageDiv.style.padding = '10px 20px';
        messageDiv.style.backgroundColor = '#d4edda';
        messageDiv.style.border = '1px solid #c3e6cb';
        messageDiv.style.borderRadius = '4px';
        messageDiv.style.color = '#155724';
        messageDiv.textContent = message;
        
        document.body.appendChild(messageDiv);
        
        setTimeout(() => {
            messageDiv.remove();
        }, 3000);
    }
};
