{% extends 'admin/base_admin.html' %}

{% block title %}Delete Promotion{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="mt-4">Delete Promotion</h1>
    
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'admin_dashboard' %}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{% url 'admin_promotions' %}">Promotions</a></li>
            <li class="breadcrumb-item active">Delete</li>
        </ol>
    </nav>
    
    {% if messages %}
    <div class="row">
        <div class="col-12">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <div class="card mb-4">
        <div class="card-header bg-danger text-white">
            <i class="fas fa-exclamation-triangle me-1"></i>
            Confirm Deletion
        </div>
        <div class="card-body">
            <div class="alert alert-warning">
                <h5>Are you sure you want to delete this promotion?</h5>
                <p>This action cannot be undone.</p>
            </div>
            
            <div class="mb-4">
                <h6>Promotion Details:</h6>
                <table class="table table-bordered">
                    <tr>
                        <th style="width: 150px;">Product:</th>
                        <td>{{ promotion.product.name }}</td>
                    </tr>
                    <tr>
                        <th>Original Price:</th>
                        <td>₨. {{ promotion.product.price|floatformat:2 }}</td>
                    </tr>
                    <tr>
                        <th>Sale Price:</th>
                        <td>₨. {{ promotion.sale_price|floatformat:2 }}</td>
                    </tr>
                    <tr>
                        <th>Discount:</th>
                        <td>
                            {% if promotion.discount_percentage %}
                            {{ promotion.discount_percentage|floatformat:0 }}%
                            {% else %}
                            -
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th>Promotion Type:</th>
                        <td>
                            {% if promotion.is_featured %}<span class="badge bg-primary">Featured</span>{% endif %}
                            {% if promotion.is_best_seller %}<span class="badge bg-success">Best Seller</span>{% endif %}
                            {% if promotion.is_new_arrival %}<span class="badge bg-info">New Arrival</span>{% endif %}
                            {% if promotion.is_top_deal %}<span class="badge bg-danger">Top Deal</span>{% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th>Period:</th>
                        <td>
                            {{ promotion.start_date|date:"M d, Y" }}
                            {% if promotion.end_date %}
                            - {{ promotion.end_date|date:"M d, Y" }}
                            {% else %}
                            - Ongoing
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
            
            <form method="post" action="{% url 'admin_promotion_delete' promotion.id %}">
                {% csrf_token %}
                <div class="d-flex">
                    <button type="submit" class="btn btn-danger me-2">
                        <i class="fas fa-trash"></i> Delete Promotion
                    </button>
                    <a href="{% url 'admin_promotions' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
