{% extends 'admin/base_admin.html' %}

{% block title %}{% if product %}Edit{% else %}Add{% endif %} Product{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="mt-4">{% if product %}Edit{% else %}Add{% endif %} Product</h1>

    {% if category_id or referring_category %}
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'admin_dashboard' %}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{% url 'admin_categories' %}">Categories</a></li>
            {% if category_id %}
            <li class="breadcrumb-item"><a href="{% url 'admin_products_by_category' category_id %}">{{ form.category.initial.name }}</a></li>
            {% elif referring_category %}
            <li class="breadcrumb-item"><a href="{% url 'admin_products_by_category' referring_category %}">Back to Category</a></li>
            {% endif %}
            <li class="breadcrumb-item active">{% if product %}Edit{% else %}Add{% endif %} Product</li>
        </ol>
    </nav>
    {% endif %}

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-edit me-1"></i>
            Product Details
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
                {% csrf_token %}

                <!-- Hidden fields to maintain category context -->
                {% if category_id %}
                <input type="hidden" name="category_id" value="{{ category_id }}">
                {% endif %}

                {% if referring_category %}
                <input type="hidden" name="referring_category" value="{{ referring_category }}">
                {% endif %}

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.name.id_for_label }}" class="form-label">Product Name*</label>
                            {{ form.name }}
                            {% if form.name.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.name.errors }}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.category.id_for_label }}" class="form-label">Category</label>
                            {{ form.category }}
                            {% if form.category.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.category.errors }}
                            </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                Products must belong to a category to appear on the frontend
                            </small>
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.brand.id_for_label }}" class="form-label">Brand</label>
                            {{ form.brand }}
                            {% if form.brand.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.brand.errors }}
                            </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                Select a brand for this product
                            </small>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.price.id_for_label }}" class="form-label">Price</label>
                            {{ form.price }}
                            {% if form.price.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.price.errors }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.stock_status.id_for_label }}" class="form-label">Stock Status</label>
                            {{ form.stock_status }}
                            {% if form.stock_status.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.stock_status.errors }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="form-group mb-3">
                    <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                    {{ form.description }}
                    {% if form.description.errors %}
                    <div class="invalid-feedback d-block">
                        {{ form.description.errors }}
                    </div>
                    {% endif %}
                </div>

                <div class="form-group mb-3">
                    <label for="{{ form.image.id_for_label }}" class="form-label">Product Image</label>
                    {{ form.image }}
                    {% if form.image.errors %}
                    <div class="invalid-feedback d-block">
                        {{ form.image.errors }}
                    </div>
                    {% endif %}
                    {% if product and product.image %}
                    <div class="mt-2">
                        <p>Current image:</p>
                        <img src="{{ product.image.url }}" alt="{{ product.name }}" style="max-height: 200px;">
                    </div>
                    {% endif %}
                </div>

                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">
                        {% if product %}Update{% else %}Create{% endif %} Product
                    </button>

                    {% if product %}
                    <a href="{% url 'admin_product_promotions' product.id %}" class="btn btn-success">
                        <i class="fas fa-tags"></i> Manage Promotions
                    </a>
                    {% endif %}

                    {% if referring_category %}
                    <a href="{% url 'admin_products_by_category' referring_category %}" class="btn btn-secondary">Cancel</a>
                    {% elif category_id %}
                    <a href="{% url 'admin_products_by_category' category_id %}" class="btn btn-secondary">Cancel</a>
                    {% else %}
                    <a href="{% url 'admin_products' %}" class="btn btn-secondary">Cancel</a>
                    {% endif %}
                </div>
            </form>
        </div>
    </div>

    {% if product %}
    <!-- Quick Category Change -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-exchange-alt me-1"></i>
            Move to Another Category
        </div>
        <div class="card-body">
            <div class="row">
                {% for category in categories %}
                {% if product.category != category %}
                <div class="col-md-3 col-sm-6 mb-3">
                    <form method="post" action="{% url 'admin_product_edit' product.id %}" class="d-inline">
                        {% csrf_token %}
                        <input type="hidden" name="name" value="{{ product.name }}">
                        <input type="hidden" name="price" value="{{ product.price|default:'' }}">
                        <input type="hidden" name="description" value="{{ product.description|default:'' }}">
                        <input type="hidden" name="stock_status" value="{{ product.stock_status }}">
                        <input type="hidden" name="category" value="{{ category.id }}">
                        <input type="hidden" name="brand" value="{{ product.brand.id|default:'' }}">
                        {% if referring_category %}
                        <input type="hidden" name="referring_category" value="{{ referring_category }}">
                        {% endif %}
                        <button type="submit" class="btn btn-outline-primary w-100 text-start">
                            <div class="d-flex align-items-center">
                                {% if category.image %}
                                <img src="{{ category.image.url }}" alt="{{ category.name }}" class="rounded-circle me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                {% else %}
                                <div class="bg-light rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px;">
                                    <i class="fas fa-tag text-muted"></i>
                                </div>
                                {% endif %}
                                <span>{{ category.name }}</span>
                            </div>
                        </button>
                    </form>
                </div>
                {% endif %}
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add custom file input behavior
    const fileInput = document.querySelector('input[type="file"]');
    if (fileInput) {
        fileInput.addEventListener('change', function(e) {
            const fileName = e.target.files[0]?.name;
            const label = this.nextElementSibling;
            if (label) {
                label.textContent = fileName || 'Choose file';
            }
        });
    }

    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });
});
</script>
{% endblock %}
