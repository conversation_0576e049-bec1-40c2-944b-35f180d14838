/* Product Carousel Styles */
.product-carousel-section {
    padding: 60px 0;
    position: relative;
    background: #f8fafc;
}

.section-heading {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 40px;
    position: relative;
}

.section-title-wrapper {
    display: flex;
    flex-direction: column;
}

.section-title {
    font-size: 32px;
    font-weight: 800;
    color: #1a202c;
    margin: 0;
    position: relative;
    padding-bottom: 15px;
    letter-spacing: -0.5px;
}

.section-title:after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 80px;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
}

.section-subtitle {
    font-size: 16px;
    color: #718096;
    margin-top: 12px;
    margin-bottom: 0;
    max-width: 600px;
    font-weight: 400;
    line-height: 1.6;
}

.carousel-controls {
    display: flex;
    gap: 12px;
}

.carousel-control {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    color: white;
    font-size: 20px;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.carousel-control:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

.carousel-control:active {
    transform: translateY(0);
}

.carousel-control:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.product-carousel {
    position: relative;
    overflow: hidden;
}

.product-carousel-inner {
    display: flex;
    transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    gap: 24px;
    padding: 0 4px;
}

.product-slide {
    flex: 0 0 calc(25% - 24px);
    min-width: 0;
}

.product-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    border: 1px solid #e2e8f0;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
    border-color: #cbd5e0;
}

.product-image-container {
    position: relative;
    padding: 20px;
    background: #f8fafc;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    overflow: hidden;
}

.product-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image {
    transform: scale(1.05);
}

.product-info {
    padding: 20px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.product-title {
    font-size: 16px;
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 8px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-price {
    font-size: 20px;
    font-weight: 700;
    color: #e53e3e;
    margin-bottom: 8px;
}

.product-original-price {
    font-size: 16px;
    color: #a0aec0;
    text-decoration: line-through;
    margin-left: 8px;
}

.product-savings {
    font-size: 14px;
    color: #38a169;
    font-weight: 600;
    margin-top: 4px;
}

.product-rating {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    gap: 8px;
}

.rating-stars {
    display: flex;
    color: #f6ad55;
    font-size: 14px;
}

.rating-count {
    font-size: 14px;
    color: #718096;
    font-weight: 500;
}

.availability-badge {
    position: absolute;
    top: 12px;
    left: 12px;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.availability-badge.available {
    background: #c6f6d5;
    color: #22543d;
}

.availability-badge.pre-order {
    background: #fed7d7;
    color: #742a2a;
}

.availability-badge.unavailable {
    background: #e2e8f0;
    color: #4a5568;
}

@media (max-width: 1200px) {
    .product-slide {
        flex: 0 0 calc(33.333% - 24px);
    }

    .section-title {
        font-size: 28px;
    }
}

@media (max-width: 992px) {
    .product-slide {
        flex: 0 0 calc(50% - 24px);
    }

    .product-carousel-section {
        padding: 40px 0;
    }

    .section-title {
        font-size: 24px;
    }
}

@media (max-width: 768px) {
    .section-heading {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .carousel-controls {
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .product-slide {
        flex: 0 0 calc(100% - 24px);
    }

    .product-carousel-inner {
        gap: 16px;
    }

    .section-title {
        font-size: 20px;
    }

    .product-info {
        padding: 16px;
    }

    .product-image-container {
        height: 160px;
        padding: 16px;
    }
}

.carousel-dots {
    display: flex;
    justify-content: center;
    margin-top: 32px;
    gap: 12px;
}

.carousel-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #cbd5e0;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
}

.carousel-dot:hover {
    background-color: #a0aec0;
    transform: scale(1.1);
}

.carousel-dot.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: scale(1.3);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
}

/* Sale badge and discount styles */
.sale-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    color: white;
    padding: 6px 12px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 700;
    z-index: 2;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(245, 101, 101, 0.3);
}

.discount-price {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.original-price {
    text-decoration: line-through;
    color: #a0aec0;
    font-size: 14px;
    font-weight: 500;
}

.save-amount {
    color: #38a169;
    font-size: 14px;
    font-weight: 700;
    background: #c6f6d5;
    padding: 2px 6px;
    border-radius: 4px;
}

/* Top Deal badge */
.top-deal {
    display: inline-block;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    padding: 4px 10px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 700;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

/* Additional modern enhancements */
.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    border-radius: 16px;
}

.product-card:hover::before {
    opacity: 1;
}

.product-brand {
    font-size: 12px;
    color: #718096;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.product-sku {
    font-size: 11px;
    color: #a0aec0;
    margin-top: auto;
    padding-top: 8px;
}

/* Loading skeleton styles */
.product-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Product Buttons */
.product-buttons {
    display: flex;
    gap: 8px;
    margin-top: auto;
    padding-top: 16px;
}

.add-to-cart {
    flex: 1;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 16px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.add-to-cart:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

.add-to-cart:active {
    transform: translateY(0);
}

.add-to-cart.disabled {
    background: #e2e8f0;
    color: #a0aec0;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.add-to-cart.preorder-btn {
    background: linear-gradient(135deg, #f6ad55 0%, #ed8936 100%);
}

.add-to-cart.preorder-btn:hover {
    box-shadow: 0 8px 20px rgba(246, 173, 85, 0.4);
}

.view-cart-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    background: #f7fafc;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    color: #4a5568;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 16px;
}

.view-cart-btn:hover {
    background: #edf2f7;
    border-color: #cbd5e0;
    color: #2d3748;
    transform: translateY(-2px);
}

/* Brand link styling */
.brand-link {
    color: inherit;
    text-decoration: none;
    transition: color 0.3s ease;
}

.brand-link:hover {
    color: #667eea;
}

.product-name-link {
    color: inherit;
    text-decoration: none;
    transition: color 0.3s ease;
}

.product-name-link:hover {
    color: #667eea;
}
