{% extends 'admin/base_admin.html' %}
{% load static %}

{% block title %}Manage Products - {{ sale.name }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'admin/css/sales_management_admin.css' %}">
<style>
.products-management-page {
    padding: 20px;
}

.sale-info-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 12px;
    margin-bottom: 25px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.sale-badge-display {
    display: inline-block;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: bold;
    color: white;
    margin-right: 15px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.products-section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.section-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
    display: flex;
    justify-content: between;
    align-items: center;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.product-card {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 15px;
    transition: all 0.2s ease;
    background: #fafbfc;
}

.product-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.product-card.assigned {
    border-color: #28a745;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

.product-card.inactive {
    opacity: 0.6;
    border-color: #dc3545;
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
}

.product-info h4 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 16px;
}

.product-info p {
    margin: 0 0 5px 0;
    color: #6c757d;
    font-size: 14px;
}

.product-price {
    font-weight: bold;
    color: #28a745;
    font-size: 16px;
}

.product-actions {
    margin-top: 15px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.product-checkbox {
    margin-right: 10px;
    transform: scale(1.2);
}

.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
    margin-left: 10px;
}

.status-badge.active {
    background: #d4edda;
    color: #155724;
}

.status-badge.inactive {
    background: #f8d7da;
    color: #721c24;
}

.bulk-actions {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 25px;
    border: 2px solid #e9ecef;
}

.bulk-actions h4 {
    margin-bottom: 15px;
    color: #333;
}

.action-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.action-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.action-btn:hover {
    transform: translateY(-1px);
    text-decoration: none;
}

.action-btn.assign {
    background: #28a745;
    color: white;
}

.action-btn.assign:hover {
    background: #218838;
    color: white;
}

.action-btn.remove {
    background: #dc3545;
    color: white;
}

.action-btn.remove:hover {
    background: #c82333;
    color: white;
}

.action-btn.toggle {
    background: #ffc107;
    color: #212529;
}

.action-btn.toggle:hover {
    background: #e0a800;
    color: #212529;
}

.action-btn.back {
    background: #6c757d;
    color: white;
}

.action-btn.back:hover {
    background: #5a6268;
    color: white;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.search-filter {
    margin-bottom: 20px;
}

.search-filter input {
    width: 100%;
    padding: 12px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    font-size: 14px;
}

.search-filter input:focus {
    border-color: #667eea;
    outline: none;
}

.stats-summary {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.stat-item {
    background: #f8f9fa;
    padding: 15px 20px;
    border-radius: 8px;
    border: 2px solid #e9ecef;
    text-align: center;
    flex: 1;
    min-width: 150px;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    display: block;
}

.stat-label {
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.select-all-container {
    margin-bottom: 15px;
    padding: 10px;
    background: #e9ecef;
    border-radius: 6px;
}

.select-all-container label {
    font-weight: 600;
    margin: 0;
    cursor: pointer;
}
</style>
{% endblock %}

{% block content %}
<div class="products-management-page">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>🔗 Manage Products</h1>
        <a href="{% url 'admin_sales_categories' %}" class="action-btn back">
            <i class="fas fa-arrow-left"></i>
            Back to Sales
        </a>
    </div>

    <!-- Sale Information Card -->
    <div class="sale-info-card">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h3>
                    <span class="sale-badge-display" style="background-color: {{ sale.badge_color }};">
                        {{ sale.badge_text }}
                    </span>
                    {{ sale.name }}
                </h3>
                <p class="mb-0">{{ sale.get_sale_type_display }} • Priority: {{ sale.priority }}/5</p>
                {% if sale.description %}
                <p class="mb-0 mt-2">{{ sale.description }}</p>
                {% endif %}
            </div>
            <div class="text-right">
                {% if sale.is_active %}
                <span class="badge badge-success">Active</span>
                {% else %}
                <span class="badge badge-secondary">Inactive</span>
                {% endif %}
                {% if sale.is_featured %}
                <span class="badge badge-warning">Featured</span>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Statistics Summary -->
    <div class="stats-summary">
        <div class="stat-item">
            <span class="stat-number">{{ assignments.count }}</span>
            <span class="stat-label">Total Assignments</span>
        </div>
        <div class="stat-item">
            <span class="stat-number">{{ assignments|length|add:0 }}</span>
            <span class="stat-label">Active Assignments</span>
        </div>
        <div class="stat-item">
            <span class="stat-number">{{ available_products.count }}</span>
            <span class="stat-label">Available Products</span>
        </div>
        <div class="stat-item">
            <span class="stat-number">{{ sale.current_uses }}</span>
            <span class="stat-label">Times Used</span>
        </div>
    </div>

    <!-- Assigned Products Section -->
    <div class="products-section">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h3 class="section-title">📦 Assigned Products ({{ assignments.count }})</h3>
        </div>

        {% if assignments %}
        <form method="post" id="assignedProductsForm">
            {% csrf_token %}
            <input type="hidden" name="action" value="">
            
            <div class="bulk-actions">
                <h4>Bulk Actions for Assigned Products:</h4>
                <div class="select-all-container">
                    <label>
                        <input type="checkbox" id="selectAllAssigned" class="product-checkbox">
                        Select All Assigned Products
                    </label>
                </div>
                <div class="action-buttons">
                    <button type="button" class="action-btn remove" onclick="submitAction('remove')">
                        <i class="fas fa-trash"></i>
                        Remove Selected
                    </button>
                    <button type="button" class="action-btn toggle" onclick="submitAction('toggle')">
                        <i class="fas fa-toggle-on"></i>
                        Toggle Active/Inactive
                    </button>
                </div>
            </div>

            <div class="products-grid">
                {% for assignment in assignments %}
                <div class="product-card {% if assignment.is_active %}assigned{% else %}inactive{% endif %}">
                    <div class="d-flex align-items-start">
                        <input type="checkbox" name="assignment_ids" value="{{ assignment.id }}" 
                               class="product-checkbox assigned-checkbox">
                        <div class="product-info flex-grow-1">
                            <h4>{{ assignment.product.name }}</h4>
                            <p><strong>Category:</strong> {{ assignment.product.category.name }}</p>
                            <p><strong>Brand:</strong> {{ assignment.product.brand.name|default:"No Brand" }}</p>
                            <div class="product-price">₨. {{ assignment.product.price }}</div>
                            <span class="status-badge {% if assignment.is_active %}active{% else %}inactive{% endif %}">
                                {% if assignment.is_active %}Active{% else %}Inactive{% endif %}
                            </span>
                            {% if assignment.custom_discount_percentage %}
                            <span class="badge badge-info">Custom: {{ assignment.custom_discount_percentage }}%</span>
                            {% elif assignment.custom_discount_amount %}
                            <span class="badge badge-info">Custom: ₨{{ assignment.custom_discount_amount }}</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="product-actions">
                        <small class="text-muted">
                            Assigned: {{ assignment.assigned_date|date:"M d, Y" }}
                            {% if assignment.assigned_by %}by {{ assignment.assigned_by.username }}{% endif %}
                        </small>
                    </div>
                </div>
                {% endfor %}
            </div>
        </form>
        {% else %}
        <div class="empty-state">
            <i class="fas fa-box-open"></i>
            <h4>No Products Assigned</h4>
            <p>This sale doesn't have any products assigned yet.</p>
        </div>
        {% endif %}
    </div>

    <!-- Available Products Section -->
    <div class="products-section">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h3 class="section-title">📋 Available Products ({{ available_products.count }})</h3>
        </div>

        {% if available_products %}
        <form method="post" id="availableProductsForm">
            {% csrf_token %}
            <input type="hidden" name="action" value="assign">
            
            <div class="bulk-actions">
                <h4>Assign Products to Sale:</h4>
                <div class="select-all-container">
                    <label>
                        <input type="checkbox" id="selectAllAvailable" class="product-checkbox">
                        Select All Available Products
                    </label>
                </div>
                <div class="action-buttons">
                    <button type="submit" class="action-btn assign">
                        <i class="fas fa-plus"></i>
                        Assign Selected Products
                    </button>
                </div>
            </div>

            <div class="search-filter">
                <input type="text" id="productSearch" placeholder="🔍 Search products by name, category, or brand...">
            </div>

            <div class="products-grid" id="availableProductsGrid">
                {% for product in available_products %}
                <div class="product-card" data-name="{{ product.name|lower }}" 
                     data-category="{{ product.category.name|lower }}" 
                     data-brand="{{ product.brand.name|lower|default:'' }}">
                    <div class="d-flex align-items-start">
                        <input type="checkbox" name="product_ids" value="{{ product.id }}" 
                               class="product-checkbox available-checkbox">
                        <div class="product-info flex-grow-1">
                            <h4>{{ product.name }}</h4>
                            <p><strong>Category:</strong> {{ product.category.name }}</p>
                            <p><strong>Brand:</strong> {{ product.brand.name|default:"No Brand" }}</p>
                            <div class="product-price">₨. {{ product.price }}</div>
                            {% if product.is_on_sale %}
                            <span class="badge badge-warning">On Sale</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </form>
        {% else %}
        <div class="empty-state">
            <i class="fas fa-check-circle"></i>
            <h4>All Products Assigned</h4>
            <p>All available products are already assigned to this sale.</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select all functionality
    const selectAllAssigned = document.getElementById('selectAllAssigned');
    const selectAllAvailable = document.getElementById('selectAllAvailable');
    const assignedCheckboxes = document.querySelectorAll('.assigned-checkbox');
    const availableCheckboxes = document.querySelectorAll('.available-checkbox');
    
    if (selectAllAssigned) {
        selectAllAssigned.addEventListener('change', function() {
            assignedCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    }
    
    if (selectAllAvailable) {
        selectAllAvailable.addEventListener('change', function() {
            availableCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    }
    
    // Search functionality
    const searchInput = document.getElementById('productSearch');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const productCards = document.querySelectorAll('#availableProductsGrid .product-card');
            
            productCards.forEach(card => {
                const name = card.dataset.name || '';
                const category = card.dataset.category || '';
                const brand = card.dataset.brand || '';
                
                const matches = name.includes(searchTerm) || 
                              category.includes(searchTerm) || 
                              brand.includes(searchTerm);
                
                card.style.display = matches ? 'block' : 'none';
            });
        });
    }
    
    // Form submission confirmations
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const action = this.querySelector('input[name="action"]').value;
            const checkedBoxes = this.querySelectorAll('input[type="checkbox"]:checked:not([id^="selectAll"])');
            
            if (checkedBoxes.length === 0) {
                e.preventDefault();
                alert('Please select at least one product.');
                return;
            }
            
            let message = '';
            switch(action) {
                case 'assign':
                    message = `Assign ${checkedBoxes.length} product(s) to this sale?`;
                    break;
                case 'remove':
                    message = `Remove ${checkedBoxes.length} product(s) from this sale?`;
                    break;
                case 'toggle':
                    message = `Toggle active/inactive status for ${checkedBoxes.length} product(s)?`;
                    break;
            }
            
            if (message && !confirm(message)) {
                e.preventDefault();
            }
        });
    });
});

function submitAction(action) {
    const form = document.getElementById('assignedProductsForm');
    const actionInput = form.querySelector('input[name="action"]');
    actionInput.value = action;
    form.submit();
}
</script>
{% endblock %}
