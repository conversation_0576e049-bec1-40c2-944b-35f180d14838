from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import authenticate, login as auth_login, logout as auth_logout
from django.contrib.auth.forms import AuthenticationForm
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.contrib import messages
from django.http import Http404
from django.db.utils import OperationalError, ProgrammingError, DatabaseError
from django.db import connection, models
from django.utils import timezone
from .forms import RegistrationForm
from .models import Category, Product, Brand

# Define a dummy Category class for fallback
class DummyCategoryManager:
    def all(self):
        return []

    def filter(self, **kwargs):
        return []

class DummyCategoryClass:
    objects = DummyCategoryManager()

    @classmethod
    def DoesNotExist(cls):
        pass

# Check if the category table exists
def category_table_exists():
    try:
        with connection.cursor() as cursor:
            cursor.execute(
                "SELECT name FROM sqlite_master WHERE type='table' AND name='authtryapp_category';"
            )
            return bool(cursor.fetchone())
    except:
        return False

# Check if the product table has a slug column
def product_has_slug_column():
    try:
        with connection.cursor() as cursor:
            cursor.execute("PRAGMA table_info(authtryapp_product);")
            columns = [column[1] for column in cursor.fetchall()]
            return "slug" in columns
    except:
        return False

# Try to import Category model, but handle the case where the table doesn't exist
if not category_table_exists():
    Category = DummyCategoryClass

def home(request):
    db_issues = []
    if not product_has_slug_column():
        db_issues.append("Product table is missing the slug column. Please run fix_product_table.py to add it.")
    if not category_table_exists():
        db_issues.append("Category table doesn't exist. Please run migrations to create it.")

    # Get hero banners
    hero_banners = []
    site_config = None
    try:
        from .models import HeroBanner, SiteConfiguration
        hero_banners = HeroBanner.objects.filter(is_active=True).order_by('order')
        # Get site configuration
        site_config = SiteConfiguration.objects.first()
    except (OperationalError, ProgrammingError, DatabaseError, AttributeError, ImportError) as e:
        db_issues.append(f"Error accessing hero banners: {str(e)}")

    # Get section headings
    section_headings = {}
    try:
        from .models import SectionHeading
        headings = SectionHeading.objects.filter(is_active=True)
        for heading in headings:
            section_headings[heading.section_type] = {
                'title': heading.title,
                'subtitle': heading.subtitle
            }
    except (OperationalError, ProgrammingError, DatabaseError, AttributeError, ImportError) as e:
        db_issues.append(f"Error accessing section headings: {str(e)}")

    # Get featured products (products with active promotions marked as featured)
    featured_products = []
    best_selling_products = []
    new_arrivals = []
    top_deals = []
    sales_products = []

    try:
        from .models import ProductPromotion
        # Get active promotions
        now = timezone.now()
        active_promotions = ProductPromotion.objects.filter(
            start_date__lte=now
        ).filter(
            models.Q(end_date__isnull=True) | models.Q(end_date__gte=now)
        )

        # Get featured products
        featured_promos = active_promotions.filter(is_featured=True)
        featured_products = [promo.product for promo in featured_promos][:8]

        # Get best selling products
        best_selling_promos = active_promotions.filter(is_best_seller=True)
        best_selling_products = [promo.product for promo in best_selling_promos][:8]

        # Get new arrivals
        new_arrival_promos = active_promotions.filter(is_new_arrival=True)
        new_arrivals = [promo.product for promo in new_arrival_promos][:8]

        # Get top deals
        top_deal_promos = active_promotions.filter(is_top_deal=True)
        top_deals = [promo.product for promo in top_deal_promos][:8]

        # Get special offers (dynamic from promotions)
        special_offer_promos = active_promotions.filter(is_special_offer=True)
        special_offers = [promo.product for promo in special_offer_promos][:12]

        # Get sales products (products with discounts/on sale)
        try:
            # Get products that are on sale (have original_price > price)
            sales_products = Product.objects.filter(
                original_price__isnull=False,
                price__isnull=False,
                original_price__gt=models.F('price')
            ).order_by('-created_at')[:12]

            # If no products with original_price, get products from active promotions
            if not sales_products:
                sales_products = [promo.product for promo in active_promotions][:12]

            # Combine special offers with sales products for the special offers section
            if special_offers:
                sales_products = special_offers

        except Exception as e:
            sales_products = special_offers if special_offers else []

        # If no featured products from promotions, get the latest products
        if not featured_products and hasattr(Product, 'objects'):
            featured_products = Product.objects.all().order_by('-created_at')[:8]

        # If no best selling products from promotions, get random products
        if not best_selling_products and hasattr(Product, 'objects'):
            best_selling_products = Product.objects.all().order_by('?')[:8]

    except (OperationalError, ProgrammingError, DatabaseError, AttributeError, ImportError) as e:
        if hasattr(Product, 'objects') and hasattr(Product.objects, 'all'):
            try:
                featured_products = Product.objects.all().order_by('-created_at')[:8]
            except Exception:
                featured_products = []
        db_issues.append(f"Error accessing product promotions: {str(e)}")

    # Get products by category
    categories = []
    category_products = {}  # Dictionary to store products by category

    if hasattr(Category, 'objects') and hasattr(Category.objects, 'all'):
        try:
            categories = Category.objects.all()

            # Get products for each category
            for category in categories:
                try:
                    # Get up to 4 products for each category
                    products = Product.objects.filter(category=category).order_by('-created_at')[:4]
                    if products.exists():
                        category_products[category.id] = products
                except Exception as e:
                    print(f"Error retrieving products for category {category.name}: {str(e)}")
                    category_products[category.id] = []

        except (OperationalError, ProgrammingError, DatabaseError, AttributeError) as e:
            categories = []
            db_issues.append(f"Error accessing categories: {str(e)}")

    # Get promotions data for display
    promotions_data = {}
    try:
        active_promos = ProductPromotion.objects.filter(
            start_date__lte=now
        ).filter(
            models.Q(end_date__isnull=True) | models.Q(end_date__gte=now)
        )

        for promo in active_promos:
            promotions_data[promo.product.id] = {
                'sale_price': promo.sale_price,
                'discount_percentage': promo.discount_percentage,
                'is_top_deal': promo.is_top_deal
            }
    except Exception:
        pass

    # Get cart
    cart = None
    try:
        from .cart_views import get_or_create_cart
        try:
            cart = get_or_create_cart(request)
        except (OperationalError, ProgrammingError, DatabaseError, AttributeError) as e:
            cart = None
            db_issues.append(f"Error accessing cart: {str(e)}")
    except ImportError:
        cart = None

    context = {
        'hero_banners': hero_banners,
        'site_config': site_config,
        'featured_products': featured_products,
        'best_selling_products': best_selling_products,
        'new_arrivals': new_arrivals,
        'top_deals': top_deals,
        'sales_products': sales_products,
        'categories': categories,
        'category_products': category_products,
        'section_headings': section_headings,
        'promotions_data': promotions_data,
        'cart': cart,
    }

    if db_issues:
        for issue in db_issues:
            messages.warning(request, issue)

    return render(request, "home_new.html", context)


def category_view(request, category_slug):
    """View for category page with filtering options"""
    # Get the category or return 404 if not found
    category = get_object_or_404(Category, slug=category_slug)

    # Get products for this category
    try:
        products = Product.objects.filter(category=category)

        # Debug: Print the number of products found
        print(f"Found {products.count()} products in category {category.name}")

        # Debug: Print each product
        for product in products:
            print(f"- {product.name} (ID: {product.id}, Slug: {product.slug})")
    except Exception as e:
        print(f"Error retrieving products: {str(e)}")
        products = Product.objects.none()

    # Filter by stock status if requested
    stock_status = request.GET.get('stock')
    if stock_status:
        products = products.filter(stock_status=stock_status)

    # Sort products if requested
    sort_by = request.GET.get('sort')
    if sort_by:
        if sort_by == 'price_asc':
            products = products.order_by('price')
        elif sort_by == 'price_desc':
            products = products.order_by('-price')
        elif sort_by == 'discount_desc':
            # Sort by highest discount percentage
            products = products.extra(
                select={'discount_pct': '((original_price - price) / original_price) * 100'}
            ).order_by('-discount_pct')
        elif sort_by == 'name_asc':
            products = products.order_by('name')
        elif sort_by == 'name_desc':
            products = products.order_by('-name')
        elif sort_by == 'newest':
            products = products.order_by('-created_at')
    else:
        # Default sort by newest
        products = products.order_by('-created_at')

    # Get all categories for navigation
    categories = Category.objects.all()

    # Get cart for the current user
    try:
        from .cart_views import get_or_create_cart
        cart = get_or_create_cart(request)
    except (OperationalError, ProgrammingError, ImportError):
        cart = None

    context = {
        'category': category,
        'products': products,
        'categories': categories,  # For navigation
        'cart': cart,
        'current_sort': sort_by,
        'current_stock': stock_status
    }

    return render(request, 'category.html', context)


def product_detail(request, product_id):
    """View for detailed product page"""
    product = get_object_or_404(Product, id=product_id)

    # Get related products (products in the same category)
    related_products = []
    if product.category:
        related_products = Product.objects.filter(category=product.category).exclude(id=product.id)[:4]

    # Get all categories for navigation
    categories = Category.objects.all()

    # Get cart for the current user
    try:
        from .cart_views import get_or_create_cart
        cart = get_or_create_cart(request)
    except (OperationalError, ProgrammingError, ImportError):
        cart = None

    context = {
        'product': product,
        'related_products': related_products,
        'categories': categories,
        'cart': cart,
    }

    return render(request, 'product_detail.html', context)


def login_view(request):
    session_id = request.session.get('cart_id')
    if request.method == 'POST':
        form = AuthenticationForm(request, data=request.POST)
        if form.is_valid():
            user = form.get_user()
            auth_login(request, user)

            if session_id:
                from .models import Cart, CartItem
                try:
                    anonymous_cart = Cart.objects.filter(session_id=session_id, user__isnull=True).first()
                    if anonymous_cart:
                        user_cart, created = Cart.objects.get_or_create(user=user, defaults={'session_id': None})
                        for item in anonymous_cart.items.all():
                            existing_item = user_cart.items.filter(product=item.product).first()
                            if existing_item:
                                existing_item.quantity += item.quantity
                                existing_item.save()
                            else:
                                CartItem.objects.create(
                                    cart=user_cart,
                                    product=item.product,
                                    quantity=item.quantity,
                                    is_preorder=item.is_preorder
                                )
                        anonymous_cart.delete()
                        del request.session['cart_id']
                except Exception as e:
                    pass
            messages.success(request, f"Welcome back, {user.username}!")
            return redirect('home')
        else:
            messages.error(request, "Invalid username or password")
    else:
        form = AuthenticationForm()

    try:
        from .cart_views import get_or_create_cart
        cart = get_or_create_cart(request)
    except (OperationalError, ProgrammingError, ImportError):
        cart = None

    return render(request, "login_base.html", {'form': form, 'cart': cart})


def logout_view(request):
    auth_logout(request)
    return redirect('login')


def register_view(request):
    if request.method == 'POST':
        form = RegistrationForm(request.POST)
        if form.is_valid():
            user = form.save()
            auth_login(request, user)
            messages.success(request, "Registration successful. You are now logged in.")
            return redirect('home')
        else:
            messages.error(request, "Registration failed. Please correct the errors below.")
    else:
        form = RegistrationForm()

    try:
        from .cart_views import get_or_create_cart
        cart = get_or_create_cart(request)
    except (OperationalError, ProgrammingError, ImportError):
        cart = None

    return render(request, "register_base.html", {'form': form, 'cart': cart})


@login_required
def profile_view(request):
    try:
        from .cart_views import get_or_create_cart
        cart = get_or_create_cart(request)
    except (OperationalError, ProgrammingError, ImportError):
        cart = None

    return render(request, "profile_base.html", {'user': request.user, 'cart': cart})


def brand_list(request):
    """View for listing all brands"""
    brands = Brand.objects.all().order_by('name')

    # Get cart for the current user
    try:
        from .cart_views import get_or_create_cart
        cart = get_or_create_cart(request)
    except (OperationalError, ProgrammingError, ImportError):
        cart = None

    context = {
        'brands': brands,
        'cart': cart,
    }

    return render(request, 'brand_list.html', context)

def product_detail(request, product_slug):
    """View for displaying product details"""
    try:
        # Get the product or return 404 if not found
        product = get_object_or_404(Product, slug=product_slug)

        # Get cart for the current user
        try:
            from .cart_views import get_or_create_cart
            cart = get_or_create_cart(request)
        except (OperationalError, ProgrammingError, ImportError):
            cart = None

        # Get categories for navigation
        try:
            categories = Category.objects.all()
        except (OperationalError, ProgrammingError, DatabaseError, AttributeError):
            categories = []

        context = {
            'product': product,
            'cart': cart,
            'categories': categories,
        }
        return render(request, "product_detail_base.html", context)
    except Exception as e:
        # If there's any error, show a message and redirect to home
        messages.error(request, f"An error occurred: {str(e)}")
        return redirect('home')


def brand_detail(request, brand_slug):
    """View for displaying products by brand"""
    # Get the brand or return 404 if not found
    brand = get_object_or_404(Brand, slug=brand_slug)

    # Get products for this brand
    products = Product.objects.filter(brand=brand)

    # Filter by stock status if requested
    stock_status = request.GET.get('stock')
    if stock_status:
        products = products.filter(stock_status=stock_status)

    # Sort products if requested
    sort_by = request.GET.get('sort')
    if sort_by:
        if sort_by == 'price_asc':
            products = products.order_by('price')
        elif sort_by == 'price_desc':
            products = products.order_by('-price')
        elif sort_by == 'name_asc':
            products = products.order_by('name')
        elif sort_by == 'name_desc':
            products = products.order_by('-name')
        elif sort_by == 'newest':
            products = products.order_by('-created_at')
    else:
        # Default sort by newest
        products = products.order_by('-created_at')

    # Get all categories for navigation
    categories = Category.objects.all()

    # Get cart for the current user
    try:
        from .cart_views import get_or_create_cart
        cart = get_or_create_cart(request)
    except (OperationalError, ProgrammingError, ImportError):
        cart = None

    context = {
        'brand': brand,
        'products': products,
        'categories': categories,  # For navigation
        'cart': cart,
        'current_sort': sort_by,
        'current_stock': stock_status
    }

    return render(request, 'brand_detail.html', context)


def sales_products_view(request):
    """View for dedicated sales products page"""
    try:
        # Get products that are on sale (have original_price > price)
        sales_products = Product.objects.filter(
            original_price__isnull=False,
            price__isnull=False,
            original_price__gt=models.F('price')
        ).order_by('-created_at')

        # If no products with original_price, get products from active promotions
        if not sales_products:
            from .models import ProductPromotion
            now = timezone.now()
            active_promotions = ProductPromotion.objects.filter(
                start_date__lte=now
            ).filter(
                models.Q(end_date__isnull=True) | models.Q(end_date__gte=now)
            )
            sales_products = [promo.product for promo in active_promotions]

    except Exception as e:
        sales_products = Product.objects.none()

    # Filter by stock status if requested
    stock_status = request.GET.get('stock')
    if stock_status and hasattr(sales_products, 'filter'):
        sales_products = sales_products.filter(stock_status=stock_status)

    # Sort products if requested
    sort_by = request.GET.get('sort')
    if sort_by and hasattr(sales_products, 'order_by'):
        if sort_by == 'price_asc':
            sales_products = sales_products.order_by('price')
        elif sort_by == 'price_desc':
            sales_products = sales_products.order_by('-price')
        elif sort_by == 'discount_desc':
            # Sort by highest discount percentage
            sales_products = sales_products.extra(
                select={'discount_pct': '((original_price - price) / original_price) * 100'}
            ).order_by('-discount_pct')
        elif sort_by == 'name_asc':
            sales_products = sales_products.order_by('name')
        elif sort_by == 'newest':
            sales_products = sales_products.order_by('-created_at')

    # Get all categories for navigation
    try:
        categories = Category.objects.all()
    except (OperationalError, ProgrammingError, DatabaseError, AttributeError):
        categories = []

    # Get cart for the current user
    try:
        from .cart_views import get_or_create_cart
        cart = get_or_create_cart(request)
    except (OperationalError, ProgrammingError, ImportError):
        cart = None

    context = {
        'sales_products': sales_products,
        'categories': categories,
        'cart': cart,
        'current_sort': sort_by,
        'current_stock': stock_status,
        'section_title': 'Sales Products',
        'section_subtitle': 'Amazing deals on top-quality products',
    }

    return render(request, 'sales_products.html', context)


def all_products_view(request):
    """View for all products page with the same UI as sales products"""
    try:
        # Get all products
        products = Product.objects.all()

        # Apply sorting
        sort_by = request.GET.get('sort', 'newest')
        if sort_by == 'price_asc':
            products = products.order_by('price')
        elif sort_by == 'price_desc':
            products = products.order_by('-price')
        elif sort_by == 'name_asc':
            products = products.order_by('name')
        elif sort_by == 'name_desc':
            products = products.order_by('-name')
        elif sort_by == 'discount_desc':
            # Sort by discount percentage (products with original_price first)
            products = products.filter(
                original_price__isnull=False,
                price__isnull=False,
                original_price__gt=models.F('price')
            ).order_by('-original_price')
        else:  # newest
            products = products.order_by('-created_at')

        # Apply stock status filter
        stock_status = request.GET.get('stock', '')
        if stock_status:
            products = products.filter(stock_status=stock_status)

    except Exception as e:
        products = Product.objects.none()

    # Get all categories for navigation
    try:
        categories = Category.objects.all()
    except (OperationalError, ProgrammingError, DatabaseError, AttributeError):
        categories = []

    # Get cart for the current user
    try:
        from .cart_views import get_or_create_cart
        cart = get_or_create_cart(request)
    except (OperationalError, ProgrammingError, ImportError):
        cart = None

    context = {
        'products': products,
        'categories': categories,
        'cart': cart,
        'current_sort': sort_by,
        'current_stock': stock_status,
        'section_title': 'All Products',
        'section_subtitle': 'Discover our complete collection of products',
    }

    return render(request, 'all_products.html', context)


def all_categories_view(request):
    """View for all categories page"""
    try:
        # Get all categories
        categories = Category.objects.all().order_by('name')

        # Get product count for each category
        categories_with_counts = []
        for category in categories:
            try:
                product_count = Product.objects.filter(category=category).count()
                categories_with_counts.append({
                    'category': category,
                    'product_count': product_count
                })
            except Exception as e:
                categories_with_counts.append({
                    'category': category,
                    'product_count': 0
                })

    except Exception as e:
        categories_with_counts = []

    # Get cart for the current user
    try:
        from .cart_views import get_or_create_cart
        cart = get_or_create_cart(request)
    except (OperationalError, ProgrammingError, ImportError):
        cart = None

    context = {
        'categories_with_counts': categories_with_counts,
        'cart': cart,
        'section_title': 'All Categories',
        'section_subtitle': 'Browse products by category',
    }

    return render(request, 'all_categories.html', context)
