#!/usr/bin/env python
"""
Create test hero banners to demonstrate the redirection functionality
"""

import os
import sys
import django

# Setup Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'authtry.settings')
django.setup()

from authtryapp.models import HeroBanner, Category, Product

def create_test_banners():
    """Create test hero banners with different link types"""
    print("🎯 Creating Test Hero Banners...")
    print("=" * 50)
    
    # Clear existing banners
    HeroBanner.objects.all().delete()
    print("🗑️ Cleared existing banners")
    
    # Banner 1: Category Link (Gaming)
    banner1, created = HeroBanner.objects.get_or_create(
        title="Gaming Laptops",
        defaults={
            'description': 'High-performance gaming laptops for enthusiasts',
            'brand_text': 'GAMING',
            'image_alt': 'Gaming Laptops Collection',
            'link_type': 'category',
            'link_target': 'gaming',  # Category slug
            'cta_label': 'Shop Gaming',
            'secondary_cta_label': 'View All',
            'secondary_link_type': 'anchor',
            'secondary_link_target': 'featured-products',
            'order': 1,
            'is_active': True,
            'background_gradient_start': '#667eea',
            'background_gradient_end': '#764ba2'
        }
    )
    print(f"✅ Banner 1: {banner1.title} → {banner1.get_redirect_url('primary')}")
    
    # Banner 2: Product Link (if we have products)
    products = Product.objects.all()[:1]
    if products:
        product = products[0]
        banner2, created = HeroBanner.objects.get_or_create(
            title=f"Featured: {product.name}",
            defaults={
                'description': f'Check out our featured product: {product.name}',
                'brand_text': 'FEATURED',
                'image_alt': f'{product.name} Product',
                'link_type': 'product',
                'link_target': product.slug,
                'cta_label': 'Buy Now',
                'secondary_cta_label': 'Learn More',
                'secondary_link_type': 'category',
                'secondary_link_target': product.category.slug if product.category else 'electronics',
                'order': 2,
                'is_active': True,
                'background_gradient_start': '#f093fb',
                'background_gradient_end': '#f5576c'
            }
        )
        print(f"✅ Banner 2: {banner2.title} → {banner2.get_redirect_url('primary')}")
    
    # Banner 3: Anchor Link (Page Section)
    banner3, created = HeroBanner.objects.get_or_create(
        title="Special Offers",
        defaults={
            'description': 'Check out our amazing deals and discounts',
            'brand_text': 'DEALS',
            'image_alt': 'Special Offers Section',
            'link_type': 'anchor',
            'link_target': 'sales-section',
            'cta_label': 'View Deals',
            'secondary_cta_label': 'All Products',
            'secondary_link_type': 'custom',
            'secondary_link_target': '/products/',
            'order': 3,
            'is_active': True,
            'background_gradient_start': '#4facfe',
            'background_gradient_end': '#00f2fe'
        }
    )
    print(f"✅ Banner 3: {banner3.title} → {banner3.get_redirect_url('primary')}")
    
    # Banner 4: Custom URL
    banner4, created = HeroBanner.objects.get_or_create(
        title="Visit Our Store",
        defaults={
            'description': 'Find us at our physical location',
            'brand_text': 'VISIT',
            'image_alt': 'Store Location',
            'link_type': 'custom',
            'link_target': '/contact/',
            'cta_label': 'Get Directions',
            'order': 4,
            'is_active': True,
            'background_gradient_start': '#fa709a',
            'background_gradient_end': '#fee140'
        }
    )
    print(f"✅ Banner 4: {banner4.title} → {banner4.get_redirect_url('primary')}")
    
    print(f"\n🎉 Created {HeroBanner.objects.filter(is_active=True).count()} active banners!")
    
    # Test URL generation
    print("\n🔗 Testing URL Generation:")
    for banner in HeroBanner.objects.filter(is_active=True).order_by('order'):
        primary_url = banner.get_redirect_url('primary')
        secondary_url = banner.get_redirect_url('secondary') if banner.has_secondary_button() else 'N/A'
        print(f"   {banner.title}:")
        print(f"     Primary ({banner.link_type}): {primary_url}")
        print(f"     Secondary: {secondary_url}")

if __name__ == "__main__":
    create_test_banners()
