{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shopping Cart - iTechStore</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        /* Additional styles for cart page */
        .cart-container {
            padding: 40px 0;
        }

        .cart-title {
            margin-bottom: 30px;
            font-size: 32px;
            font-weight: 700;
        }

        .cart-empty {
            text-align: center;
            padding: 50px 0;
        }

        .cart-empty i {
            font-size: 60px;
            color: #ccc;
            margin-bottom: 20px;
        }

        .cart-empty p {
            font-size: 18px;
            color: #777;
            margin-bottom: 30px;
        }

        .cart-table {
            width: 100%;
            border-collapse: collapse;
        }

        .cart-table th {
            text-align: left;
            padding: 15px;
            background-color: #f5f7fa;
            border-bottom: 1px solid #eee;
        }

        .cart-table td {
            padding: 15px;
            border-bottom: 1px solid #eee;
            vertical-align: middle;
        }

        .cart-product {
            display: flex;
            align-items: center;
        }

        .cart-product-image {
            width: 80px;
            height: 80px;
            margin-right: 15px;
            border-radius: 4px;
            overflow: hidden;
        }

        .cart-product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .cart-product-info h3 {
            font-size: 16px;
            margin-bottom: 5px;
        }

        .cart-product-info .status {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 4px;
            display: inline-block;
        }

        .cart-product-info .status.available {
            background-color: #e8f5e9;
            color: #4caf50;
        }

        .cart-product-info .status.preorder {
            background-color: #fff3e0;
            color: #ff9800;
        }

        .quantity-control {
            display: flex;
            align-items: center;
        }

        .quantity-btn {
            width: 30px;
            height: 30px;
            background-color: #f5f7fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            font-size: 14px;
        }

        .quantity-input {
            width: 50px;
            height: 30px;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-align: center;
            margin: 0 5px;
        }

        .remove-btn {
            color: #f44336;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 18px;
        }

        .cart-summary {
            margin-top: 30px;
            background-color: #f5f7fa;
            padding: 20px;
            border-radius: 8px;
        }

        .cart-summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .cart-summary-row.total {
            font-size: 20px;
            font-weight: 700;
            border-top: 1px solid #ddd;
            padding-top: 15px;
        }

        .cart-actions {
            margin-top: 30px;
            display: flex;
            justify-content: space-between;
        }

        .continue-shopping {
            background-color: #f5f7fa;
            color: #333;
            border: 1px solid #ddd;
            padding: 12px 20px;
            border-radius: 4px;
            font-weight: 500;
            transition: background-color 0.3s;
        }

        .continue-shopping:hover {
            background-color: #e9ecef;
        }

        .checkout-btn {
            background-color: #4dabf7;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 4px;
            font-weight: 500;
            transition: background-color 0.3s;
        }

        .checkout-btn:hover {
            background-color: #0c8599;
        }
    </style>
</head>
<body>
    <!-- Top Navigation Bar -->
    <div class="top-nav">
        <div class="container">
            <div class="top-nav-left">
                <span>NPR</span>
                <i class="fas fa-chevron-down"></i>
            </div>
            <div class="top-nav-right">
                {% if user.is_authenticated %}
                    <span>Welcome, {{ user.username }}</span>
                    <a href="{% url 'logout' %}" class="sign-in">Logout</a>
                {% else %}
                    <a href="{% url 'register' %}" class="create-account">Create an account</a>
                    <a href="{% url 'login' %}" class="sign-in">Sign in</a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Main Navigation -->
    <nav class="main-nav">
        <div class="container">
            <div class="logo">
                <a href="{% url 'home' %}">
                    <h1><span class="i-text">i</span><span class="tech-text">Tech</span><span class="store-text">Store</span></h1>
                    <p class="tagline">Your Technology Destination</p>
                </a>
            </div>
            <div class="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </div>
            <div class="nav-links">
                <ul>
                    <li><a href="{% url 'home' %}">Home</a></li>
                    <li class="dropdown">
                        <a href="#">Shop <i class="fas fa-chevron-down"></i></a>
                        <div class="dropdown-content">
                            <a href="#">Laptops</a>
                            <a href="#">Smartphones</a>
                            <a href="#">Tablets</a>
                            <a href="#">Accessories</a>
                        </div>
                    </li>
                    <li><a href="#">Brands</a></li>
                    <li class="dropdown">
                        <a href="#">Blogs <i class="fas fa-chevron-down"></i></a>
                        <div class="dropdown-content">
                            <a href="#">Tech News</a>
                            <a href="#">Reviews</a>
                            <a href="#">Tutorials</a>
                        </div>
                    </li>
                    <li><a href="#">PC BLD</a></li>
                </ul>
            </div>
            <div class="nav-icons">
                <a href="#" class="search-icon"><i class="fas fa-search"></i></a>
                <a href="{% url 'cart_summary' %}" class="cart-icon active">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count">{{ cart.total_items }}</span>
                </a>
            </div>
        </div>
    </nav>

    <!-- Cart Section -->
    <section class="cart-container">
        <div class="container">
            <h1 class="cart-title">Shopping Cart</h1>

            {% if messages %}
                <div class="messages">
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}

            {% if cart_items %}
                <table class="cart-table">
                    <thead>
                        <tr>
                            <th>Product</th>
                            <th>Price</th>
                            <th>Quantity</th>
                            <th>Total</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in cart_items %}
                            <tr data-item-id="{{ item.id }}">
                                <td>
                                    <div class="cart-product">
                                        <div class="cart-product-image">
                                            {% if item.product.image %}
                                                <img src="{{ item.product.image.url }}" alt="{{ item.product.name }}">
                                            {% else %}
                                                <img src="https://via.placeholder.com/300x200?text=Product+Image" alt="{{ item.product.name }}">
                                            {% endif %}
                                        </div>
                                        <div class="cart-product-info">
                                            <h3>{{ item.product.name }}</h3>
                                            {% if item.is_preorder %}
                                                <span class="status preorder">Pre-order</span>
                                            {% else %}
                                                <span class="status available">Available</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td>${{ item.product.price }}</td>
                                <td>
                                    <form method="post" action="{% url 'update_cart' item.id %}" class="quantity-form">
                                        {% csrf_token %}
                                        <div class="quantity-control">
                                            <button type="button" class="quantity-btn decrease">-</button>
                                            <input type="number" name="quantity" value="{{ item.quantity }}" min="1" class="quantity-input">
                                            <button type="button" class="quantity-btn increase">+</button>
                                        </div>
                                    </form>
                                </td>
                                <td class="item-total">${{ item.total_price }}</td>
                                <td>
                                    <form method="post" action="{% url 'remove_from_cart' item.id %}" class="remove-form">
                                        {% csrf_token %}
                                        <button type="submit" class="remove-btn"><i class="fas fa-trash-alt"></i></button>
                                    </form>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>

                <div class="cart-summary">
                    <div class="cart-summary-row">
                        <span>Subtotal:</span>
                        <span class="cart-subtotal">${{ cart.total_price }}</span>
                    </div>
                    <div class="cart-summary-row">
                        <span>Shipping:</span>
                        <span>Free</span>
                    </div>
                    <div class="cart-summary-row total">
                        <span>Total:</span>
                        <span class="cart-total">${{ cart.total_price }}</span>
                    </div>
                </div>

                <div class="cart-actions">
                    <a href="{% url 'home' %}" class="continue-shopping">Continue Shopping</a>
                    <form method="post" action="{% url 'clear_cart' %}" class="clear-cart-form">
                        {% csrf_token %}
                        <button type="submit" class="continue-shopping">Clear Cart</button>
                    </form>
                    <a href="#" class="checkout-btn">Proceed to Checkout</a>
                </div>
            {% else %}
                <div class="cart-empty">
                    <i class="fas fa-shopping-cart"></i>
                    <p>Your cart is empty</p>
                    <a href="{% url 'home' %}" class="continue-shopping">Continue Shopping</a>
                </div>
            {% endif %}
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-columns">
                <div class="footer-column">
                    <h3>Shop</h3>
                    <ul>
                        <li><a href="#">Laptops</a></li>
                        <li><a href="#">Smartphones</a></li>
                        <li><a href="#">Tablets</a></li>
                        <li><a href="#">Accessories</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Support</h3>
                    <ul>
                        <li><a href="#">Contact Us</a></li>
                        <li><a href="#">FAQs</a></li>
                        <li><a href="#">Shipping</a></li>
                        <li><a href="#">Returns</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Company</h3>
                    <ul>
                        <li><a href="#">About Us</a></li>
                        <li><a href="#">Careers</a></li>
                        <li><a href="#">Blog</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Connect</h3>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                    <div class="newsletter">
                        <h4>Subscribe to our newsletter</h4>
                        <form>
                            <input type="email" placeholder="Your email address">
                            <button type="submit">Subscribe</button>
                        </form>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 iTechStore. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Floating Cart Button (hidden on cart page) -->
    <a href="{% url 'cart_summary' %}" class="floating-cart" style="display: none;">
        <i class="fas fa-shopping-cart"></i>
        <span class="floating-cart-count">{{ cart.total_items|default:"0" }}</span>
    </a>

    <script src="{% static 'js/script.js' %}"></script>
    <script src="{% static 'js/cart.js' %}"></script>
</body>
</html>
