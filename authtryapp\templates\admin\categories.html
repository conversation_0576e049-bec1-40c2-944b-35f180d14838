{% extends 'admin/base_admin.html' %}

{% block title %}Categories Management{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mt-4 mb-4">
        <h1>Categories Management</h1>
        <a href="{% url 'admin_category_add' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add New Category
        </a>
    </div>
    
    <!-- Categories Grid -->
    <div class="row">
        {% for category in categories %}
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            {% if category.image %}
                            <img src="{{ category.image.url }}" alt="{{ category.name }}" class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                            {% endif %}
                            <div>
                                <h5 class="mb-0">{{ category.name }}</h5>
                                {% if category.description %}
                                <small class="text-muted">{{ category.description|truncatewords:30 }}</small>
                                {% endif %}
                            </div>
                        </div>
                        <div class="btn-group">
                            <a href="{% url 'admin_category_edit' category.id %}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            <a href="{% url 'admin_category_delete' category.id %}" class="btn btn-sm btn-outline-danger">
                                <i class="fas fa-trash"></i> Delete
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">
                            Products in this category 
                            <span class="badge bg-secondary">{{ category.products.count }}</span>
                        </h6>
                        <div>
                            <a href="{% url 'admin_products_by_category' category.id %}" class="btn btn-sm btn-outline-primary me-2">
                                <i class="fas fa-search"></i> View All
                            </a>
                            <a href="{% url 'admin_product_add' %}?category={{ category.id }}" class="btn btn-sm btn-success">
                                <i class="fas fa-plus"></i> Add Product
                            </a>
                        </div>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Image</th>
                                    <th>Name</th>
                                    <th>Price</th>
                                    <th>Stock Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in category.products.all|slice:":5" %}
                                <tr>
                                    <td>
                                        {% if product.image %}
                                        <img src="{{ product.image.url }}" alt="{{ product.name }}" style="width: 50px; height: 50px; object-fit: cover;">
                                        {% else %}
                                        <div class="bg-light" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                        {% endif %}
                                    </td>
                                    <td>{{ product.name }}</td>
                                    <td>${{ product.price|default:"N/A" }}</td>
                                    <td>
                                        <span class="badge {% if product.stock_status == 'available' %}bg-success{% elif product.stock_status == 'preorder' %}bg-warning{% else %}bg-danger{% endif %}">
                                            {{ product.get_stock_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{% url 'admin_product_edit' product.id %}?referring_category={{ category.id }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'admin_product_view' product.id %}" class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'admin_product_delete' product.id %}" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-box-open fa-2x mb-2"></i>
                                            <p class="mb-0">No products in this category</p>
                                            <a href="{% url 'admin_product_add' %}?category={{ category.id }}" class="btn btn-sm btn-outline-primary mt-2">
                                                Add your first product
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                                
                                {% if category.products.count > 5 %}
                                <tr class="bg-light">
                                    <td colspan="5" class="text-center">
                                        <a href="{% url 'admin_products_by_category' category.id %}" class="btn btn-link">
                                            View all {{ category.products.count }} products in this category
                                        </a>
                                    </td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                    <h4>No Categories Found</h4>
                    <p class="text-muted">Get started by creating your first category</p>
                    <a href="{% url 'admin_category_add' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create Category
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: none;
}
.card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}
.table th {
    font-weight: 600;
    background-color: #f8f9fa;
}
.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}
.btn-group .btn {
    padding: 0.25rem 0.5rem;
}
</style>
{% endblock %}
