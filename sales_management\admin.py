from django.contrib import admin
from django.db import models
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from .models import (
    SaleCategory, ProductSaleAssignment, SaleUsageLog,
    SalePerformanceMetrics, SaleCampaign, SaleNotification
)


@admin.register(SaleCategory)
class SaleCategoryAdmin(admin.ModelAdmin):
    list_display = (
        'name', 'sale_type_display', 'badge_preview', 'priority',
        'is_active', 'is_featured', 'is_currently_active',
        'product_count', 'usage_count', 'start_date', 'end_date'
    )
    list_filter = (
        'sale_type', 'is_active', 'is_featured', 'priority',
        'is_member_only', 'requires_coupon_code', 'start_date', 'end_date'
    )
    search_fields = ('name', 'badge_text', 'description', 'coupon_code')
    ordering = ('-priority', '-is_featured', '-start_date')
    list_per_page = 25
    prepopulated_fields = {'slug': ('name',)}

    actions = [
        'activate_sales', 'deactivate_sales', 'mark_as_featured',
        'unmark_as_featured', 'duplicate_sales'
    ]

    readonly_fields = ('current_uses', 'created_at', 'updated_at')

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'sale_type', 'description', 'priority')
        }),
        ('Sale Configuration', {
            'fields': (
                'discount_percentage', 'discount_amount', 'minimum_purchase_amount',
                'maximum_discount_amount', 'free_item_description', 'free_item_value',
                'buy_quantity', 'get_quantity'
            ),
            'description': 'Fill in the relevant fields based on the sale type selected above.'
        }),
        ('Display Settings', {
            'fields': ('badge_text', 'badge_color')
        }),
        ('Timing & Availability', {
            'fields': ('start_date', 'end_date', 'is_active', 'is_featured')
        }),
        ('Usage Limits', {
            'fields': (
                'max_uses_per_customer', 'max_total_uses', 'current_uses',
                'requires_coupon_code', 'coupon_code'
            )
        }),
        ('Advanced Settings', {
            'fields': (
                'is_stackable', 'is_member_only', 'is_first_time_buyer_only'
            ),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    class Media:
        css = {
            'all': ('admin/css/sales_management_admin.css',)
        }
        js = ('admin/js/sales_management_admin.js',)

    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

    def sale_type_display(self, obj):
        """Display sale type with icon"""
        icons = {
            'percentage': '📊', 'fixed_amount': '💰', 'free_item': '🎁',
            'buy_x_get_y': '🔄', 'bundle': '📦', 'clearance': '🏷️',
            'flash_sale': '⚡', 'seasonal': '🌟', 'gift_with_purchase': '🎀',
            'free_shipping': '🚚', 'loyalty_discount': '👑', 'student_discount': '🎓',
            'bulk_discount': '📈', 'referral_bonus': '🤝', 'first_time_buyer': '🆕'
        }
        icon = icons.get(obj.sale_type, '🏷️')
        return format_html(f"{icon} {obj.get_sale_type_display()}")
    sale_type_display.short_description = 'Sale Type'

    def badge_preview(self, obj):
        """Show a preview of the badge"""
        return format_html(
            '<span style="background-color: {}; color: white; padding: 3px 8px; '
            'border-radius: 4px; font-size: 11px; font-weight: bold; '
            'text-transform: uppercase;">{}</span>',
            obj.badge_color, obj.badge_text
        )
    badge_preview.short_description = 'Badge Preview'

    def is_currently_active(self, obj):
        """Show if sale is currently active"""
        return obj.is_currently_active()
    is_currently_active.boolean = True
    is_currently_active.short_description = 'Currently Active'

    def product_count(self, obj):
        """Show number of products in this sale"""
        count = obj.product_assignments.filter(is_active=True).count()
        if count > 0:
            url = reverse('admin:sales_management_productsaleassignment_changelist')
            return format_html(
                '<a href="{}?sale_category__id__exact={}">{} products</a>',
                url, obj.id, count
            )
        return '0 products'
    product_count.short_description = 'Products'

    def usage_count(self, obj):
        """Show usage statistics"""
        return f"{obj.current_uses}"
    usage_count.short_description = 'Uses'

    # Custom admin actions
    def activate_sales(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, f'Successfully activated {updated} sale categories.')
    activate_sales.short_description = "Activate selected sales"

    def deactivate_sales(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f'Successfully deactivated {updated} sale categories.')
    deactivate_sales.short_description = "Deactivate selected sales"

    def mark_as_featured(self, request, queryset):
        updated = queryset.update(is_featured=True)
        self.message_user(request, f'Successfully marked {updated} sales as featured.')
    mark_as_featured.short_description = "Mark as featured"

    def unmark_as_featured(self, request, queryset):
        updated = queryset.update(is_featured=False)
        self.message_user(request, f'Successfully removed featured status from {updated} sales.')
    unmark_as_featured.short_description = "Remove featured status"

    def duplicate_sales(self, request, queryset):
        """Duplicate selected sales with new names"""
        count = 0
        for sale in queryset:
            sale.pk = None
            sale.name = f"{sale.name} (Copy)"
            sale.slug = ""
            sale.is_active = False
            sale.created_by = request.user
            sale.save()
            count += 1
        self.message_user(request, f'Successfully duplicated {count} sales.')
    duplicate_sales.short_description = "Duplicate selected sales"

    def changelist_view(self, request, extra_context=None):
        """Add extra context for the changelist template"""
        extra_context = extra_context or {}

        # Add statistics
        extra_context['active_count'] = SaleCategory.objects.filter(is_active=True).count()
        extra_context['featured_count'] = SaleCategory.objects.filter(is_featured=True).count()
        extra_context['total_products_in_sales'] = ProductSaleAssignment.objects.filter(
            is_active=True, sale_category__is_active=True
        ).values('product').distinct().count()

        return super().changelist_view(request, extra_context=extra_context)


@admin.register(ProductSaleAssignment)
class ProductSaleAssignmentAdmin(admin.ModelAdmin):
    list_display = (
        'product', 'sale_category', 'is_active', 'assigned_date',
        'assigned_by', 'custom_discount_display'
    )
    list_filter = (
        'is_active', 'assigned_date', 'sale_category__sale_type',
        'sale_category__is_active'
    )
    search_fields = (
        'product__name', 'sale_category__name', 'assigned_by__username'
    )
    ordering = ('-assigned_date',)
    list_per_page = 50

    autocomplete_fields = ('product', 'sale_category', 'assigned_by')
    readonly_fields = ('assigned_date',)

    fieldsets = (
        ('Assignment Details', {
            'fields': ('product', 'sale_category', 'is_active', 'assigned_by')
        }),
        ('Custom Overrides', {
            'fields': ('custom_discount_percentage', 'custom_discount_amount'),
            'description': 'Override the default sale settings for this specific product.'
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.assigned_by = request.user
        super().save_model(request, obj, form, change)

    def custom_discount_display(self, obj):
        """Show custom discount if any"""
        if obj.custom_discount_percentage:
            return f"{obj.custom_discount_percentage}% (Custom)"
        elif obj.custom_discount_amount:
            return f"${obj.custom_discount_amount} (Custom)"
        return "Default"
    custom_discount_display.short_description = 'Discount'


@admin.register(SaleCampaign)
class SaleCampaignAdmin(admin.ModelAdmin):
    list_display = (
        'name', 'status', 'is_featured', 'start_date', 'end_date',
        'sales_count', 'target_revenue', 'is_active_display'
    )
    list_filter = ('status', 'is_featured', 'start_date', 'end_date')
    search_fields = ('name', 'description')
    ordering = ('-start_date',)
    list_per_page = 20
    prepopulated_fields = {'slug': ('name',)}

    filter_horizontal = ('sale_categories',)
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('Campaign Information', {
            'fields': ('name', 'slug', 'description', 'status', 'is_featured')
        }),
        ('Timing', {
            'fields': ('start_date', 'end_date')
        }),
        ('Goals & Budget', {
            'fields': ('target_revenue', 'target_conversions', 'budget')
        }),
        ('Sale Categories', {
            'fields': ('sale_categories',)
        }),
        ('Metadata', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

    def sales_count(self, obj):
        """Show number of sales in campaign"""
        return obj.get_total_sales_count()
    sales_count.short_description = 'Sales Count'

    def is_active_display(self, obj):
        """Show if campaign is currently active"""
        return obj.is_active()
    is_active_display.boolean = True
    is_active_display.short_description = 'Currently Active'


@admin.register(SaleUsageLog)
class SaleUsageLogAdmin(admin.ModelAdmin):
    list_display = (
        'sale_category', 'product', 'user', 'original_price',
        'discount_amount', 'final_price', 'used_at'
    )
    list_filter = ('used_at', 'sale_category__sale_type')
    search_fields = (
        'sale_category__name', 'product__name', 'user__username',
        'order_id', 'ip_address'
    )
    ordering = ('-used_at',)
    list_per_page = 100

    readonly_fields = ('used_at',)

    fieldsets = (
        ('Usage Details', {
            'fields': ('sale_category', 'product', 'user', 'order_id')
        }),
        ('Pricing', {
            'fields': ('original_price', 'discount_amount', 'final_price')
        }),
        ('Tracking', {
            'fields': ('session_key', 'ip_address', 'user_agent', 'used_at'),
            'classes': ('collapse',)
        }),
    )

    def has_add_permission(self, request):
        """Prevent manual creation of usage logs"""
        return False

    def has_change_permission(self, request, obj=None):
        """Make usage logs read-only"""
        return False


@admin.register(SalePerformanceMetrics)
class SalePerformanceMetricsAdmin(admin.ModelAdmin):
    list_display = (
        'sale_category', 'period_type', 'period_start', 'period_end',
        'total_uses', 'total_savings_given', 'conversion_rate'
    )
    list_filter = ('period_type', 'period_start', 'sale_category__sale_type')
    search_fields = ('sale_category__name',)
    ordering = ('-period_start',)
    list_per_page = 50

    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('Period Information', {
            'fields': ('sale_category', 'period_type', 'period_start', 'period_end')
        }),
        ('Usage Metrics', {
            'fields': ('total_uses', 'unique_users', 'unique_products')
        }),
        ('Financial Metrics', {
            'fields': ('total_savings_given', 'total_revenue_impact')
        }),
        ('Conversion Metrics', {
            'fields': ('views', 'conversions', 'conversion_rate')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(SaleNotification)
class SaleNotificationAdmin(admin.ModelAdmin):
    list_display = (
        'title', 'notification_type', 'priority', 'sale_category',
        'is_read', 'is_sent', 'created_at'
    )
    list_filter = (
        'notification_type', 'priority', 'is_read', 'is_sent',
        'created_at', 'send_to_all_admins'
    )
    search_fields = ('title', 'message', 'sale_category__name')
    ordering = ('-created_at',)
    list_per_page = 50

    readonly_fields = ('created_at', 'sent_at')

    fieldsets = (
        ('Notification Details', {
            'fields': ('notification_type', 'priority', 'title', 'message')
        }),
        ('Related Objects', {
            'fields': ('sale_category', 'campaign')
        }),
        ('Recipients', {
            'fields': ('recipient', 'send_to_all_admins')
        }),
        ('Status', {
            'fields': ('is_read', 'is_sent', 'sent_at', 'created_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['mark_as_read', 'mark_as_sent']

    def mark_as_read(self, request, queryset):
        updated = queryset.update(is_read=True)
        self.message_user(request, f'Marked {updated} notifications as read.')
    mark_as_read.short_description = "Mark selected notifications as read"

    def mark_as_sent(self, request, queryset):
        updated = queryset.update(is_sent=True, sent_at=timezone.now())
        self.message_user(request, f'Marked {updated} notifications as sent.')
    mark_as_sent.short_description = "Mark selected notifications as sent"
