{% extends 'base_new.html' %}
{% load static %}

{% block title %}{{ section_title }} - iTechStore{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/sales_products.css' %}">
<style>
    /* Page-specific styles */
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 60px 0;
        margin-bottom: 40px;
    }
    
    .page-title {
        font-size: 42px;
        font-weight: 700;
        margin-bottom: 10px;
        text-align: center;
    }
    
    .page-subtitle {
        font-size: 20px;
        text-align: center;
        opacity: 0.9;
    }
    
    .filters-section {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 30px;
    }
    
    .filters-row {
        display: flex;
        gap: 20px;
        align-items: center;
        flex-wrap: wrap;
    }
    
    .filter-group {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .filter-label {
        font-weight: 600;
        color: #333;
    }
    
    .filter-select {
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background: white;
    }
    
    .results-info {
        margin-bottom: 20px;
        color: #666;
        font-size: 14px;
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <h1 class="page-title">{{ section_title }}</h1>
        <p class="page-subtitle">{{ section_subtitle }}</p>
    </div>
</section>

<!-- Main Content -->
<div class="container">
    <!-- Filters Section -->
    <div class="filters-section">
        <form method="GET" class="filters-form">
            <div class="filters-row">
                <div class="filter-group">
                    <label class="filter-label">Sort by:</label>
                    <select name="sort" class="filter-select" onchange="this.form.submit()">
                        <option value="">Default</option>
                        <option value="discount_desc" {% if current_sort == 'discount_desc' %}selected{% endif %}>Highest Discount</option>
                        <option value="price_asc" {% if current_sort == 'price_asc' %}selected{% endif %}>Price: Low to High</option>
                        <option value="price_desc" {% if current_sort == 'price_desc' %}selected{% endif %}>Price: High to Low</option>
                        <option value="name_asc" {% if current_sort == 'name_asc' %}selected{% endif %}>Name: A to Z</option>
                        <option value="newest" {% if current_sort == 'newest' %}selected{% endif %}>Newest First</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">Availability:</label>
                    <select name="stock" class="filter-select" onchange="this.form.submit()">
                        <option value="">All Products</option>
                        <option value="available" {% if current_stock == 'available' %}selected{% endif %}>Available</option>
                        <option value="preorder" {% if current_stock == 'preorder' %}selected{% endif %}>Pre-order</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <button type="button" onclick="window.location.href='{% url 'sales_products' %}'" class="filter-select" style="background: #dc3545; color: white; border-color: #dc3545;">
                        Clear Filters
                    </button>
                </div>
            </div>
        </form>
    </div>
    
    <!-- Results Info -->
    {% if sales_products %}
        <div class="results-info">
            {% if sales_products|length == 1 %}
                Showing 1 product
            {% else %}
                Showing {{ sales_products|length }} products
            {% endif %}
        </div>
    {% endif %}
    
    <!-- Sales Products Grid -->
    {% if sales_products %}
        <div class="sales-products-grid">
            {% for product in sales_products %}
                {% include 'includes/sales_product_card.html' with product=product %}
            {% endfor %}
        </div>
    {% else %}
        <!-- No Products Message -->
        <div class="no-products-message" style="text-align: center; padding: 80px 20px; background: #f8f9fa; border-radius: 12px; border: 2px dashed #dee2e6;">
            <i class="fas fa-tag" style="font-size: 64px; color: #ddd; margin-bottom: 20px;"></i>
            <h3 style="color: #666; margin-bottom: 15px; font-size: 24px;">No Sales Products Found</h3>
            <p style="color: #999; font-size: 16px; margin-bottom: 20px;">
                {% if current_stock or current_sort %}
                    Try adjusting your filters or check back later for new deals.
                {% else %}
                    Check back soon for amazing deals and discounts!
                {% endif %}
            </p>
            <a href="{% url 'home' %}" class="view-all-btn">
                <i class="fas fa-home"></i>
                Back to Home
            </a>
        </div>
    {% endif %}
</div>

<!-- JavaScript for enhanced functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add to cart functionality
    const addToCartButtons = document.querySelectorAll('.add-to-cart-btn:not(.disabled)');
    
    addToCartButtons.forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.getAttribute('data-product-id');
            const originalText = this.innerHTML;
            
            // Show loading state
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';
            this.disabled = true;
            
            // Simulate API call (replace with actual implementation)
            setTimeout(() => {
                this.innerHTML = '<i class="fas fa-check"></i> Added!';
                this.style.background = '#28a745';
                
                // Reset after 2 seconds
                setTimeout(() => {
                    this.innerHTML = originalText;
                    this.style.background = '';
                    this.disabled = false;
                }, 2000);
            }, 800);
        });
    });
    
    // Quick view functionality
    const quickViewButtons = document.querySelectorAll('.quick-view-btn');
    
    quickViewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.getAttribute('data-product-id');
            // Implement quick view modal or redirect to product page
            console.log('Quick view for product:', productId);
            // For now, you could redirect to product detail page
            // window.location.href = `/product/${productId}/`;
        });
    });
    
    // Smooth scroll for any anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});
</script>
{% endblock %}
