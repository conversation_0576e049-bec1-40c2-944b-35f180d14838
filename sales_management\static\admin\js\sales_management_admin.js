// Sales Management Admin JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all functionality
    initializeBadgePreview();
    initializeSaleTypeHelper();
    initializeFieldHighlighting();
    initializeFormValidation();
    initializeListEnhancements();
    initializeDashboardFeatures();
    
    console.log('Sales Management Admin initialized');
});

function initializeBadgePreview() {
    const badgeTextInput = document.querySelector('#id_badge_text');
    const badgeColorInput = document.querySelector('#id_badge_color');
    
    if (badgeTextInput && badgeColorInput) {
        // Create preview container
        let previewContainer = document.querySelector('.badge-preview-container');
        if (!previewContainer) {
            previewContainer = document.createElement('div');
            previewContainer.className = 'badge-preview-container';
            previewContainer.innerHTML = `
                <span class="badge-preview-label">🎨 Live Badge Preview:</span>
                <span class="badge-preview">SAMPLE BADGE</span>
            `;
            
            // Insert after the badge color field
            const badgeColorField = badgeColorInput.closest('.form-row');
            if (badgeColorField) {
                badgeColorField.parentNode.insertBefore(previewContainer, badgeColorField.nextSibling);
            }
        }
        
        const previewBadge = previewContainer.querySelector('.badge-preview');
        
        function updateBadgePreview() {
            const text = badgeTextInput.value || 'SAMPLE BADGE';
            const color = badgeColorInput.value || '#dc3545';
            
            previewBadge.style.backgroundColor = color;
            previewBadge.style.color = 'white';
            previewBadge.textContent = text;
            
            // Add pulse animation for new changes
            previewBadge.style.animation = 'none';
            setTimeout(() => {
                previewBadge.style.animation = 'pulse 0.5s ease-in-out';
            }, 10);
        }
        
        // Update preview on input change
        badgeTextInput.addEventListener('input', updateBadgePreview);
        badgeColorInput.addEventListener('input', updateBadgePreview);
        badgeColorInput.addEventListener('change', updateBadgePreview);
        
        // Initial preview
        updateBadgePreview();
    }
}

function initializeSaleTypeHelper() {
    const saleTypeSelect = document.querySelector('#id_sale_type');
    if (saleTypeSelect) {
        // Create helper container
        const helperContainer = document.createElement('div');
        helperContainer.className = 'sale-type-helper';
        saleTypeSelect.parentNode.appendChild(helperContainer);
        
        const saleTypeHelp = {
            'percentage': 'Fill in the "Discount percentage" field (e.g., 10 for 10% off)',
            'fixed_amount': 'Fill in the "Discount amount" field (e.g., 100 for $100 off)',
            'free_item': 'Fill in the "Free item description" field (e.g., "Free wireless mouse")',
            'buy_x_get_y': 'Fill in both "Buy quantity" and "Get quantity" fields (e.g., Buy 2, Get 1)',
            'bundle': 'Use "Discount percentage" for bundle savings',
            'clearance': 'Use "Discount percentage" for clearance discount',
            'flash_sale': 'Use "Discount percentage" and set an end date for urgency',
            'seasonal': 'Use "Discount percentage" for seasonal discount',
            'gift_with_purchase': 'Fill in "Free item description" for the gift',
            'free_shipping': 'No additional fields needed - just activate the sale',
            'loyalty_discount': 'Use "Discount percentage" and check "Member only"',
            'student_discount': 'Use "Discount percentage" and add verification requirements',
            'bulk_discount': 'Use "Discount percentage" and set "Minimum purchase amount"',
            'referral_bonus': 'Use "Discount amount" or "Discount percentage"',
            'first_time_buyer': 'Use "Discount percentage" and check "First time buyer only"'
        };
        
        function updateHelper() {
            const selectedType = saleTypeSelect.value;
            const helpText = saleTypeHelp[selectedType] || 'Select a sale type to see relevant fields';
            helperContainer.innerHTML = `<strong>💡 Configuration Tip:</strong> ${helpText}`;
        }
        
        saleTypeSelect.addEventListener('change', updateHelper);
        updateHelper(); // Initial call
    }
}

function initializeFieldHighlighting() {
    const saleTypeSelect = document.querySelector('#id_sale_type');
    if (saleTypeSelect) {
        const fieldMappings = {
            'percentage': ['#id_discount_percentage', '#id_maximum_discount_amount'],
            'fixed_amount': ['#id_discount_amount'],
            'free_item': ['#id_free_item_description', '#id_free_item_value'],
            'buy_x_get_y': ['#id_buy_quantity', '#id_get_quantity'],
            'bundle': ['#id_discount_percentage', '#id_minimum_purchase_amount'],
            'clearance': ['#id_discount_percentage'],
            'flash_sale': ['#id_discount_percentage', '#id_end_date_0', '#id_end_date_1'],
            'seasonal': ['#id_discount_percentage'],
            'gift_with_purchase': ['#id_free_item_description', '#id_free_item_value'],
            'free_shipping': [],
            'loyalty_discount': ['#id_discount_percentage', '#id_is_member_only'],
            'student_discount': ['#id_discount_percentage'],
            'bulk_discount': ['#id_discount_percentage', '#id_minimum_purchase_amount'],
            'referral_bonus': ['#id_discount_percentage', '#id_discount_amount'],
            'first_time_buyer': ['#id_discount_percentage', '#id_is_first_time_buyer_only']
        };
        
        function highlightRelevantFields() {
            // Remove all highlights first
            Object.values(fieldMappings).flat().forEach(selector => {
                const field = document.querySelector(selector);
                if (field) {
                    field.style.border = '';
                    field.style.backgroundColor = '';
                    field.style.boxShadow = '';
                }
            });
            
            // Add highlights for current selection
            const selectedType = saleTypeSelect.value;
            const relevantFields = fieldMappings[selectedType] || [];
            
            relevantFields.forEach(selector => {
                const field = document.querySelector(selector);
                if (field) {
                    field.style.border = '3px solid #667eea';
                    field.style.backgroundColor = '#f8f9ff';
                    field.style.boxShadow = '0 0 10px rgba(102, 126, 234, 0.3)';
                    field.style.transition = 'all 0.3s ease';
                }
            });
        }
        
        saleTypeSelect.addEventListener('change', highlightRelevantFields);
        highlightRelevantFields(); // Initial call
    }
}

function initializeFormValidation() {
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const saleType = document.querySelector('#id_sale_type')?.value;
            const discountPercentage = document.querySelector('#id_discount_percentage')?.value;
            const discountAmount = document.querySelector('#id_discount_amount')?.value;
            const freeItemDescription = document.querySelector('#id_free_item_description')?.value;
            const buyQuantity = document.querySelector('#id_buy_quantity')?.value;
            const getQuantity = document.querySelector('#id_get_quantity')?.value;
            
            let isValid = true;
            let errorMessage = '';
            
            // Validate based on sale type
            switch (saleType) {
                case 'percentage':
                    if (!discountPercentage || discountPercentage <= 0 || discountPercentage > 100) {
                        isValid = false;
                        errorMessage = 'Please enter a valid discount percentage (1-100)';
                    }
                    break;
                case 'fixed_amount':
                    if (!discountAmount || discountAmount <= 0) {
                        isValid = false;
                        errorMessage = 'Please enter a valid discount amount';
                    }
                    break;
                case 'free_item':
                case 'gift_with_purchase':
                    if (!freeItemDescription) {
                        isValid = false;
                        errorMessage = 'Please enter a description for the free item';
                    }
                    break;
                case 'buy_x_get_y':
                    if (!buyQuantity || !getQuantity || buyQuantity <= 0 || getQuantity <= 0) {
                        isValid = false;
                        errorMessage = 'Please enter valid buy and get quantities';
                    }
                    break;
            }
            
            if (!isValid) {
                e.preventDefault();
                alert('⚠️ Validation Error: ' + errorMessage);
                return false;
            }
        });
    }
}

function initializeListEnhancements() {
    // Add row classes for styling
    const rows = document.querySelectorAll('.change-list tbody tr');
    rows.forEach(row => {
        // Check if featured
        const featuredCell = row.querySelector('.field-is_featured');
        if (featuredCell && featuredCell.textContent.includes('True')) {
            row.classList.add('featured');
        }
        
        // Check if inactive
        const activeCell = row.querySelector('.field-is_active');
        if (activeCell && activeCell.textContent.includes('False')) {
            row.classList.add('inactive');
        }
        
        // Check if currently active
        const currentlyActiveCell = row.querySelector('.field-is_currently_active');
        if (currentlyActiveCell && currentlyActiveCell.textContent.includes('True')) {
            row.classList.add('active-sale');
        }
    });
    
    // Add tooltips to badge previews
    const badgePreviews = document.querySelectorAll('.field-badge_preview span');
    badgePreviews.forEach(badge => {
        badge.title = 'This is how the badge will appear on products';
        badge.style.cursor = 'help';
    });
}

function initializeDashboardFeatures() {
    // Add page title enhancements
    const pageTitle = document.querySelector('.change-list h1, .change-form h1');
    if (pageTitle) {
        if (pageTitle.textContent.includes('Sale categories')) {
            pageTitle.innerHTML = '🏷️ ' + pageTitle.innerHTML;
        } else if (pageTitle.textContent.includes('Sale campaigns')) {
            pageTitle.innerHTML = '📢 ' + pageTitle.innerHTML;
        } else if (pageTitle.textContent.includes('Product sale assignments')) {
            pageTitle.innerHTML = '🔗 ' + pageTitle.innerHTML;
        }
    }
    
    // Enhance add buttons
    const addButtons = document.querySelectorAll('.addlink');
    addButtons.forEach(button => {
        if (button.href && button.href.includes('salecategory')) {
            button.innerHTML = '🏷️ ' + button.innerHTML;
            button.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
            button.style.color = 'white';
            button.style.padding = '10px 20px';
            button.style.borderRadius = '8px';
            button.style.fontWeight = '600';
            button.style.textDecoration = 'none';
            button.style.boxShadow = '0 2px 8px rgba(40, 167, 69, 0.3)';
            button.style.transition = 'all 0.2s ease';
            
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 4px 12px rgba(40, 167, 69, 0.4)';
            });
            
            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 2px 8px rgba(40, 167, 69, 0.3)';
            });
        }
    });
}

// Utility functions
window.SalesManagementAdmin = {
    showSuccessMessage: function(message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'alert alert-success';
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            padding: 15px 25px;
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 2px solid #28a745;
            border-radius: 8px;
            color: #155724;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
            animation: slideInFromRight 0.3s ease-out;
        `;
        messageDiv.innerHTML = `✅ ${message}`;
        
        document.body.appendChild(messageDiv);
        
        setTimeout(() => {
            messageDiv.style.animation = 'slideOutToRight 0.3s ease-in';
            setTimeout(() => messageDiv.remove(), 300);
        }, 3000);
    },
    
    confirmBulkAction: function(actionName, count) {
        return confirm(`🤔 Are you sure you want to ${actionName} ${count} items?\n\nThis action cannot be undone.`);
    },
    
    calculateSavings: function(originalPrice, discountPercentage, discountAmount) {
        if (discountPercentage) {
            return (originalPrice * discountPercentage) / 100;
        } else if (discountAmount) {
            return Math.min(discountAmount, originalPrice);
        }
        return 0;
    }
};

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInFromRight {
        0% { opacity: 0; transform: translateX(100px); }
        100% { opacity: 1; transform: translateX(0); }
    }
    
    @keyframes slideOutToRight {
        0% { opacity: 1; transform: translateX(0); }
        100% { opacity: 0; transform: translateX(100px); }
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
`;
document.head.appendChild(style);
