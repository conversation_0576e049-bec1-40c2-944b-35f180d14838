{% extends "base_new.html" %}

{% block title %}{{ category.name }} - Products{% endblock %}

{% block content %}
<!-- Category Header -->
<section class="category-header py-5 bg-light">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-4">{{ category.name }}</h1>
                {% if category.description %}
                    <p class="lead text-muted">{{ category.description }}</p>
                {% endif %}
            </div>
            {% if category.image %}
            <div class="col-md-4">
                <img src="{{ category.image.url }}" alt="{{ category.name }}" class="img-fluid rounded">
            </div>
            {% endif %}
        </div>
    </div>
</section>

<!-- Products Grid -->
<section class="sales-section">
    <div class="container">
        {% if products %}
            <div class="sales-products-grid">
                {% for product in products %}
                    {% include 'includes/sales_product_card.html' with product=product %}
                {% endfor %}
            </div>
        {% else %}
        <div class="text-center py-5">
            <h3>No products found in this category</h3>
            <p class="text-muted">Check back later for new products!</p>
            <a href="{% url 'home' %}" class="btn btn-primary mt-3">Return to Home</a>
        </div>
        {% endif %}
    </div>
</section>

<!-- Category Navigation -->
<section class="category-nav py-4 bg-light">
    <div class="container">
        <h4 class="mb-3">Browse Other Categories</h4>
        <div class="row">
            {% for other_category in categories %}
            {% if other_category != category %}
            <div class="col-6 col-md-3 mb-3">
                <a href="{% url 'category_view' other_category.slug %}"
                   class="btn btn-outline-secondary w-100">
                    {{ other_category.name }}
                </a>
            </div>
            {% endif %}
            {% endfor %}
        </div>
    </div>
</section>

{% endblock %}

{% block extra_css %}
<style>
.product-card {
    transition: transform 0.2s;
    border: none;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}
.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}
.category-header {
    background: linear-gradient(45deg, #f8f9fa 0%, #e9ecef 100%);
}
</style>
{% endblock %}
