<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <title>Admin Dashboard - {% block title %}{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --secondary-color: #f0f9ff;
            --accent-color: #0284c7;
            --text-dark: #1e293b;
            --text-light: #64748b;
            --bg-light: #f8fafc;
            --bg-white: #ffffff;
            --shadow-sm: 0 1px 2px rgba(0,0,0,0.05);
            --shadow-md: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
        }

        body {
            background-color: #f1f5f9;
            color: var(--text-dark);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        .navbar {
            box-shadow: var(--shadow-md);
            background: linear-gradient(to right, var(--primary-color), #3b82f6);
        }

        .navbar-brand {
            font-weight: 700;
            letter-spacing: -0.01em;
        }

        .navbar-dark .navbar-nav .nav-link {
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: var(--radius-md);
            transition: all 0.2s;
        }

        .navbar-dark .navbar-nav .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .navbar-dark .navbar-nav .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.15);
        }

        .alert {
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-sm);
        }

        .alert-success {
            background-color: #ecfdf5;
            border-left: 4px solid #10b981;
            color: #065f46;
        }

        .alert-danger {
            background-color: #fef2f2;
            border-left: 4px solid #ef4444;
            color: #b91c1c;
        }

        .alert-warning {
            background-color: #fffbeb;
            border-left: 4px solid #f59e0b;
            color: #92400e;
        }

        .alert-info {
            background-color: #f0f9ff;
            border-left: 4px solid #0ea5e9;
            color: #0c4a6e;
        }

        .btn-close {
            font-size: 0.8rem;
        }

        #layoutSidenav_content {
            padding-bottom: 3rem;
        }

        footer {
            background-color: #f8fafc;
            color: var(--text-light);
            box-shadow: 0 -1px 3px rgba(0,0,0,0.05);
        }

        .text-muted {
            color: #94a3b8 !important;
        }

        @media (max-width: 768px) {
            .navbar-nav {
                padding-top: 1rem;
            }

            .navbar-nav .nav-item {
                margin-bottom: 0.5rem;
            }
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body class="sb-nav-fixed">
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'admin_dashboard' %}">
                <i class="fas fa-tachometer-alt me-2"></i>Admin Dashboard
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link {% if 'dashboard' in request.path %}active{% endif %}" href="{% url 'admin_dashboard' %}">
                            <i class="fas fa-home me-1"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/admin/categories/' in request.path %}active{% endif %}" href="{% url 'admin_categories' %}">
                            <i class="fas fa-folder me-1"></i> Categories
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/admin/brands/' in request.path %}active{% endif %}" href="{% url 'admin_brands' %}">
                            <i class="fas fa-trademark me-1"></i> Brands
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/admin/products/' in request.path %}active{% endif %}" href="{% url 'admin_products' %}">
                            <i class="fas fa-box me-1"></i> Products
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/admin/sales/' in request.path %}active{% endif %}" href="{% url 'admin_sales_dashboard' %}">
                            <i class="fas fa-tags me-1"></i> Sales Management
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/admin/orders/' in request.path %}active{% endif %}" href="{% url 'admin_orders' %}">
                            <i class="fas fa-shopping-cart me-1"></i> Orders
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/admin/users/' in request.path %}active{% endif %}" href="{% url 'admin_users' %}">
                            <i class="fas fa-users me-1"></i> Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/admin/hero-banners/' in request.path %}active{% endif %}" href="{% url 'admin_hero_banners' %}">
                            <i class="fas fa-images me-1"></i> Hero Banners
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/admin/site-config/' in request.path %}active{% endif %}" href="{% url 'admin_site_config' %}">
                            <i class="fas fa-cog me-1"></i> Site Config
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/admin/section-headings/' in request.path %}active{% endif %}" href="{% url 'admin_section_headings' %}">
                            <i class="fas fa-heading me-1"></i> Section Headings
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/admin/promotions/' in request.path %}active{% endif %}" href="{% url 'admin_promotions' %}">
                            <i class="fas fa-tags me-1"></i> Promotions
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'admin_logout' %}">
                            <i class="fas fa-sign-out-alt me-1"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div id="layoutSidenav">
        <div id="layoutSidenav_content">
            <main>
                {% if messages %}
                <div class="container-fluid px-4 mt-4">
                    {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                {% block content %}{% endblock %}
            </main>
            <footer class="py-4 mt-auto">
                <div class="container-fluid px-4">
                    <div class="d-flex align-items-center justify-content-between small">
                        <div class="text-muted">Copyright &copy; iTechStore Admin Panel 2025</div>
                    </div>
                </div>
            </footer>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
