{% extends 'base_new.html' %}
{% load static %}

{% block title %}My Profile - iTechStore{% endblock %}

{% block extra_css %}
<style>
    /* Additional styles for profile page */
    .profile-container {
        padding: 60px 0;
    }
    
    .profile-header {
        background: linear-gradient(to right, #4dabf7, #0c8599);
        padding: 40px 0;
        color: white;
        margin-bottom: 40px;
        border-radius: 10px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    }
    
    .profile-header h1 {
        font-size: 32px;
        margin-bottom: 10px;
    }
    
    .profile-header p {
        font-size: 18px;
        opacity: 0.9;
    }
    
    .profile-content {
        display: flex;
        gap: 30px;
    }
    
    .profile-sidebar {
        width: 250px;
        flex-shrink: 0;
    }
    
    .profile-main {
        flex-grow: 1;
    }
    
    .profile-menu {
        background-color: white;
        border-radius: 10px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .profile-menu-item {
        padding: 15px 20px;
        border-bottom: 1px solid #eee;
        display: flex;
        align-items: center;
        transition: all 0.3s;
        cursor: pointer;
    }
    
    .profile-menu-item:last-child {
        border-bottom: none;
    }
    
    .profile-menu-item.active {
        background-color: #f5f9ff;
        border-left: 4px solid #4dabf7;
    }
    
    .profile-menu-item:hover {
        background-color: #f5f9ff;
    }
    
    .profile-menu-item i {
        margin-right: 15px;
        color: #4dabf7;
        width: 20px;
        text-align: center;
    }
    
    .profile-section {
        background-color: white;
        border-radius: 10px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        padding: 30px;
        margin-bottom: 30px;
    }
    
    .profile-section h2 {
        font-size: 24px;
        margin-bottom: 20px;
        color: #333;
        border-bottom: 1px solid #eee;
        padding-bottom: 15px;
    }
    
    .profile-info {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
    
    .info-item {
        margin-bottom: 20px;
    }
    
    .info-item label {
        display: block;
        font-weight: 600;
        margin-bottom: 8px;
        color: #777;
    }
    
    .info-item p {
        font-size: 16px;
        color: #333;
    }
    
    .edit-button {
        background-color: #4dabf7;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        font-weight: 500;
        transition: background-color 0.3s;
    }
    
    .edit-button:hover {
        background-color: #0c8599;
    }
    
    .order-list {
        width: 100%;
    }
    
    .order-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 0;
        border-bottom: 1px solid #eee;
    }
    
    .order-item:last-child {
        border-bottom: none;
    }
    
    .order-id {
        font-weight: 600;
        color: #4dabf7;
    }
    
    .order-date {
        color: #777;
    }
    
    .order-status {
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
    }
    
    .status-completed {
        background-color: #e6f7ee;
        color: #2e7d32;
    }
    
    .status-processing {
        background-color: #fff8e1;
        color: #ff8f00;
    }
    
    .status-cancelled {
        background-color: #ffebee;
        color: #c62828;
    }
    
    .order-total {
        font-weight: 600;
    }
    
    .order-actions a {
        color: #4dabf7;
        text-decoration: none;
        margin-left: 15px;
    }
    
    .order-actions a:hover {
        text-decoration: underline;
    }
    
    @media (max-width: 768px) {
        .profile-content {
            flex-direction: column;
        }
        
        .profile-sidebar {
            width: 100%;
        }
        
        .profile-info {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Profile Section -->
<section class="profile-container">
    <div class="container">
        <div class="profile-header">
            <div class="container">
                <h1>My Profile</h1>
                <p>Welcome back, {{ user.username }}!</p>
            </div>
        </div>
        
        <div class="profile-content">
            <div class="profile-sidebar">
                <div class="profile-menu">
                    <div class="profile-menu-item active" data-section="account">
                        <i class="fas fa-user"></i> Account Information
                    </div>
                    <div class="profile-menu-item" data-section="orders">
                        <i class="fas fa-shopping-bag"></i> My Orders
                    </div>
                    <div class="profile-menu-item" data-section="wishlist">
                        <i class="fas fa-heart"></i> Wishlist
                    </div>
                    <div class="profile-menu-item" data-section="addresses">
                        <i class="fas fa-map-marker-alt"></i> Addresses
                    </div>
                    <div class="profile-menu-item" data-section="security">
                        <i class="fas fa-shield-alt"></i> Security
                    </div>
                    <div class="profile-menu-item">
                        <a href="{% url 'logout' %}" style="color: inherit; text-decoration: none; display: flex; align-items: center;">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="profile-main">
                <!-- Account Information Section -->
                <div class="profile-section" id="account-section">
                    <h2>Account Information</h2>
                    <div class="profile-info">
                        <div class="info-item">
                            <label>Username</label>
                            <p>{{ user.username }}</p>
                        </div>
                        <div class="info-item">
                            <label>Email</label>
                            <p>{{ user.email }}</p>
                        </div>
                        <div class="info-item">
                            <label>First Name</label>
                            <p>{{ user.first_name|default:"Not provided" }}</p>
                        </div>
                        <div class="info-item">
                            <label>Last Name</label>
                            <p>{{ user.last_name|default:"Not provided" }}</p>
                        </div>
                        <div class="info-item">
                            <label>Date Joined</label>
                            <p>{{ user.date_joined|date:"F j, Y" }}</p>
                        </div>
                        <div class="info-item">
                            <label>Last Login</label>
                            <p>{{ user.last_login|date:"F j, Y" }}</p>
                        </div>
                    </div>
                    <button class="edit-button">Edit Information</button>
                </div>
                
                <!-- Orders Section (Hidden by default) -->
                <div class="profile-section" id="orders-section" style="display: none;">
                    <h2>My Orders</h2>
                    <div class="order-list">
                        <!-- Sample orders - replace with actual orders from database -->
                        <div class="order-item">
                            <div class="order-id">#ORD12345</div>
                            <div class="order-date">May 15, 2025</div>
                            <div class="order-status status-completed">Completed</div>
                            <div class="order-total">₨. 79,999</div>
                            <div class="order-actions">
                                <a href="#">View</a>
                                <a href="#">Track</a>
                            </div>
                        </div>
                        <div class="order-item">
                            <div class="order-id">#ORD12346</div>
                            <div class="order-date">May 10, 2025</div>
                            <div class="order-status status-processing">Processing</div>
                            <div class="order-total">₨. 119,999</div>
                            <div class="order-actions">
                                <a href="#">View</a>
                                <a href="#">Track</a>
                            </div>
                        </div>
                        <div class="order-item">
                            <div class="order-id">#ORD12347</div>
                            <div class="order-date">April 28, 2025</div>
                            <div class="order-status status-cancelled">Cancelled</div>
                            <div class="order-total">₨. 24,999</div>
                            <div class="order-actions">
                                <a href="#">View</a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Wishlist Section (Hidden by default) -->
                <div class="profile-section" id="wishlist-section" style="display: none;">
                    <h2>My Wishlist</h2>
                    <p>You don't have any items in your wishlist yet.</p>
                </div>
                
                <!-- Addresses Section (Hidden by default) -->
                <div class="profile-section" id="addresses-section" style="display: none;">
                    <h2>My Addresses</h2>
                    <p>You haven't added any addresses yet.</p>
                    <button class="edit-button">Add New Address</button>
                </div>
                
                <!-- Security Section (Hidden by default) -->
                <div class="profile-section" id="security-section" style="display: none;">
                    <h2>Security Settings</h2>
                    <div class="profile-info">
                        <div class="info-item">
                            <label>Password</label>
                            <p>••••••••</p>
                        </div>
                    </div>
                    <button class="edit-button">Change Password</button>
                </div>
            </div>
        </div>
    </div>
</section>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Profile menu functionality
        const menuItems = document.querySelectorAll('.profile-menu-item');
        const sections = document.querySelectorAll('.profile-section');
        
        menuItems.forEach(item => {
            if (!item.dataset.section) return; // Skip if no data-section attribute
            
            item.addEventListener('click', function() {
                // Remove active class from all menu items
                menuItems.forEach(mi => mi.classList.remove('active'));
                
                // Add active class to clicked menu item
                this.classList.add('active');
                
                // Hide all sections
                sections.forEach(section => {
                    section.style.display = 'none';
                });
                
                // Show the selected section
                const sectionId = `${this.dataset.section}-section`;
                document.getElementById(sectionId).style.display = 'block';
            });
        });
    });
</script>
{% endblock %}
{% endblock %}
