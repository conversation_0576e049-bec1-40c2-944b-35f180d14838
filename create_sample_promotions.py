import os
import django
import random
from datetime import timedelta
from decimal import Decimal

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'authtry.settings')
django.setup()

from django.utils import timezone
from authtryapp.models import Product, ProductPromotion

def create_sample_promotions():
    """Create sample product promotions for existing products"""

    # Get all products
    products = Product.objects.all()

    if not products.exists():
        print("No products found in the database. Please add some products first.")
        return

    # Clear existing promotions
    ProductPromotion.objects.all().delete()
    print("Cleared existing promotions")

    # Current time
    now = timezone.now()

    # Create promotions for random products
    promotion_count = 0

    for product in products:
        # Randomly decide if this product should have a promotion (70% chance)
        if random.random() < 0.7:
            # Calculate a sale price (10-30% off)
            discount_percentage = Decimal(random.randint(10, 30))
            sale_price = product.price * (Decimal('1.0') - discount_percentage / Decimal('100.0'))
            sale_price = round(sale_price, 2)

            # Randomly assign promotion types
            is_featured = random.random() < 0.3
            is_best_seller = random.random() < 0.3
            is_new_arrival = random.random() < 0.3
            is_top_deal = random.random() < 0.3

            # Ensure at least one type is selected
            if not any([is_featured, is_best_seller, is_new_arrival, is_top_deal]):
                # If none selected, make it featured
                is_featured = True

            # Set random start and end dates
            start_date = now - timedelta(days=random.randint(0, 10))

            # 70% chance to have an end date
            if random.random() < 0.7:
                end_date = now + timedelta(days=random.randint(5, 30))
            else:
                end_date = None

            # Create the promotion
            promotion = ProductPromotion.objects.create(
                product=product,
                sale_price=sale_price,
                discount_percentage=discount_percentage,
                is_featured=is_featured,
                is_best_seller=is_best_seller,
                is_new_arrival=is_new_arrival,
                is_top_deal=is_top_deal,
                start_date=start_date,
                end_date=end_date
            )

            promotion_count += 1
            print(f"Created promotion for {product.name}: {discount_percentage}% off")

    print(f"Created {promotion_count} promotions for {products.count()} products")

if __name__ == '__main__':
    print("Creating sample product promotions...")
    create_sample_promotions()
    print("Done!")
