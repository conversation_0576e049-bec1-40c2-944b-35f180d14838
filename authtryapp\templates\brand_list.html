{% extends 'base_new.html' %}

{% block title %}Shop by Brand{% endblock %}

{% block extra_css %}
<style>
    /* Brand List Page - Alphabetical Style */
    .brand-container {
        padding: 40px 0;
    }

    .brand-header {
        margin-bottom: 30px;
        text-align: center;
    }

    .brand-title {
        font-size: 32px;
        font-weight: 700;
        margin-bottom: 15px;
        color: #333;
    }

    .brand-description {
        color: #666;
        max-width: 800px;
        margin: 0 auto 30px;
        line-height: 1.6;
    }

    /* Alphabet Index Layout */
    .alphabet-index {
        display: flex;
        justify-content: center;
        margin-bottom: 40px;
        border-bottom: 1px solid #eee;
        padding-bottom: 15px;
    }

    .alphabet-letter {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 18px;
        color: #333;
        cursor: pointer;
        transition: all 0.2s;
        position: relative;
    }

    .alphabet-letter:hover {
        color: #0070e0;
    }

    .alphabet-letter.active {
        color: #0070e0;
        font-weight: 700;
    }

    .alphabet-letter.active:after {
        content: "";
        position: absolute;
        bottom: -16px;
        left: 50%;
        transform: translateX(-50%);
        width: 30px;
        height: 3px;
        background-color: #0070e0;
    }

    /* Brand List Layout */
    .alphabet-section {
        margin-bottom: 50px;
    }

    .section-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
    }

    .section-letter {
        font-size: 28px;
        font-weight: 700;
        color: #0070e0;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f0f7ff;
        border-radius: 8px;
        margin-right: 15px;
    }

    .section-divider {
        flex: 1;
        height: 1px;
        background-color: #eee;
    }

    /* Brand List Grid */
    .brand-list {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
    }

    .brand-item {
        flex: 0 0 calc(25% - 15px);
        padding: 15px;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        transition: all 0.3s;
        text-decoration: none;
        color: #333;
        display: flex;
        align-items: center;
    }

    .brand-item:hover {
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transform: translateY(-3px);
    }

    .brand-logo {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 8px;
    }

    .brand-logo img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }

    .brand-info {
        flex: 1;
    }

    .brand-name {
        font-weight: 600;
        margin-bottom: 5px;
        font-size: 16px;
    }

    .brand-count {
        font-size: 13px;
        color: #666;
    }

    /* Empty State */
    .no-brands {
        text-align: center;
        padding: 50px 20px;
        background-color: #f8f9fa;
        border-radius: 8px;
        margin-top: 30px;
    }

    .no-brands h3 {
        font-size: 20px;
        margin-bottom: 10px;
        color: #333;
    }

    .no-brands p {
        color: #666;
        margin-bottom: 20px;
    }

    /* Responsive */
    @media (max-width: 992px) {
        .brand-item {
            flex: 0 0 calc(33.333% - 15px);
        }
    }

    @media (max-width: 768px) {
        .brand-item {
            flex: 0 0 calc(50% - 15px);
        }

        .alphabet-letter {
            width: 30px;
            height: 30px;
            font-size: 16px;
        }
    }

    @media (max-width: 576px) {
        .brand-item {
            flex: 0 0 100%;
        }

        .alphabet-index {
            flex-wrap: wrap;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="brand-container">
    <div class="container">
        <!-- Brand Header -->
        <div class="brand-header">
            <h1 class="brand-title">Shop by Brand</h1>
            <p class="brand-description">
                Discover premium products from the world's leading tech brands. Browse our exclusive collection and find the perfect devices crafted by your favorite manufacturers.
            </p>
        </div>

        <!-- Alphabet Index -->
        <div class="alphabet-index">
            <div class="alphabet-letter" data-letter="#">#</div>
            {% with 'A B C D E F G H I J K L M N O P Q R S T U V W X Y Z' as list_letters %}
                {% for letter in list_letters.split %}
                    <div class="alphabet-letter" data-letter="{{ letter }}">{{ letter }}</div>
                {% endfor %}
            {% endwith %}
        </div>

        <!-- Brand List by Alphabet -->
        {% regroup brands by name|first|upper as brand_list %}

        <!-- Special section for brands starting with numbers or symbols -->
        <div class="alphabet-section" id="section-#">
            <div class="section-header">
                <div class="section-letter">#</div>
                <div class="section-divider"></div>
            </div>
            <div class="brand-list">
                {% for brand in brands %}
                    {% with first_char=brand.name|first|upper %}
                        {% if first_char == "0" or first_char == "1" or first_char == "2" or first_char == "3" or first_char == "4" or first_char == "5" or first_char == "6" or first_char == "7" or first_char == "8" or first_char == "9" %}
                        <a href="{% url 'brand_detail' brand.slug %}" class="brand-item">
                            <div class="brand-logo">
                                {% if brand.logo %}
                                    <img src="{{ brand.logo.url }}" alt="{{ brand.name }}">
                                {% else %}
                                    <img src="https://via.placeholder.com/50x50?text={{ brand.name|first }}" alt="{{ brand.name }}">
                                {% endif %}
                            </div>
                            <div class="brand-info">
                                <div class="brand-name">{{ brand.name }}</div>
                                <div class="brand-count">{{ brand.products.count }} Products</div>
                            </div>
                        </a>
                        {% endif %}
                    {% endwith %}
                {% endfor %}
            </div>
        </div>

        <!-- Alphabetical sections -->
        {% for letter_group in brand_list %}
        <div class="alphabet-section" id="section-{{ letter_group.grouper }}">
            <div class="section-header">
                <div class="section-letter">{{ letter_group.grouper }}</div>
                <div class="section-divider"></div>
            </div>
            <div class="brand-list">
                {% for brand in letter_group.list %}
                <a href="{% url 'brand_detail' brand.slug %}" class="brand-item">
                    <div class="brand-logo">
                        {% if brand.logo %}
                            <img src="{{ brand.logo.url }}" alt="{{ brand.name }}">
                        {% else %}
                            <img src="https://via.placeholder.com/50x50?text={{ brand.name|first }}" alt="{{ brand.name }}">
                        {% endif %}
                    </div>
                    <div class="brand-info">
                        <div class="brand-name">{{ brand.name }}</div>
                        <div class="brand-count">{{ brand.products.count }} Products</div>
                    </div>
                </a>
                {% endfor %}
            </div>
        </div>
        {% empty %}
        <div class="no-brands">
            <h3>No brands found</h3>
            <p>We're currently updating our brand catalog. Please check back later.</p>
            <a href="{% url 'home' %}" class="btn btn-primary">Back to Home</a>
        </div>
        {% endfor %}
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Alphabet index functionality
        const letters = document.querySelectorAll('.alphabet-letter');

        // Collect all available sections
        const availableSections = [];
        document.querySelectorAll('.alphabet-section').forEach(section => {
            const letter = section.id.replace('section-', '');
            availableSections.push(letter);
        });

        // Highlight active letters and dim inactive ones
        letters.forEach(letter => {
            const letterValue = letter.dataset.letter;
            if (availableSections.includes(letterValue)) {
                // This letter has brands
                letter.classList.add('has-brands');
            } else {
                // This letter has no brands
                letter.style.opacity = '0.4';
                letter.style.cursor = 'default';
            }

            // Add click event for scrolling
            letter.addEventListener('click', function() {
                const selectedLetter = this.dataset.letter;
                const section = document.getElementById(`section-${selectedLetter}`);

                // Only proceed if there's a section for this letter
                if (section) {
                    // Remove active class from all letters
                    letters.forEach(l => l.classList.remove('active'));

                    // Add active class to clicked letter
                    this.classList.add('active');

                    // Scroll to the section
                    section.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Activate the first available letter by default
        if (availableSections.length > 0) {
            const firstLetter = document.querySelector(`.alphabet-letter[data-letter="${availableSections[0]}"]`);
            if (firstLetter) {
                firstLetter.classList.add('active');

                // Scroll to the first section after a short delay
                setTimeout(() => {
                    const firstSection = document.getElementById(`section-${availableSections[0]}`);
                    if (firstSection) {
                        firstSection.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                }, 300);
            }
        }
    });
</script>
{% endblock %}
{% endblock %}