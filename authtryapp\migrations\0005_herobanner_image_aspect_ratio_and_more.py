# Generated by Django 5.0.6 on 2025-05-25 04:35

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('authtryapp', '0004_herobanner_siteconfiguration_featuredproduct'),
    ]

    operations = [
        migrations.AddField(
            model_name='herobanner',
            name='image_aspect_ratio',
            field=models.FloatField(blank=True, help_text='Auto-calculated aspect ratio', null=True),
        ),
        migrations.AddField(
            model_name='herobanner',
            name='image_display_mode',
            field=models.CharField(choices=[('contain', 'Fit in Frame (Show full image)'), ('cover', 'Fill Carousel (Crop if needed)'), ('auto', 'Auto-detect (Smart sizing)')], default='auto', help_text='How to display the image in carousel', max_length=10),
        ),
        migrations.AddField(
            model_name='herobanner',
            name='image_height',
            field=models.IntegerField(blank=True, help_text='Auto-detected image height', null=True),
        ),
        migrations.AddField(
            model_name='herobanner',
            name='image_width',
            field=models.IntegerField(blank=True, help_text='Auto-detected image width', null=True),
        ),
        migrations.AlterField(
            model_name='herobanner',
            name='image',
            field=models.ImageField(help_text='Banner image (auto-detects optimal sizing)', upload_to='hero_banners/'),
        ),
    ]
