{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>{% if product %}Edit{% else %}Add{% endif %} Product - Admin Dashboard - iTechStore</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        /* Admin Dashboard Styles */
        .admin-container {
            display: flex;
            min-height: calc(100vh - 150px);
        }

        .admin-sidebar {
            width: 250px;
            background-color: #1a1a1a;
            color: white;
            padding: 20px 0;
            flex-shrink: 0;
        }

        .admin-logo {
            padding: 0 20px 20px;
            border-bottom: 1px solid #333;
            margin-bottom: 20px;
        }

        .admin-logo h2 {
            font-size: 20px;
            display: flex;
            align-items: center;
        }

        .admin-logo i {
            margin-right: 10px;
            color: #4dabf7;
        }

        .admin-menu {
            list-style: none;
        }

        .admin-menu li {
            margin-bottom: 5px;
        }

        .admin-menu a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: #aaa;
            transition: all 0.3s;
        }

        .admin-menu a:hover, .admin-menu a.active {
            background-color: #333;
            color: white;
        }

        .admin-menu i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .admin-content {
            flex: 1;
            padding: 30px;
            background-color: #f5f7fa;
            overflow-y: auto;
        }

        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .admin-title h1 {
            font-size: 24px;
            color: #333;
        }

        .admin-user {
            display: flex;
            align-items: center;
        }

        .admin-user-info {
            margin-right: 15px;
            text-align: right;
        }

        .admin-user-name {
            font-weight: 500;
            color: #333;
        }

        .admin-user-role {
            font-size: 12px;
            color: #777;
        }

        .admin-user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #4dabf7;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
        }

        .admin-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 30px;
        }

        .admin-card-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-card-title {
            font-size: 18px;
            color: #333;
            font-weight: 500;
        }

        .admin-card-body {
            padding: 20px;
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            border-color: #4dabf7;
            outline: none;
        }

        textarea.form-control {
            min-height: 150px;
            resize: vertical;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-col {
            flex: 1;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 20px;
            border-radius: 5px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
        }

        .btn-primary {
            background-color: #4dabf7;
            color: white;
        }

        .btn-primary:hover {
            background-color: #0c8599;
        }

        .btn-secondary {
            background-color: #f5f5f5;
            color: #333;
        }

        .btn-secondary:hover {
            background-color: #e0e0e0;
        }

        .image-preview {
            width: 100%;
            height: 200px;
            border: 1px dashed #ddd;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            overflow: hidden;
        }

        .image-preview img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .image-preview-placeholder {
            color: #777;
            font-size: 14px;
        }

        .help-text {
            font-size: 12px;
            color: #777;
            margin-top: 5px;
        }

        .error-text {
            font-size: 12px;
            color: #f44336;
            margin-top: 5px;
        }

        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            color: white;
        }

        .alert-danger {
            background-color: #f44336;
        }

        .alert-success {
            background-color: #4caf50;
        }

        @media (max-width: 992px) {
            .admin-container {
                flex-direction: column;
            }

            .admin-sidebar {
                width: 100%;
                padding: 10px 0;
            }

            .admin-logo {
                padding: 0 15px 15px;
                margin-bottom: 10px;
            }

            .admin-menu a {
                padding: 10px 15px;
            }

            .form-row {
                flex-direction: column;
                gap: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Top Navigation Bar -->
    <div class="top-nav">
        <div class="container">
            <div class="top-nav-left">
                <span>Admin Panel</span>
            </div>
            <div class="top-nav-right">
                <span>Welcome, {{ request.user.username }}</span>
                <a href="{% url 'admin_logout' %}" class="sign-in">Logout</a>
            </div>
        </div>
    </div>

    <!-- Admin Container -->
    <div class="admin-container">
        <!-- Admin Sidebar -->
        <div class="admin-sidebar">
            <div class="admin-logo">
                <h2><i class="fas fa-tachometer-alt"></i> Admin Panel</h2>
            </div>
            <ul class="admin-menu">
                <li><a href="{% url 'admin_dashboard' %}"><i class="fas fa-home"></i> Dashboard</a></li>
                <li><a href="{% url 'admin_products' %}" class="active"><i class="fas fa-box"></i> Products</a></li>
                <li><a href="{% url 'admin_users' %}"><i class="fas fa-users"></i> Users</a></li>
                <li><a href="{% url 'admin_orders' %}"><i class="fas fa-shopping-cart"></i> Orders</a></li>
                <li><a href="{% url 'admin_categories' %}"><i class="fas fa-tags"></i> Categories</a></li>
                <li><a href="{% url 'admin_settings' %}"><i class="fas fa-cog"></i> Settings</a></li>
                <li><a href="/django-admin/" target="_blank"><i class="fas fa-tools"></i> Django Admin</a></li>
                <li><a href="{% url 'home' %}"><i class="fas fa-store"></i> View Store</a></li>
                <li><a href="{% url 'admin_logout' %}"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
            </ul>
        </div>

        <!-- Admin Content -->
        <div class="admin-content">
            <div class="admin-header">
                <div class="admin-title">
                    <h1>{% if product %}Edit{% else %}Add{% endif %} Product</h1>
                </div>
                <div class="admin-user">
                    <div class="admin-user-info">
                        <div class="admin-user-name">{{ request.user.username }}</div>
                        <div class="admin-user-role">Administrator</div>
                    </div>
                    <div class="admin-user-avatar">
                        {{ request.user.username|first|upper }}
                    </div>
                </div>
            </div>

            <!-- Product Form -->
            <div class="admin-card">
                <div class="admin-card-header">
                    <div class="admin-card-title">Product Information</div>
                </div>
                <div class="admin-card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert {% if message.tags == 'error' %}alert-danger{% else %}alert-success{% endif %}">
                                {{ message }}
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post" enctype="multipart/form-data" action="{% if product %}{% url 'admin_product_edit' product.id %}{% else %}{% url 'admin_product_add' %}{% endif %}">
                        {% csrf_token %}

                        <div class="form-group">
                            <label for="name">Product Name</label>
                            <input type="text" id="name" name="name" class="form-control" value="{{ product.name|default:'' }}" required>
                            {% if form.name.errors %}
                                <div class="error-text">{{ form.name.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label for="price">Price ($)</label>
                                    <input type="number" id="price" name="price" step="0.01" class="form-control" value="{{ product.price|default:'' }}" required>
                                    {% if form.price.errors %}
                                        <div class="error-text">{{ form.price.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label for="stock_status">Stock Status</label>
                                    <select id="stock_status" name="stock_status" class="form-control" required>
                                        <option value="available" {% if product.stock_status == 'available' %}selected{% endif %}>Available</option>
                                        <option value="preorder" {% if product.stock_status == 'preorder' %}selected{% endif %}>Pre-order</option>
                                        <option value="notavailable" {% if product.stock_status == 'notavailable' %}selected{% endif %}>Not Available</option>
                                    </select>
                                    {% if form.stock_status.errors %}
                                        <div class="error-text">{{ form.stock_status.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea id="description" name="description" class="form-control">{{ product.description|default:'' }}</textarea>
                            {% if form.description.errors %}
                                <div class="error-text">{{ form.description.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label for="image">Product Image</label>
                            <div class="image-preview">
                                {% if product.image %}
                                    <img src="{{ product.image.url }}" alt="{{ product.name }}">
                                {% else %}
                                    <div class="image-preview-placeholder">No image selected</div>
                                {% endif %}
                            </div>
                            <input type="file" id="image" name="image" class="form-control">
                            <div class="help-text">Recommended size: 800x600 pixels. Max file size: 2MB.</div>
                            {% if form.image.errors %}
                                <div class="error-text">{{ form.image.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="form-actions">
                            <a href="{% url 'admin_products' %}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">{% if product %}Update{% else %}Create{% endif %} Product</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="{% static 'js/script.js' %}"></script>
    <script>
        // Image preview functionality
        document.getElementById('image').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(event) {
                    const imagePreview = document.querySelector('.image-preview');
                    imagePreview.innerHTML = `<img src="${event.target.result}" alt="Preview">`;
                };
                reader.readAsDataURL(file);
            }
        });
    </script>
</body>
</html>
