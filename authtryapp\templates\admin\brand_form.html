{% extends 'admin/base_admin.html' %}

{% block title %}
    {% if is_add %}Add New Brand{% else %}Edit Brand{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    :root {
        --primary-color: #2563eb;
        --primary-hover: #1d4ed8;
        --secondary-color: #f0f9ff;
        --accent-color: #0284c7;
        --text-dark: #1e293b;
        --text-light: #64748b;
        --bg-light: #f8fafc;
        --bg-white: #ffffff;
        --shadow-sm: 0 1px 2px rgba(0,0,0,0.05);
        --shadow-md: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06);
        --shadow-lg: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05);
        --radius-sm: 0.375rem;
        --radius-md: 0.5rem;
        --radius-lg: 0.75rem;
    }

    .preview-container {
        margin-top: 20px;
        text-align: center;
        background-color: var(--bg-light);
        padding: 20px;
        border-radius: var(--radius-lg);
        border: 1px dashed #cbd5e1;
    }
    
    .logo-preview {
        max-width: 220px;
        max-height: 120px;
        object-fit: contain;
        border: 1px solid #e2e8f0;
        border-radius: var(--radius-md);
        padding: 15px;
        background-color: var(--bg-white);
        box-shadow: var(--shadow-sm);
        transition: transform 0.3s ease;
    }
    
    .logo-preview:hover {
        transform: scale(1.05);
        box-shadow: var(--shadow-md);
    }
    
    .form-group {
        margin-bottom: 1.75rem;
    }
    
    .form-label {
        font-weight: 600;
        color: var(--text-dark);
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
    }
    
    .form-control {
        border-radius: var(--radius-md);
        border: 1px solid #e2e8f0;
        padding: 0.65rem 1rem;
        font-size: 0.95rem;
        transition: all 0.2s;
        background-color: var(--bg-light);
        color: var(--text-dark);
    }
    
    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
        background-color: var(--bg-white);
    }
    
    .form-text {
        color: var(--text-light);
        font-size: 0.85rem;
        margin-top: 0.5rem;
    }
    
    .required-field::after {
        content: " *";
        color: #ef4444;
    }
    
    .card {
        border: none;
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-md);
        overflow: hidden;
    }
    
    .card-header {
        background: linear-gradient(to right, var(--primary-color), #3b82f6);
        color: white;
        font-weight: 600;
        padding: 1rem 1.25rem;
        border: none;
    }
    
    .btn {
        padding: 0.6rem 1.2rem;
        border-radius: var(--radius-md);
        font-weight: 500;
        transition: all 0.2s;
    }
    
    .btn-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }
    
    .btn-primary:hover {
        background-color: var(--primary-hover);
        border-color: var(--primary-hover);
        box-shadow: 0 4px 6px rgba(37, 99, 235, 0.1);
        transform: translateY(-1px);
    }
    
    .btn-secondary {
        background-color: #94a3b8;
        border-color: #94a3b8;
    }
    
    .btn-secondary:hover {
        background-color: #64748b;
        border-color: #64748b;
        box-shadow: 0 4px 6px rgba(100, 116, 139, 0.1);
    }
    
    .invalid-feedback {
        color: #ef4444;
        font-size: 0.85rem;
        margin-top: 0.5rem;
    }
    
    .breadcrumb {
        background-color: transparent;
        padding: 0;
        margin-bottom: 1.5rem;
    }
    
    .breadcrumb-item a {
        color: var(--primary-color);
        text-decoration: none;
        transition: color 0.2s;
    }
    
    .breadcrumb-item a:hover {
        color: var(--primary-hover);
        text-decoration: underline;
    }
    
    .breadcrumb-item.active {
        color: var(--text-light);
    }
    
    .page-heading {
        color: var(--text-dark);
        font-weight: 700;
        font-size: 1.75rem;
        margin-bottom: 0.75rem;
    }
    
    /* File input styling */
    input[type="file"] {
        position: relative;
    }
    
    input[type="file"]::file-selector-button {
        background-color: #e2e8f0;
        color: var(--text-dark);
        border: 0;
        border-right: 1px solid #cbd5e1;
        padding: 0.6rem 1rem;
        margin-right: 1rem;
        border-radius: var(--radius-sm);
        transition: all 0.2s;
    }
    
    input[type="file"]::file-selector-button:hover {
        background-color: #cbd5e1;
    }
    
    /* Card effect */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .card {
        animation: fadeIn 0.5s ease-out;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="page-heading">
        {% if is_add %}Add New Brand{% else %}Edit Brand: {{ brand.name }}{% endif %}
    </h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{% url 'admin_dashboard' %}">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="{% url 'admin_brands' %}">Brands</a></li>
        <li class="breadcrumb-item active">
            {% if is_add %}Add Brand{% else %}Edit Brand{% endif %}
        </li>
    </ol>
    
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-{% if is_add %}plus{% else %}edit{% endif %} me-1"></i>
            {% if is_add %}Create a New Brand{% else %}Update Brand Information{% endif %}
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data" novalidate>
                {% csrf_token %}
                
                <div class="row">
                    <div class="col-md-8">
                        <!-- Brand Name -->
                        <div class="form-group">
                            <label for="{{ form.name.id_for_label }}" class="form-label required-field">Brand Name</label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.name.errors }}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">Enter the full brand name (e.g., "Apple", "Samsung").</small>
                        </div>
                        
                        <!-- Brand Description -->
                        <div class="form-group">
                            <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.description.errors }}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">Provide a short description of the brand to help customers recognize it.</small>
                        </div>
                        
                        <!-- Brand Logo -->
                        <div class="form-group">
                            <label for="{{ form.logo.id_for_label }}" class="form-label">Brand Logo</label>
                            {{ form.logo }}
                            {% if form.logo.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.logo.errors }}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">Upload a logo for the brand. Recommended size: 200x100px, PNG or JPG format with transparent background if possible.</small>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="preview-container">
                            {% if is_edit and brand.logo %}
                                <p class="text-muted mb-2">Current Logo:</p>
                                <img src="{{ brand.logo.url }}" alt="{{ brand.name }}" class="logo-preview mb-3">
                            {% else %}
                                <p class="text-muted mb-2">Logo Preview:</p>
                                <div id="logo-preview-container">
                                    <img src="https://via.placeholder.com/200x100?text=No+Logo" alt="Logo Preview" class="logo-preview">
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="mt-4 d-flex justify-content-between">
                    <a href="{% url 'admin_brands' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Back to Brands
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-{% if is_add %}plus{% else %}save{% endif %} me-1"></i>
                        {% if is_add %}Create Brand{% else %}Update Brand{% endif %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Logo preview functionality
    document.getElementById('{{ form.logo.id_for_label }}').addEventListener('change', function(e) {
        const file = e.target.files[0];
        const previewContainer = document.getElementById('logo-preview-container');
        
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewContainer.innerHTML = `<img src="${e.target.result}" alt="Logo Preview" class="logo-preview">`;
            };
            reader.readAsDataURL(file);
        }
    });
</script>
{% endblock %} 