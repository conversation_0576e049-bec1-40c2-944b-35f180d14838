{% extends "base.html" %}

{% block title %}My Profile{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-lg">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">My Profile</h3>
                </div>
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} text-center">{{ message }}</div>
                        {% endfor %}
                    {% endif %}

                    <div class="row mb-4">
                        <div class="col-md-4 text-center">
                            <div class="profile-img-container mb-3">
                                <img src="https://via.placeholder.com/150" alt="Profile Image" class="img-fluid rounded-circle">
                            </div>
                            <button class="btn btn-sm btn-outline-primary">Change Photo</button>
                        </div>
                        <div class="col-md-8">
                            <h4>{{ user.get_full_name }}</h4>
                            <p class="text-muted">@{{ user.username }}</p>
                            <p><i class="fas fa-envelope me-2"></i> {{ user.email }}</p>
                            <p><i class="fas fa-calendar me-2"></i> Joined: {{ user.date_joined|date:"F j, Y" }}</p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <h5 class="border-bottom pb-2">Account Information</h5>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <p><strong>First Name:</strong> {{ user.first_name }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Last Name:</strong> {{ user.last_name }}</p>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <p><strong>Username:</strong> {{ user.username }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Email:</strong> {{ user.email }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end mt-4">
                        <a href="#" class="btn btn-outline-primary me-2">Edit Profile</a>
                        <a href="{% url 'logout' %}" class="btn btn-outline-danger">Logout</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
