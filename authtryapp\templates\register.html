<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Register</title>
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: "Segoe UI", sans-serif;
      background: linear-gradient(to bottom, #0a0a23, #2929a3);
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      padding: 20px 0;
    }

    .register-card {
      background-color: #14143d;
      padding: 40px 30px;
      border-radius: 16px;
      width: 100%;
      max-width: 500px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      text-align: center;
      position: relative;
    }

    .register-icon {
      width: 80px;
      height: 80px;
      background: radial-gradient(circle, #2b2bc9 30%, #14143d 90%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 20px;
    }

    .register-icon img {
      width: 40px;
      height: 40px;
    }

    .register-card h2 {
      margin-bottom: 30px;
      font-size: 24px;
    }

    .form-group {
      text-align: left;
      margin-bottom: 20px;
    }

    .form-group label {
      display: block;
      font-size: 14px;
      margin-bottom: 6px;
    }

    .form-group input {
      width: 100%;
      padding: 12px;
      border-radius: 8px;
      border: none;
      font-size: 14px;
      background-color: #f1f1f1;
      color: #333;
    }

    .help-text {
      font-size: 12px;
      color: #aaa;
      margin-top: 5px;
    }

    .error-text {
      font-size: 12px;
      color: #ff5757;
      margin-top: 5px;
    }

    .register-btn {
      margin-top: 10px;
      width: 100%;
      padding: 12px;
      background: linear-gradient(to right, #8e2de2, #4ac7ff);
      border: none;
      border-radius: 8px;
      color: white;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
    }

    .register-btn:hover {
      opacity: 0.9;
    }

    .row {
      display: flex;
      gap: 15px;
    }

    .col {
      flex: 1;
    }

    .alert {
      padding: 10px;
      border-radius: 8px;
      margin-bottom: 20px;
      text-align: left;
    }

    .alert-danger {
      background-color: #ff5757;
    }

    .alert-success {
      background-color: #4caf50;
    }
  </style>
</head>
<body>
  <div class="register-card">
    <div class="register-icon">
      <img src="data:image/svg+xml;base64,PHN2ZyBmaWxsPSIjZmZmIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgdmlld0JveD0iMCAwIDUxMiA1MTIiPjxwYXRoIGQ9Ik0yNTYgMGMtMTQxLjQgMC0yNTYgMTE0LjYtMjU2IDI1NnMxMTQuNiAyNTYgMjU2IDI1NiAyNTYtMTE0LjYgMjU2LTI1Ni0xMTQuNi0yNTYtMjU2LTI1NnptMCA0OThjLTMyLjggMC02NC4zLTcuNS05Mi4zLTIwLjcgNS43OC0yOC40IDE3LjUtNTMuOCAzNS42LTc1LjQgMTkuNS0yMi45IDQ1LjUtNDEuMiA3NS40LTUyLjggMjYuOC0xMC4yIDU2LjItMTUuOCA4Ni4zLTE1LjggMzAuMSAwIDU5LjUgNS42IDg2LjMgMTUuOCAyOS45IDExLjYgNTUuOSAzMC4xIDc1LjQgNTIuOCAxOC4xIDIxLjYgMjkuOCA0NyAzNS42IDc1LjQtMjguMSAxMy4yLTU5LjUgMjAuNy05Mi4zIDIwLjctODQuNSAwLTE1Mi0xMy4zLTIwMy4xLTUwLjR6bTAgLTQzMmM1Ni40IDAgMTAyLjMgNDUuOSA2Mi4zIDQ1LjkgMCAzNS4xLTMyLjEgNjQtNzIgNjRzLTcyLTI4LjktNzItNjRjLTQwLjIgMC02Mi4zLTQ1LjkgNjIuMy00NS45eiIvPjwvc3ZnPg==" alt="User Icon" />
    </div>
    <h2>Create an Account</h2>
    
    {% if messages %}
      {% for message in messages %}
        <div class="alert {% if message.tags == 'error' %}alert-danger{% else %}alert-success{% endif %}">
          {{ message }}
        </div>
      {% endfor %}
    {% endif %}

    <form method="post" action="{% url 'register' %}">
      {% csrf_token %}
      
      <div class="form-group">
        <label>Username</label>
        <input type="text" name="username" placeholder="Choose a username" required />
        {% if form.username.errors %}
          <div class="error-text">{{ form.username.errors.0 }}</div>
        {% endif %}
        <div class="help-text">{{ form.username.help_text }}</div>
      </div>
      
      <div class="row">
        <div class="col">
          <div class="form-group">
            <label>First Name</label>
            <input type="text" name="first_name" placeholder="Your first name" required />
            {% if form.first_name.errors %}
              <div class="error-text">{{ form.first_name.errors.0 }}</div>
            {% endif %}
          </div>
        </div>
        <div class="col">
          <div class="form-group">
            <label>Last Name</label>
            <input type="text" name="last_name" placeholder="Your last name" required />
            {% if form.last_name.errors %}
              <div class="error-text">{{ form.last_name.errors.0 }}</div>
            {% endif %}
          </div>
        </div>
      </div>
      
      <div class="form-group">
        <label>Email</label>
        <input type="email" name="email" placeholder="Your email address" required />
        {% if form.email.errors %}
          <div class="error-text">{{ form.email.errors.0 }}</div>
        {% endif %}
      </div>
      
      <div class="form-group">
        <label>Password</label>
        <input type="password" name="password1" placeholder="Create a password" required />
        {% if form.password1.errors %}
          <div class="error-text">{{ form.password1.errors.0 }}</div>
        {% endif %}
        <div class="help-text">{{ form.password1.help_text }}</div>
      </div>
      
      <div class="form-group">
        <label>Confirm Password</label>
        <input type="password" name="password2" placeholder="Confirm your password" required />
        {% if form.password2.errors %}
          <div class="error-text">{{ form.password2.errors.0 }}</div>
        {% endif %}
      </div>
      
      <button type="submit" class="register-btn">Create Account</button>
      
      <div style="margin-top: 20px; font-size: 14px;">
        <p>Already have an account? <a href="{% url 'login' %}" style="color: #4ac7ff; text-decoration: none;">Login here</a></p>
      </div>
    </form>
  </div>
</body>
</html>
