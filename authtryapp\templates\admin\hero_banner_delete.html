{% extends 'admin/base_admin.html' %}

{% block title %}Delete Hero Banner{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mt-4 mb-4">
        <h1>🗑️ Delete Hero Banner</h1>
        <a href="{% url 'admin_hero_banners' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Banners
        </a>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Warning Card -->
            <div class="card border-danger mb-4">
                <div class="card-header bg-danger text-white">
                    <i class="fas fa-exclamation-triangle"></i> Confirm Deletion
                </div>
                <div class="card-body">
                    <h5 class="text-danger">Are you sure you want to delete this hero banner?</h5>
                    <p class="text-muted">This action cannot be undone. The banner will be permanently removed from your website.</p>
                </div>
            </div>

            <!-- Banner Preview -->
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-eye"></i> Banner to be Deleted
                </div>
                <div class="card-body p-0">
                    <div class="banner-preview" style="height: 250px; background: linear-gradient(135deg, {{ banner.background_gradient_start }} 0%, {{ banner.background_gradient_end }} 100%);">
                        <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center text-white p-4">
                            <div class="text-center">
                                {% if banner.brand_text %}
                                <small class="d-block opacity-75 mb-2">{{ banner.brand_text }}</small>
                                {% endif %}
                                <h4 class="mb-3">{{ banner.title }}</h4>
                                <p class="mb-3">{{ banner.subtitle }}</p>
                                {% if banner.image %}
                                <img src="{{ banner.image.url }}" alt="{{ banner.image_alt }}" class="img-fluid mt-2" style="max-height: 80px; max-width: 120px; object-fit: contain;">
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Banner Details -->
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-info-circle"></i> Banner Details
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Basic Information</h6>
                            <ul class="list-unstyled">
                                <li><strong>Title:</strong> {{ banner.title }}</li>
                                <li><strong>Subtitle:</strong> {{ banner.subtitle|truncatechars:50 }}</li>
                                {% if banner.brand_text %}
                                <li><strong>Brand:</strong> {{ banner.brand_text }}</li>
                                {% endif %}
                                <li><strong>Status:</strong> 
                                    <span class="badge {% if banner.is_active %}bg-success{% else %}bg-secondary{% endif %}">
                                        {% if banner.is_active %}Active{% else %}Inactive{% endif %}
                                    </span>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Technical Details</h6>
                            <ul class="list-unstyled">
                                <li><strong>Display Order:</strong> {{ banner.display_order }}</li>
                                <li><strong>Created:</strong> {{ banner.created_at|date:"M d, Y H:i" }}</li>
                                <li><strong>Updated:</strong> {{ banner.updated_at|date:"M d, Y H:i" }}</li>
                                <li><strong>Banner ID:</strong> {{ banner.id }}</li>
                            </ul>
                        </div>
                    </div>
                    
                    {% if banner.primary_button_text or banner.secondary_button_text %}
                    <div class="mt-3">
                        <h6>Button Configuration</h6>
                        <div class="d-flex gap-2">
                            {% if banner.primary_button_text %}
                            <span class="badge bg-primary">{{ banner.primary_button_text }}</span>
                            {% endif %}
                            {% if banner.secondary_button_text %}
                            <span class="badge bg-outline-primary">{{ banner.secondary_button_text }}</span>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Confirmation Form -->
            <div class="card">
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="d-flex justify-content-between align-items-center">
                            <a href="{% url 'admin_hero_banners' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <div>
                                <a href="{% url 'admin_hero_banner_edit' banner.id %}" class="btn btn-outline-primary me-2">
                                    <i class="fas fa-edit"></i> Edit Instead
                                </a>
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash"></i> Yes, Delete Banner
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.banner-preview {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
}

.card-border-danger {
    border-color: #dc3545;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
    transform: translateY(-1px);
}

.badge {
    font-size: 0.75rem;
}

.list-unstyled li {
    margin-bottom: 0.5rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const deleteForm = document.querySelector('form');
    const deleteBtn = deleteForm.querySelector('button[type="submit"]');
    
    deleteForm.addEventListener('submit', function(e) {
        // Double confirmation for safety
        if (!confirm('Are you absolutely sure you want to delete this banner? This action cannot be undone.')) {
            e.preventDefault();
            return false;
        }
        
        deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Deleting...';
        deleteBtn.disabled = true;
    });
});
</script>
{% endblock %}
