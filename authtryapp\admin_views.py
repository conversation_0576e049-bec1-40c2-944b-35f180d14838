from django.contrib.auth.decorators import login_required, user_passes_test
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.contrib.auth.models import User
from django.core.paginator import Paginator
from django.http import HttpResponseRedirect
from django.urls import reverse
from django.contrib.auth import authenticate, login as auth_login, logout as auth_logout
from django.contrib.auth.forms import AuthenticationForm
from .models import Product, Category, Brand, SectionHeading, ProductPromotion, HeroBanner, FeaturedProduct, SiteConfiguration
from .forms import ProductForm, CategoryForm, BrandForm
from sales_management.models import SaleCategory, ProductSaleAssignment, SaleCampaign, SaleUsageLog, SalePerformanceMetrics
from django.db.models import Q, Count
from decimal import Decimal

def is_admin(user):
    return user.is_staff or user.is_superuser

def admin_login(request):
    """Admin login view"""
    # If user is already logged in and is an admin, redirect to dashboard
    if request.user.is_authenticated and is_admin(request.user):
        return redirect('admin_dashboard')

    if request.method == 'POST':
        form = AuthenticationForm(request, data=request.POST)
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(username=username, password=password)

            # Check if user exists and is an admin
            if user is not None and is_admin(user):
                auth_login(request, user)
                messages.success(request, f"Welcome back, {username}!")

                # Remember the user if remember checkbox is checked
                if not request.POST.get('remember', None):
                    request.session.set_expiry(0)

                return redirect('admin_dashboard')
            else:
                # If user is not an admin or doesn't exist
                if user is not None and not is_admin(user):
                    messages.error(request, "You don't have permission to access the admin panel.")
                else:
                    messages.error(request, "Invalid username or password.")
        else:
            messages.error(request, "Invalid username or password.")
    else:
        form = AuthenticationForm()

    return render(request, 'admin/login.html', {'form': form})

def admin_logout(request):
    """Admin logout view"""
    auth_logout(request)
    messages.success(request, "You have been successfully logged out.")
    return redirect('/admin/')

@login_required
@user_passes_test(is_admin)
def admin_dashboard(request):
    # Get counts for dashboard stats
    products_count = Product.objects.count()
    users_count = User.objects.count()

    # Get hero banner and site config counts
    try:
        hero_banners_count = HeroBanner.objects.filter(is_active=True).count()
        site_config = SiteConfiguration.objects.first()
    except:
        hero_banners_count = 0
        site_config = None

    # Get categories for dashboard
    categories = Category.objects.all()

    # Get recent products and users for dashboard tables
    recent_products = Product.objects.all().order_by('-created_at')[:5]
    recent_users = User.objects.all().order_by('-date_joined')[:5]

    # Get recent hero banners
    try:
        recent_banners = HeroBanner.objects.all().order_by('-created_at')[:3]
    except:
        recent_banners = []

    context = {
        'products_count': products_count,
        'users_count': users_count,
        'hero_banners_count': hero_banners_count,
        'orders_count': 0,  # Placeholder for future order functionality
        'total_revenue': 0,  # Placeholder for future revenue calculation
        'recent_products': recent_products,
        'recent_users': recent_users,
        'recent_banners': recent_banners,
        'categories': categories,
        'site_config': site_config,
    }

    return render(request, 'admin/dashboard.html', context)

@login_required
@user_passes_test(is_admin)
def admin_products(request):
    # Get query parameters for filtering
    category_id = request.GET.get('category')
    search_query = request.GET.get('search')

    # Start with all products
    products_list = Product.objects.all()

    # Apply category filter if provided
    if category_id:
        try:
            category = Category.objects.get(id=category_id)
            products_list = products_list.filter(category=category)
        except (Category.DoesNotExist, ValueError):
            pass

    # Apply search filter if provided
    if search_query:
        products_list = products_list.filter(name__icontains=search_query)

    # Get all categories for the filter dropdown
    categories = Category.objects.all()

    # Order products by creation date
    products_list = products_list.order_by('-created_at')

    # Pagination
    paginator = Paginator(products_list, 10)  # Show 10 products per page
    page = request.GET.get('page')
    products = paginator.get_page(page)

    context = {
        'products': products,
        'categories': categories,
        'current_category_id': category_id,
        'search_query': search_query,
    }

    return render(request, 'admin/products.html', context)

@login_required
@user_passes_test(is_admin)
def admin_products_by_category(request, category_id):
    # Get the category or 404
    category = get_object_or_404(Category, id=category_id)

    # Get products filtered by this category
    products_list = Product.objects.filter(category=category).order_by('-created_at')

    # Pagination
    paginator = Paginator(products_list, 10)  # Show 10 products per page
    page = request.GET.get('page')
    products = paginator.get_page(page)

    # Get all categories for filtering
    categories = Category.objects.all()

    context = {
        'products': products,
        'categories': categories,
        'current_category': category,
        'current_category_id': str(category_id),
    }

    return render(request, 'admin/products_by_category.html', context)

@login_required
@user_passes_test(is_admin)
def admin_product_add(request):
    # Get category_id from query parameter if exists
    initial_category_id = request.GET.get('category')
    initial_data = {}

    if initial_category_id:
        try:
            category = Category.objects.get(id=initial_category_id)
            initial_data['category'] = category
        except (Category.DoesNotExist, ValueError):
            pass

    if request.method == 'POST':
        form = ProductForm(request.POST, request.FILES)
        if form.is_valid():
            product = form.save()
            messages.success(request, f"Product '{product.name}' was created successfully!")

            # If we came from a filtered category view, return there
            if 'category_id' in request.POST and request.POST['category_id']:
                return redirect('admin_products_by_category', category_id=request.POST['category_id'])
            return redirect('admin_products')
    else:
        form = ProductForm(initial=initial_data)

    # Get all categories for context
    categories = Category.objects.all()

    context = {
        'form': form,
        'categories': categories,
        'category_id': initial_category_id,
    }

    return render(request, 'admin/product_form.html', context)

@login_required
@user_passes_test(is_admin)
def admin_product_edit(request, product_id):
    """Edit an existing product"""
    product = get_object_or_404(Product, id=product_id)
    categories = Category.objects.all()
    referring_category = request.GET.get('referring_category')

    if request.method == 'POST':
        form = ProductForm(request.POST, request.FILES, instance=product)
        if form.is_valid():
            product = form.save()
            messages.success(request, f"Product '{product.name}' was updated successfully!")

            # Check if we should redirect back to a category view
            if 'referring_category' in request.POST and request.POST['referring_category']:
                return redirect('admin_products_by_category', category_id=request.POST['referring_category'])
            elif product.category:
                return redirect('admin_products_by_category', category_id=product.category.id)
            else:
                return redirect('admin_products')
    else:
        form = ProductForm(instance=product)

    return render(request, 'admin/product_form.html', {
        'form': form,
        'product': product,
        'categories': categories,
        'referring_category': referring_category
    })

@login_required
@user_passes_test(is_admin)
def admin_product_delete(request, product_id):
    product = get_object_or_404(Product, id=product_id)

    if request.method == 'POST':
        product.delete()
        messages.success(request, 'Product deleted successfully!')
        return redirect('admin_products')

    context = {
        'product': product,
    }

    return render(request, 'admin/product_delete.html', context)

@login_required
@user_passes_test(is_admin)
def admin_product_view(request, product_id):
    product = get_object_or_404(Product, id=product_id)

    context = {
        'product': product,
    }

    return render(request, 'admin/product_view.html', context)

@login_required
@user_passes_test(is_admin)
def admin_users(request):
    # Get all users
    users_list = User.objects.all().order_by('-date_joined')

    # Pagination
    paginator = Paginator(users_list, 10)  # Show 10 users per page
    page = request.GET.get('page')
    users = paginator.get_page(page)

    context = {
        'users': users,
    }

    return render(request, 'admin/users.html', context)

@login_required
@user_passes_test(is_admin)
def admin_user_edit(request, user_id):
    user = get_object_or_404(User, id=user_id)

    # Placeholder for future user edit functionality
    context = {
        'user_obj': user,
    }

    return render(request, 'admin/user_edit.html', context)

@login_required
@user_passes_test(is_admin)
def admin_user_delete(request, user_id):
    user = get_object_or_404(User, id=user_id)

    # Placeholder for future user delete functionality
    context = {
        'user_obj': user,
    }

    return render(request, 'admin/user_delete.html', context)

@login_required
@user_passes_test(is_admin)
def admin_user_view(request, user_id):
    user = get_object_or_404(User, id=user_id)

    # Placeholder for future user view functionality
    context = {
        'user_obj': user,
    }

    return render(request, 'admin/user_view.html', context)

@login_required
@user_passes_test(is_admin)
def admin_orders(request):
    # Placeholder for future order management functionality
    return render(request, 'admin/orders.html')

@login_required
@user_passes_test(is_admin)
def admin_categories(request):
    """View all categories"""
    categories = Category.objects.all().order_by('name')
    return render(request, 'admin/categories.html', {'categories': categories})

@login_required
@user_passes_test(is_admin)
def admin_category_add(request):
    """Add a new category"""
    if request.method == 'POST':
        form = CategoryForm(request.POST, request.FILES)
        if form.is_valid():
            category = form.save()
            messages.success(request, f"Category '{category.name}' was created successfully!")
            return redirect('admin_categories')
    else:
        form = CategoryForm()

    return render(request, 'admin/category_form.html', {'form': form, 'action': 'Add'})

@login_required
@user_passes_test(is_admin)
def admin_category_edit(request, category_id):
    """Edit an existing category"""
    category = get_object_or_404(Category, id=category_id)

    if request.method == 'POST':
        form = CategoryForm(request.POST, request.FILES, instance=category)
        if form.is_valid():
            category = form.save()
            messages.success(request, f"Category '{category.name}' was updated successfully!")
            return redirect('admin_categories')
    else:
        form = CategoryForm(instance=category)

    return render(request, 'admin/category_form.html', {
        'form': form,
        'action': 'Edit',
        'category': category
    })

@login_required
@user_passes_test(is_admin)
def admin_category_delete(request, category_id):
    """Delete a category"""
    category = get_object_or_404(Category, id=category_id)

    if request.method == 'POST':
        name = category.name
        category.delete()
        messages.success(request, f"Category '{name}' was deleted successfully!")
        return redirect('admin_categories')

    return render(request, 'admin/category_confirm_delete.html', {'category': category})

@login_required
@user_passes_test(is_admin)
def admin_settings(request):
    # Placeholder for future settings functionality
    return render(request, 'admin/settings.html')

@login_required
@user_passes_test(is_admin)
def admin_brands(request):
    """Admin brands list view"""
    brands = Brand.objects.all().order_by('name')
    categories = Category.objects.all()

    context = {
        'brands': brands,
        'categories': categories,
        'active_link': 'brands',
    }

    return render(request, 'admin/brands.html', context)

@login_required
@user_passes_test(is_admin)
def admin_brand_add(request):
    """Admin brand add view"""
    if request.method == 'POST':
        form = BrandForm(request.POST, request.FILES)
        if form.is_valid():
            form.save()
            messages.success(request, "Brand added successfully!")
            return redirect('admin_brands')
    else:
        form = BrandForm()

    categories = Category.objects.all()

    context = {
        'form': form,
        'categories': categories,
        'active_link': 'brands',
        'is_add': True,
    }

    return render(request, 'admin/brand_form.html', context)

@login_required
@user_passes_test(is_admin)
def admin_brand_edit(request, brand_id):
    """Admin brand edit view"""
    brand = get_object_or_404(Brand, id=brand_id)

    if request.method == 'POST':
        form = BrandForm(request.POST, request.FILES, instance=brand)
        if form.is_valid():
            form.save()
            messages.success(request, "Brand updated successfully!")
            return redirect('admin_brands')
    else:
        form = BrandForm(instance=brand)

    categories = Category.objects.all()

    context = {
        'form': form,
        'brand': brand,
        'categories': categories,
        'active_link': 'brands',
        'is_edit': True,
    }

    return render(request, 'admin/brand_form.html', context)

@login_required
@user_passes_test(is_admin)
def admin_brand_delete(request, brand_id):
    """Admin brand delete view"""
    brand = get_object_or_404(Brand, id=brand_id)

    if request.method == 'POST':
        # Check if brand has associated products
        if brand.products.exists():
            messages.error(request, "Cannot delete brand. There are products associated with this brand.")
            return redirect('admin_brands')

        brand.delete()
        messages.success(request, "Brand deleted successfully!")
        return redirect('admin_brands')

    categories = Category.objects.all()

    context = {
        'brand': brand,
        'categories': categories,
        'active_link': 'brands',
    }

    return render(request, 'admin/brand_delete.html', context)


@login_required
@user_passes_test(is_admin)
def admin_section_headings(request):
    """Admin view for managing section headings"""
    headings = SectionHeading.objects.all().order_by('section_type')

    if request.method == 'POST':
        section_type = request.POST.get('section_type')
        title = request.POST.get('title')
        subtitle = request.POST.get('subtitle')
        is_active = request.POST.get('is_active') == 'on'

        # Check if section type already exists
        if SectionHeading.objects.filter(section_type=section_type).exists():
            messages.error(request, f"A heading for {dict(SectionHeading.SECTION_TYPES).get(section_type, section_type)} already exists.")
        else:
            SectionHeading.objects.create(
                section_type=section_type,
                title=title,
                subtitle=subtitle,
                is_active=is_active
            )
            messages.success(request, "Section heading created successfully!")

        return redirect('admin_section_headings')

    # Get section types for dropdown
    section_types = SectionHeading.SECTION_TYPES

    context = {
        'headings': headings,
        'section_types': section_types,
        'active_link': 'section_headings',
    }

    return render(request, 'admin/section_headings.html', context)


@login_required
@user_passes_test(is_admin)
def admin_hero_banners(request):
    """Admin view for managing hero banners"""
    try:
        banners = HeroBanner.objects.all().order_by('order', '-created_at')
    except:
        banners = []

    context = {
        'banners': banners,
        'active_link': 'hero_banners',
    }

    return render(request, 'admin/hero_banners.html', context)


@login_required
@user_passes_test(is_admin)
def admin_hero_banner_add(request):
    """Add a new hero banner"""
    if request.method == 'POST':
        try:
            # Create new banner
            banner = HeroBanner.objects.create(
                title=request.POST.get('title'),
                description=request.POST.get('description', ''),
                brand_text=request.POST.get('brand_text', ''),
                cta_label=request.POST.get('cta_label', 'Shop Now'),
                link_type=request.POST.get('link_type', 'custom'),
                link_target=request.POST.get('link_target', '#'),
                secondary_cta_label=request.POST.get('secondary_cta_label', ''),
                secondary_link_type=request.POST.get('secondary_link_type', ''),
                secondary_link_target=request.POST.get('secondary_link_target', ''),
                background_gradient_start=request.POST.get('background_gradient_start', '#8B5CF6'),
                background_gradient_end=request.POST.get('background_gradient_end', '#EC4899'),
                image_alt=request.POST.get('image_alt', ''),
                order=int(request.POST.get('order', 0)),
                is_active=request.POST.get('is_active') == 'on',
                image_width=int(request.POST.get('image_width', 0)) or None,
                image_height=int(request.POST.get('image_height', 0)) or None,
                width_unit=request.POST.get('width_unit', 'px'),
                height_unit=request.POST.get('height_unit', 'px')
            )

            # Handle image upload
            if 'image' in request.FILES:
                banner.image = request.FILES['image']
                banner.save()

            messages.success(request, "Hero banner created successfully!")
            return redirect('admin_hero_banners')
        except Exception as e:
            messages.error(request, f"Error creating banner: {str(e)}")

    context = {
        'active_link': 'hero_banners',
        'action': 'Add',
    }

    return render(request, 'admin/hero_banner_form.html', context)


@login_required
@user_passes_test(is_admin)
def admin_hero_banner_edit(request, banner_id):
    """Edit an existing hero banner"""
    try:
        banner = get_object_or_404(HeroBanner, id=banner_id)
    except:
        messages.error(request, "Banner not found.")
        return redirect('admin_hero_banners')

    if request.method == 'POST':
        try:
            # Update banner
            banner.title = request.POST.get('title')
            banner.description = request.POST.get('description', '')
            banner.brand_text = request.POST.get('brand_text', '')
            banner.cta_label = request.POST.get('cta_label', 'Shop Now')
            banner.link_type = request.POST.get('link_type', 'custom')
            banner.link_target = request.POST.get('link_target', '#')
            banner.secondary_cta_label = request.POST.get('secondary_cta_label', '')
            banner.secondary_link_type = request.POST.get('secondary_link_type', '')
            banner.secondary_link_target = request.POST.get('secondary_link_target', '')
            banner.background_gradient_start = request.POST.get('background_gradient_start', '#8B5CF6')
            banner.background_gradient_end = request.POST.get('background_gradient_end', '#EC4899')
            banner.image_alt = request.POST.get('image_alt', '')
            banner.order = int(request.POST.get('order', 0))
            banner.is_active = request.POST.get('is_active') == 'on'

            # Handle image dimensions
            banner.image_width = int(request.POST.get('image_width', 0)) or None
            banner.image_height = int(request.POST.get('image_height', 0)) or None
            banner.width_unit = request.POST.get('width_unit', 'px')
            banner.height_unit = request.POST.get('height_unit', 'px')

            # Handle image upload
            if 'image' in request.FILES:
                banner.image = request.FILES['image']

            banner.save()
            messages.success(request, "Hero banner updated successfully!")
            return redirect('admin_hero_banners')
        except Exception as e:
            messages.error(request, f"Error updating banner: {str(e)}")

    context = {
        'banner': banner,
        'active_link': 'hero_banners',
        'action': 'Edit',
    }

    return render(request, 'admin/hero_banner_form.html', context)


@login_required
@user_passes_test(is_admin)
def admin_hero_banner_delete(request, banner_id):
    """Delete a hero banner"""
    try:
        banner = get_object_or_404(HeroBanner, id=banner_id)
    except:
        messages.error(request, "Banner not found.")
        return redirect('admin_hero_banners')

    if request.method == 'POST':
        try:
            title = banner.title
            banner.delete()
            messages.success(request, f"Hero banner '{title}' was deleted successfully!")
        except Exception as e:
            messages.error(request, f"Error deleting banner: {str(e)}")
        return redirect('admin_hero_banners')

    context = {
        'banner': banner,
        'active_link': 'hero_banners',
    }

    return render(request, 'admin/hero_banner_delete.html', context)


@login_required
@user_passes_test(is_admin)
def admin_site_config(request):
    """Admin view for site configuration"""
    try:
        config, created = SiteConfiguration.objects.get_or_create(
            defaults={
                'site_name': 'iTechStore',
                'site_tagline': 'Your Technology Destination',
                'hero_auto_slide_interval': 5000,
                'hero_show_arrows': True,
                'hero_show_dots': True,
                'products_per_row_desktop': 4,
                'products_per_row_tablet': 2,
                'products_per_row_mobile': 1,
            }
        )
    except:
        config = None
        created = False

    if request.method == 'POST' and config:
        try:
            config.site_name = request.POST.get('site_name', 'iTechStore')
            config.site_tagline = request.POST.get('site_tagline', 'Your Technology Destination')
            config.hero_auto_slide_interval = int(request.POST.get('hero_auto_slide_interval', 5000))
            config.hero_show_arrows = request.POST.get('hero_show_arrows') == 'on'
            config.hero_show_dots = request.POST.get('hero_show_dots') == 'on'
            config.products_per_row_desktop = int(request.POST.get('products_per_row_desktop', 4))
            config.products_per_row_tablet = int(request.POST.get('products_per_row_tablet', 2))
            config.products_per_row_mobile = int(request.POST.get('products_per_row_mobile', 1))
            config.save()
            messages.success(request, "Site configuration updated successfully!")
        except Exception as e:
            messages.error(request, f"Error updating configuration: {str(e)}")
        return redirect('admin_site_config')

    context = {
        'config': config,
        'active_link': 'site_config',
    }

    return render(request, 'admin/site_config.html', context)


@login_required
@user_passes_test(is_admin)
def admin_section_heading_edit(request, heading_id):
    """Admin view for editing a section heading"""
    heading = get_object_or_404(SectionHeading, id=heading_id)

    if request.method == 'POST':
        title = request.POST.get('title')
        subtitle = request.POST.get('subtitle')
        is_active = request.POST.get('is_active') == 'on'

        heading.title = title
        heading.subtitle = subtitle
        heading.is_active = is_active
        heading.save()

        messages.success(request, "Section heading updated successfully!")
        return redirect('admin_section_headings')

    context = {
        'heading': heading,
        'active_link': 'section_headings',
    }

    return render(request, 'admin/section_heading_edit.html', context)


@login_required
@user_passes_test(is_admin)
def admin_promotions(request):
    """Admin view for managing product promotions"""
    promotions = ProductPromotion.objects.all().order_by('-created_at')

    # Filter by product if specified
    product_id = request.GET.get('product')
    if product_id:
        try:
            product = Product.objects.get(id=product_id)
            promotions = promotions.filter(product=product)
        except (Product.DoesNotExist, ValueError):
            pass

    # Filter by promotion type if specified
    promo_type = request.GET.get('type')
    if promo_type:
        if promo_type == 'featured':
            promotions = promotions.filter(is_featured=True)
        elif promo_type == 'best_seller':
            promotions = promotions.filter(is_best_seller=True)
        elif promo_type == 'new_arrival':
            promotions = promotions.filter(is_new_arrival=True)
        elif promo_type == 'top_deal':
            promotions = promotions.filter(is_top_deal=True)
        elif promo_type == 'special_offer':
            promotions = promotions.filter(is_special_offer=True)

    # Get all products for the form and filter
    products = Product.objects.all().order_by('name')

    # Handle form submission for new promotion
    if request.method == 'POST':
        from .forms import ProductPromotionForm
        form = ProductPromotionForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, "Promotion created successfully!")
            return redirect('admin_promotions')
    else:
        from .forms import ProductPromotionForm
        form = ProductPromotionForm()

    context = {
        'promotions': promotions,
        'products': products,
        'form': form,
        'active_link': 'promotions',
        'current_product_id': product_id,
        'current_promo_type': promo_type,
    }

    return render(request, 'admin/promotions.html', context)


@login_required
@user_passes_test(is_admin)
def admin_promotion_edit(request, promotion_id):
    """Admin view for editing a product promotion"""
    promotion = get_object_or_404(ProductPromotion, id=promotion_id)

    if request.method == 'POST':
        from .forms import ProductPromotionForm
        form = ProductPromotionForm(request.POST, instance=promotion)
        if form.is_valid():
            form.save()
            messages.success(request, "Promotion updated successfully!")
            return redirect('admin_promotions')
    else:
        from .forms import ProductPromotionForm
        form = ProductPromotionForm(instance=promotion)

    context = {
        'form': form,
        'promotion': promotion,
        'active_link': 'promotions',
    }

    return render(request, 'admin/promotion_form.html', context)


@login_required
@user_passes_test(is_admin)
def admin_promotion_delete(request, promotion_id):
    """Admin view for deleting a product promotion"""
    promotion = get_object_or_404(ProductPromotion, id=promotion_id)

    if request.method == 'POST':
        product_name = promotion.product.name
        promotion.delete()
        messages.success(request, f"Promotion for '{product_name}' deleted successfully!")
        return redirect('admin_promotions')

    context = {
        'promotion': promotion,
        'active_link': 'promotions',
    }

    return render(request, 'admin/promotion_delete.html', context)


# ============================================================================
# SALES MANAGEMENT VIEWS
# ============================================================================

@login_required
@user_passes_test(is_admin)
def admin_sales_dashboard(request):
    """Sales Management Dashboard"""
    # Get statistics
    total_sales = SaleCategory.objects.count()
    active_sales = SaleCategory.objects.filter(is_active=True).count()
    featured_sales = SaleCategory.objects.filter(is_featured=True).count()
    total_campaigns = SaleCampaign.objects.count()
    active_campaigns = SaleCampaign.objects.filter(status='active').count()

    # Get products in sales
    products_in_sales = ProductSaleAssignment.objects.filter(
        is_active=True, sale_category__is_active=True
    ).values('product').distinct().count()

    # Get recent sales
    recent_sales = SaleCategory.objects.all().order_by('-created_at')[:5]

    # Get recent campaigns
    recent_campaigns = SaleCampaign.objects.all().order_by('-created_at')[:3]

    # Get top performing sales (by usage)
    top_sales = SaleCategory.objects.filter(is_active=True).order_by('-current_uses')[:5]

    context = {
        'total_sales': total_sales,
        'active_sales': active_sales,
        'featured_sales': featured_sales,
        'total_campaigns': total_campaigns,
        'active_campaigns': active_campaigns,
        'products_in_sales': products_in_sales,
        'recent_sales': recent_sales,
        'recent_campaigns': recent_campaigns,
        'top_sales': top_sales,
        'active_link': 'sales_dashboard',
    }

    return render(request, 'admin/sales_dashboard.html', context)

@login_required
@user_passes_test(is_admin)
def admin_sales_categories(request):
    """List all sale categories"""
    # Get query parameters for filtering
    sale_type = request.GET.get('type')
    status = request.GET.get('status')
    search_query = request.GET.get('search')

    # Start with all sales
    sales_list = SaleCategory.objects.all()

    # Apply filters
    if sale_type:
        sales_list = sales_list.filter(sale_type=sale_type)

    if status == 'active':
        sales_list = sales_list.filter(is_active=True)
    elif status == 'inactive':
        sales_list = sales_list.filter(is_active=False)
    elif status == 'featured':
        sales_list = sales_list.filter(is_featured=True)

    if search_query:
        sales_list = sales_list.filter(
            Q(name__icontains=search_query) |
            Q(badge_text__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    # Order by priority and creation date
    sales_list = sales_list.order_by('-priority', '-is_featured', '-created_at')

    # Pagination
    paginator = Paginator(sales_list, 15)
    page = request.GET.get('page')
    sales = paginator.get_page(page)

    # Get sale type choices for filter
    sale_type_choices = SaleCategory.SALE_TYPE_CHOICES

    context = {
        'sales': sales,
        'sale_type_choices': sale_type_choices,
        'current_type': sale_type,
        'current_status': status,
        'search_query': search_query,
        'active_link': 'sales_categories',
    }

    return render(request, 'admin/sales_categories.html', context)

@login_required
@user_passes_test(is_admin)
def admin_sale_category_add(request):
    """Add a new sale category"""
    if request.method == 'POST':
        try:
            # Get form data
            name = request.POST.get('name')
            sale_type = request.POST.get('sale_type')
            badge_text = request.POST.get('badge_text')
            badge_color = request.POST.get('badge_color', '#dc3545')
            priority = int(request.POST.get('priority', 2))
            description = request.POST.get('description', '')

            # Create sale category
            sale_data = {
                'name': name,
                'sale_type': sale_type,
                'badge_text': badge_text,
                'badge_color': badge_color,
                'priority': priority,
                'description': description,
                'is_active': request.POST.get('is_active') == 'on',
                'is_featured': request.POST.get('is_featured') == 'on',
                'is_member_only': request.POST.get('is_member_only') == 'on',
                'is_first_time_buyer_only': request.POST.get('is_first_time_buyer_only') == 'on',
                'is_stackable': request.POST.get('is_stackable') == 'on',
                'requires_coupon_code': request.POST.get('requires_coupon_code') == 'on',
            }

            # Add optional fields based on sale type
            if request.POST.get('discount_percentage'):
                sale_data['discount_percentage'] = Decimal(request.POST.get('discount_percentage'))
            if request.POST.get('discount_amount'):
                sale_data['discount_amount'] = Decimal(request.POST.get('discount_amount'))
            if request.POST.get('minimum_purchase_amount'):
                sale_data['minimum_purchase_amount'] = Decimal(request.POST.get('minimum_purchase_amount'))
            if request.POST.get('maximum_discount_amount'):
                sale_data['maximum_discount_amount'] = Decimal(request.POST.get('maximum_discount_amount'))
            if request.POST.get('free_item_description'):
                sale_data['free_item_description'] = request.POST.get('free_item_description')
            if request.POST.get('free_item_value'):
                sale_data['free_item_value'] = Decimal(request.POST.get('free_item_value'))
            if request.POST.get('buy_quantity'):
                sale_data['buy_quantity'] = int(request.POST.get('buy_quantity'))
            if request.POST.get('get_quantity'):
                sale_data['get_quantity'] = int(request.POST.get('get_quantity'))
            if request.POST.get('coupon_code'):
                sale_data['coupon_code'] = request.POST.get('coupon_code')
            if request.POST.get('max_total_uses'):
                sale_data['max_total_uses'] = int(request.POST.get('max_total_uses'))
            if request.POST.get('max_uses_per_customer'):
                sale_data['max_uses_per_customer'] = int(request.POST.get('max_uses_per_customer'))

            # Handle end date
            end_date = request.POST.get('end_date')
            if end_date:
                from django.utils.dateparse import parse_datetime
                sale_data['end_date'] = parse_datetime(end_date)

            # Create the sale
            sale = SaleCategory.objects.create(**sale_data)
            messages.success(request, f"Sale category '{sale.name}' was created successfully!")
            return redirect('admin_sales_categories')

        except Exception as e:
            messages.error(request, f"Error creating sale category: {str(e)}")

    context = {
        'sale_type_choices': SaleCategory.SALE_TYPE_CHOICES,
        'badge_color_choices': SaleCategory.BADGE_COLOR_CHOICES,
        'priority_choices': SaleCategory.PRIORITY_CHOICES,
        'active_link': 'sales_categories',
        'action': 'Add',
    }

    return render(request, 'admin/sale_category_form.html', context)

@login_required
@user_passes_test(is_admin)
def admin_sale_category_edit(request, sale_id):
    """Edit an existing sale category"""
    sale = get_object_or_404(SaleCategory, id=sale_id)

    if request.method == 'POST':
        try:
            # Update sale category
            sale.name = request.POST.get('name')
            sale.sale_type = request.POST.get('sale_type')
            sale.badge_text = request.POST.get('badge_text')
            sale.badge_color = request.POST.get('badge_color', '#dc3545')
            sale.priority = int(request.POST.get('priority', 2))
            sale.description = request.POST.get('description', '')
            sale.is_active = request.POST.get('is_active') == 'on'
            sale.is_featured = request.POST.get('is_featured') == 'on'
            sale.is_member_only = request.POST.get('is_member_only') == 'on'
            sale.is_first_time_buyer_only = request.POST.get('is_first_time_buyer_only') == 'on'
            sale.is_stackable = request.POST.get('is_stackable') == 'on'
            sale.requires_coupon_code = request.POST.get('requires_coupon_code') == 'on'

            # Update optional fields
            if request.POST.get('discount_percentage'):
                sale.discount_percentage = Decimal(request.POST.get('discount_percentage'))
            else:
                sale.discount_percentage = None

            if request.POST.get('discount_amount'):
                sale.discount_amount = Decimal(request.POST.get('discount_amount'))
            else:
                sale.discount_amount = None

            if request.POST.get('minimum_purchase_amount'):
                sale.minimum_purchase_amount = Decimal(request.POST.get('minimum_purchase_amount'))
            else:
                sale.minimum_purchase_amount = None

            if request.POST.get('maximum_discount_amount'):
                sale.maximum_discount_amount = Decimal(request.POST.get('maximum_discount_amount'))
            else:
                sale.maximum_discount_amount = None

            sale.free_item_description = request.POST.get('free_item_description', '')

            if request.POST.get('free_item_value'):
                sale.free_item_value = Decimal(request.POST.get('free_item_value'))
            else:
                sale.free_item_value = None

            if request.POST.get('buy_quantity'):
                sale.buy_quantity = int(request.POST.get('buy_quantity'))
            else:
                sale.buy_quantity = None

            if request.POST.get('get_quantity'):
                sale.get_quantity = int(request.POST.get('get_quantity'))
            else:
                sale.get_quantity = None

            sale.coupon_code = request.POST.get('coupon_code', '')

            if request.POST.get('max_total_uses'):
                sale.max_total_uses = int(request.POST.get('max_total_uses'))
            else:
                sale.max_total_uses = None

            if request.POST.get('max_uses_per_customer'):
                sale.max_uses_per_customer = int(request.POST.get('max_uses_per_customer'))
            else:
                sale.max_uses_per_customer = None

            # Handle end date
            end_date = request.POST.get('end_date')
            if end_date:
                from django.utils.dateparse import parse_datetime
                sale.end_date = parse_datetime(end_date)
            else:
                sale.end_date = None

            sale.save()
            messages.success(request, f"Sale category '{sale.name}' was updated successfully!")
            return redirect('admin_sales_categories')

        except Exception as e:
            messages.error(request, f"Error updating sale category: {str(e)}")

    context = {
        'sale': sale,
        'sale_type_choices': SaleCategory.SALE_TYPE_CHOICES,
        'badge_color_choices': SaleCategory.BADGE_COLOR_CHOICES,
        'priority_choices': SaleCategory.PRIORITY_CHOICES,
        'active_link': 'sales_categories',
        'action': 'Edit',
    }

    return render(request, 'admin/sale_category_form.html', context)

@login_required
@user_passes_test(is_admin)
def admin_sale_category_delete(request, sale_id):
    """Delete a sale category"""
    sale = get_object_or_404(SaleCategory, id=sale_id)

    if request.method == 'POST':
        name = sale.name
        sale.delete()
        messages.success(request, f"Sale category '{name}' was deleted successfully!")
        return redirect('admin_sales_categories')

    # Get products assigned to this sale
    assigned_products = ProductSaleAssignment.objects.filter(sale_category=sale, is_active=True)

    context = {
        'sale': sale,
        'assigned_products': assigned_products,
        'active_link': 'sales_categories',
    }

    return render(request, 'admin/sale_category_delete.html', context)

@login_required
@user_passes_test(is_admin)
def admin_sale_category_products(request, sale_id):
    """Manage products assigned to a sale category"""
    sale = get_object_or_404(SaleCategory, id=sale_id)

    # Get assigned products
    assignments = ProductSaleAssignment.objects.filter(
        sale_category=sale
    ).select_related('product').order_by('-is_active', 'product__name')

    # Get available products (not assigned to this sale)
    assigned_product_ids = assignments.values_list('product_id', flat=True)
    available_products = Product.objects.exclude(id__in=assigned_product_ids).order_by('name')

    # Handle product assignment
    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'assign':
            product_ids = request.POST.getlist('product_ids')
            for product_id in product_ids:
                try:
                    product = Product.objects.get(id=product_id)
                    ProductSaleAssignment.objects.get_or_create(
                        product=product,
                        sale_category=sale,
                        defaults={'is_active': True, 'assigned_by': request.user}
                    )
                except Product.DoesNotExist:
                    pass
            messages.success(request, f"Products assigned to '{sale.name}' successfully!")

        elif action == 'remove':
            assignment_ids = request.POST.getlist('assignment_ids')
            ProductSaleAssignment.objects.filter(id__in=assignment_ids).delete()
            messages.success(request, "Products removed from sale successfully!")

        elif action == 'toggle':
            assignment_ids = request.POST.getlist('assignment_ids')
            for assignment in ProductSaleAssignment.objects.filter(id__in=assignment_ids):
                assignment.is_active = not assignment.is_active
                assignment.save()
            messages.success(request, "Product assignments updated successfully!")

        return redirect('admin_sale_category_products', sale_id=sale_id)

    context = {
        'sale': sale,
        'assignments': assignments,
        'available_products': available_products,
        'active_link': 'sales_categories',
    }

    return render(request, 'admin/sale_category_products.html', context)

@login_required
@user_passes_test(is_admin)
def admin_product_promotions(request, product_id):
    """Admin view for managing promotions for a specific product"""
    product = get_object_or_404(Product, id=product_id)
    promotions = ProductPromotion.objects.filter(product=product).order_by('-created_at')

    # Handle form submission for new promotion
    if request.method == 'POST':
        from .forms import ProductPromotionForm
        form = ProductPromotionForm(request.POST, initial={'product': product})
        if form.is_valid():
            form.save()
            messages.success(request, f"Promotion for '{product.name}' created successfully!")
            return redirect('admin_product_promotions', product_id=product_id)
    else:
        from .forms import ProductPromotionForm
        form = ProductPromotionForm(initial={'product': product})

    context = {
        'product': product,
        'promotions': promotions,
        'form': form,
        'active_link': 'products',
    }

    return render(request, 'admin/product_promotions.html', context)
