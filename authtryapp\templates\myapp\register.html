{% extends "base.html" %}

{% block title %}Register{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-lg">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">Create an Account</h3>
                </div>
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} text-center">{{ message }}</div>
                        {% endfor %}
                    {% endif %}

                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.username.id_for_label }}" class="form-label">Username</label>
                            {{ form.username }}
                            {% if form.username.errors %}
                                <div class="text-danger">
                                    {% for error in form.username.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">{{ form.username.help_text }}</div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.first_name.id_for_label }}" class="form-label">First Name</label>
                                {{ form.first_name }}
                                {% if form.first_name.errors %}
                                    <div class="text-danger">
                                        {% for error in form.first_name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.last_name.id_for_label }}" class="form-label">Last Name</label>
                                {{ form.last_name }}
                                {% if form.last_name.errors %}
                                    <div class="text-danger">
                                        {% for error in form.last_name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label">Email</label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="text-danger">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.password1.id_for_label }}" class="form-label">Password</label>
                            {{ form.password1 }}
                            {% if form.password1.errors %}
                                <div class="text-danger">
                                    {% for error in form.password1.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">{{ form.password1.help_text }}</div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.password2.id_for_label }}" class="form-label">Confirm Password</label>
                            {{ form.password2 }}
                            {% if form.password2.errors %}
                                <div class="text-danger">
                                    {% for error in form.password2.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">{{ form.password2.help_text }}</div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">Register</button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center">
                    <p class="mb-0">Already have an account? <a href="{% url 'login' %}">Login here</a></p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
