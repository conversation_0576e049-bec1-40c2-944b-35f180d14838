{% extends 'base_new.html' %}
{% load static %}

{% block title %}{{ section_title }} - iTechStore{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/sales_products.css' %}">
<style>
    /* Category Card Styles - Based on Sales Product Card */
    .category-card {
        background: #fff;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        position: relative;
        display: flex;
        flex-direction: column;
        height: 100%;
        border: 1px solid #f0f0f0;
    }

    .category-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.12);
        border-color: #e0e0e0;
    }

    /* Category Image Container */
    .category-image-container {
        position: relative;
        height: 240px;
        padding: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #fafafa;
        overflow: hidden;
    }

    .category-image {
        max-height: 200px;
        max-width: 100%;
        width: auto;
        object-fit: cover;
        border-radius: 8px;
        transition: transform 0.3s ease;
    }

    .category-card:hover .category-image {
        transform: scale(1.05);
    }

    /* Category Badges */
    .category-badges {
        position: absolute;
        top: 12px;
        right: 12px;
        display: flex;
        flex-direction: column;
        gap: 6px;
        z-index: 2;
    }

    .category-badge {
        background: #007bff;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Category Information */
    .category-info {
        padding: 20px;
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        gap: 12px;
    }

    /* Title & Description */
    .category-title-section {
        margin-bottom: 8px;
    }

    .category-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin: 0 0 8px 0;
        line-height: 1.3;
    }

    .category-title a {
        color: inherit;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .category-title a:hover {
        color: #007bff;
    }

    .category-description {
        font-size: 14px;
        color: #666;
        margin: 0;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    /* Category Stats */
    .category-stats {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 8px;
    }

    .stat-item {
        display: flex;
        align-items: center;
        gap: 6px;
    }

    .stat-item i {
        font-size: 14px;
        color: #666;
    }

    .stat-text {
        font-size: 14px;
        color: #666;
        font-weight: 500;
    }

    /* Action Buttons */
    .category-actions {
        margin-top: auto;
        display: flex;
        gap: 10px;
    }

    .browse-category-btn {
        flex: 1;
        padding: 12px 16px;
        border: none;
        background: #007bff;
        color: white;
        border-radius: 6px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        font-size: 14px;
    }

    .browse-category-btn:hover {
        background: #0056b3;
        color: white;
        text-decoration: none;
        transform: translateY(-1px);
    }

    .categories-header {
        background-color: #ffffff;
        padding: 30px 0 10px;
        text-align: center;
        margin-bottom: 20px;
    }

    .categories-title {
        font-size: 36px;
        font-weight: 700;
        color: #333;
        margin-bottom: 10px;
    }

    .categories-subtitle {
        font-size: 18px;
        color: #666;
        font-weight: 400;
        max-width: 800px;
        margin: 0 auto;
        line-height: 1.6;
    }

    .no-categories-message {
        text-align: center;
        padding: 80px 20px;
        background: #f8f9fa;
        border-radius: 12px;
        border: 2px dashed #dee2e6;
        margin: 40px 0;
    }

    .view-all-btn {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 12px 24px;
        background: #007bff;
        color: white;
        text-decoration: none;
        border-radius: 6px;
        font-weight: 600;
        transition: background 0.3s ease;
    }

    .view-all-btn:hover {
        background: #0056b3;
        color: white;
        text-decoration: none;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .categories-title {
            font-size: 28px;
        }

        .categories-subtitle {
            font-size: 16px;
        }

        .category-image-container {
            height: 200px;
            padding: 15px;
        }

        .category-image {
            max-height: 170px;
        }

        .category-info {
            padding: 16px;
        }
    }

    @media (max-width: 480px) {
        .sales-products-grid {
            grid-template-columns: 1fr;
            gap: 12px;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Categories Header -->
<section class="categories-header">
    <div class="container">
        <h1 class="categories-title">{{ section_title }}</h1>
        <p class="categories-subtitle">{{ section_subtitle }}</p>
    </div>
</section>

<!-- Categories Grid -->
<section class="sales-section">
    <div class="container">
        {% if categories_with_counts %}
            <div class="sales-products-grid">
                {% for category_data in categories_with_counts %}
                    {% include 'includes/category_card.html' with category_data=category_data %}
                {% endfor %}
            </div>
        {% else %}
            <!-- No Categories Message -->
            <div class="no-categories-message">
                <i class="fas fa-folder-open" style="font-size: 64px; color: #ddd; margin-bottom: 20px;"></i>
                <h3 style="color: #666; margin-bottom: 15px; font-size: 24px;">No Categories Found</h3>
                <p style="color: #999; font-size: 16px; margin-bottom: 20px;">
                    No categories are currently available. Please check back later.
                </p>
                <a href="{% url 'home' %}" class="view-all-btn">
                    <i class="fas fa-home"></i>
                    Back to Home
                </a>
            </div>
        {% endif %}
    </div>
</section>
{% endblock %}
