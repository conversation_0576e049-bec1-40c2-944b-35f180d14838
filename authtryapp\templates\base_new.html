{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>{% block title %}iTechStore - Your Technology Destination{% endblock %}</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/ui-enhancements.css' %}">
    <link rel="stylesheet" href="{% static 'css/product_carousel.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Top Navigation Bar -->
    <div class="top-nav">
        <div class="container">
            <div class="top-nav-left">
                <span>NPR</span>
                <i class="fas fa-chevron-down"></i>
            </div>
            <div class="top-nav-right">
                {% if user.is_authenticated %}
                    <span>Welcome, {{ user.username }}</span>
                    {% if user.is_superuser %}
                        <a href="{% url 'admin_dashboard' %}" class="create-account" title="Admin Panel">
                            <i class="fas fa-cog"></i> Admin
                        </a>
                    {% endif %}
                    <a href="{% url 'logout' %}" class="sign-in">Logout</a>
                {% else %}
                    <a href="{% url 'register' %}" class="create-account">Create an account</a>
                    <a href="{% url 'login' %}" class="sign-in">Sign in</a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Main Navigation -->
    <nav class="main-nav">
        <div class="container">
            <div class="logo">
                <a href="{% url 'home' %}">
                    <h1><span class="i-text">i</span><span class="tech-text">Tech</span><span class="store-text">Store</span></h1>
                    <p class="tagline">Your Technology Destination</p>
                </a>
            </div>
            <div class="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </div>
            <div class="nav-links">
                <ul>
                    <li><a href="{% url 'home' %}" class="{% if request.path == '/' %}active{% endif %}">Home</a></li>
                    <li class="dropdown">
                        <a href="#" class="{% if '/category/' in request.path %}active{% endif %}">Shop <i class="fas fa-chevron-down"></i></a>
                        <div class="dropdown-content">
                            {% if categories %}
                                {% for category in categories %}
                                    <a href="{% url 'category_view' category.slug %}" data-category="{{ category.slug }}" class="{% if category.slug in request.path %}active{% endif %}">{{ category.name }}</a>
                                {% endfor %}
                                <a href="{% url 'all_products' %}" data-category="all">View All Products</a>
                                <a href="{% url 'all_categories' %}" data-category="categories">View All Categories</a>
                            {% else %}
                                <a href="#" data-category="laptops">Laptops</a>
                                <a href="#" data-category="desktops">Desktops</a>
                                <a href="#" data-category="gaming">Gaming</a>
                                <a href="#" data-category="audio">Audio & Video</a>
                                <a href="#" data-category="mobile">Phones & Tablets</a>
                                <a href="#" data-category="smart-home">Smart Home</a>
                                <a href="#" data-category="accessories">Accessories</a>
                                <a href="{% url 'all_products' %}" data-category="all">View All Products</a>
                                <a href="{% url 'all_categories' %}" data-category="categories">View All Categories</a>
                            {% endif %}
                        </div>
                    </li>
                    <li><a href="{% url 'brand_list' %}" class="{% if '/brands/' in request.path or '/brand/' in request.path %}active{% endif %}">Brands</a></li>
                    <li><a href="{% url 'sales_products' %}" class="{% if '/sales/' in request.path %}active{% endif %}">Sales</a></li>
                    <li class="dropdown">
                        <a href="#">Blogs <i class="fas fa-chevron-down"></i></a>
                        <div class="dropdown-content">
                            <a href="#">Tech News</a>
                            <a href="#">Reviews</a>
                            <a href="#">Tutorials</a>
                        </div>
                    </li>
                    <li><a href="#">PC BLD</a></li>
                </ul>
            </div>
            <div class="nav-icons">
                <div class="search-container">
                    <form id="search-form" action="#" method="get">
                        <input type="text" id="search-input" placeholder="Search products...">
                        <button type="submit" class="search-btn"><i class="fas fa-search"></i></button>
                    </form>
                    <div class="search-results" id="search-results"></div>
                </div>
                <a href="{% url 'cart_summary' %}" class="cart-icon">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count">{% if cart %}{{ cart.total_items|default:"0" }}{% else %}0{% endif %}</span>
                </a>
            </div>
        </div>
    </nav>

    <!-- Mobile Menu (Hidden by default) -->
    <div class="mobile-menu">
        <div class="mobile-menu-header">
            <h3><span class="i-text">i</span><span class="tech-text">Tech</span><span class="store-text">Store</span></h3>
            <button class="mobile-menu-close"><i class="fas fa-times"></i></button>
        </div>
        <div class="mobile-search">
            <form id="mobile-search-form" action="#" method="get">
                <input type="text" id="mobile-search-input" placeholder="Search products...">
                <button type="submit" class="mobile-search-btn"><i class="fas fa-search"></i></button>
            </form>
            <div class="search-results" id="mobile-search-results"></div>
        </div>
        <ul class="mobile-menu-links">
            <li><a href="{% url 'home' %}" class="{% if request.path == '/' %}active{% endif %}">Home</a></li>
            <li class="mobile-dropdown">
                <a href="#">Shop <i class="fas fa-chevron-down"></i></a>
                <ul class="mobile-dropdown-content">
                    {% if categories %}
                        {% for category in categories %}
                            <li><a href="{% url 'category_view' category.slug %}" data-category="{{ category.slug }}" class="{% if category.slug in request.path %}active{% endif %}">{{ category.name }}</a></li>
                        {% endfor %}
                        <li><a href="{% url 'all_products' %}" data-category="all">View All Products</a></li>
                        <li><a href="{% url 'all_categories' %}" data-category="categories">View All Categories</a></li>
                    {% else %}
                        <li><a href="#" data-category="laptops">Laptops</a></li>
                        <li><a href="#" data-category="desktops">Desktops</a></li>
                        <li><a href="#" data-category="gaming">Gaming</a></li>
                        <li><a href="#" data-category="audio">Audio & Video</a></li>
                        <li><a href="#" data-category="mobile">Phones & Tablets</a></li>
                        <li><a href="#" data-category="smart-home">Smart Home</a></li>
                        <li><a href="#" data-category="accessories">Accessories</a></li>
                        <li><a href="{% url 'all_products' %}" data-category="all">View All Products</a></li>
                        <li><a href="{% url 'all_categories' %}" data-category="categories">View All Categories</a></li>
                    {% endif %}
                </ul>
            </li>
            <li><a href="{% url 'brand_list' %}" class="{% if '/brands/' in request.path or '/brand/' in request.path %}active{% endif %}">Brands</a></li>
            <li><a href="{% url 'sales_products' %}" class="{% if '/sales/' in request.path %}active{% endif %}">Sales</a></li>
            <li class="mobile-dropdown">
                <a href="#">Blogs <i class="fas fa-chevron-down"></i></a>
                <ul class="mobile-dropdown-content">
                    <li><a href="#">Tech News</a></li>
                    <li><a href="#">Reviews</a></li>
                    <li><a href="#">Tutorials</a></li>
                </ul>
            </li>
            <li><a href="#">PC BLD</a></li>
        </ul>
        <div class="mobile-menu-footer">
            {% if user.is_authenticated %}
                <a href="{% url 'profile' %}" class="mobile-account-link">My Profile</a>
                <a href="{% url 'logout' %}" class="mobile-account-link">Logout</a>
            {% else %}
                <a href="{% url 'register' %}" class="mobile-account-link">Create an account</a>
                <a href="{% url 'login' %}" class="mobile-account-link">Sign in</a>
            {% endif %}
            <a href="{% url 'cart_summary' %}" class="mobile-account-link">
                <i class="fas fa-shopping-cart"></i> Cart ({% if cart %}{{ cart.total_items|default:"0" }}{% else %}0{% endif %})
            </a>
        </div>
    </div>

    <!-- Main Content -->
    {% block content %}{% endblock %}

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-columns">
                <div class="footer-column">
                    <h3>Shop</h3>
                    <ul>
                        <li><a href="{% url 'sales_products' %}">Sales & Offers</a></li>
                        <li><a href="{% url 'all_products' %}">All Products</a></li>
                        <li><a href="{% url 'all_categories' %}">All Categories</a></li>
                        {% if categories %}
                            {% for category in categories %}
                                <li><a href="{% url 'category_view' category.slug %}" data-category="{{ category.slug }}">{{ category.name }}</a></li>
                            {% endfor %}
                        {% else %}
                            <li><a href="#" data-category="laptops">Laptops</a></li>
                            <li><a href="#" data-category="desktops">Desktops</a></li>
                            <li><a href="#" data-category="gaming">Gaming</a></li>
                            <li><a href="#" data-category="audio">Audio & Video</a></li>
                            <li><a href="#" data-category="mobile">Phones & Tablets</a></li>
                            <li><a href="#" data-category="smart-home">Smart Home</a></li>
                            <li><a href="#" data-category="accessories">Accessories</a></li>
                        {% endif %}
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Support</h3>
                    <ul>
                        <li><a href="#">Contact Us</a></li>
                        <li><a href="#">FAQs</a></li>
                        <li><a href="#">Shipping</a></li>
                        <li><a href="#">Returns</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Company</h3>
                    <ul>
                        <li><a href="#">About Us</a></li>
                        <li><a href="#">Careers</a></li>
                        <li><a href="#">Blog</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Connect</h3>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                    <div class="newsletter">
                        <h4>Subscribe to our newsletter</h4>
                        <form>
                            <input type="email" placeholder="Your email address">
                            <button type="submit">Subscribe</button>
                        </form>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 iTechStore. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Floating Cart Button -->
    <a href="{% url 'cart_summary' %}" class="floating-cart">
        <i class="fas fa-shopping-cart"></i>
        <span class="floating-cart-count">{% if cart %}{{ cart.total_items|default:"0" }}{% else %}0{% endif %}</span>
    </a>

    <script src="{% static 'js/script.js' %}"></script>
    <script src="{% static 'js/ui-enhancements.js' %}"></script>
    <script src="{% static 'js/product_carousel.js' %}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
