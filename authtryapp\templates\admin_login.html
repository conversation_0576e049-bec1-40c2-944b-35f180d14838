{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>Admin Login - iTechStore</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            background-color: #f5f7fa;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .admin-login-container {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 40px 0;
        }

        .admin-login-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 450px;
            overflow: hidden;
        }

        .admin-login-header {
            background-color: #1a1a1a;
            color: white;
            padding: 30px;
            text-align: center;
        }

        .admin-login-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }

        .admin-login-logo i {
            font-size: 40px;
            margin-right: 15px;
            color: #4dabf7;
        }

        .admin-login-logo h1 {
            font-size: 24px;
            font-weight: 700;
        }

        .admin-login-title {
            font-size: 18px;
            opacity: 0.8;
        }

        .admin-login-body {
            padding: 30px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            border-color: #4dabf7;
            outline: none;
            box-shadow: 0 0 0 3px rgba(77, 171, 247, 0.1);
        }

        .admin-login-button {
            width: 100%;
            padding: 14px;
            background: linear-gradient(to right, #4dabf7, #0c8599);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: opacity 0.3s;
        }

        .admin-login-button:hover {
            opacity: 0.9;
        }

        .admin-login-footer {
            text-align: center;
            margin-top: 25px;
            color: #777;
        }

        .admin-login-footer a {
            color: #4dabf7;
            font-weight: 500;
            text-decoration: none;
        }

        .admin-login-footer a:hover {
            text-decoration: underline;
        }

        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            color: white;
        }

        .alert-danger {
            background-color: #f44336;
        }

        .alert-success {
            background-color: #4caf50;
        }

        .input-group {
            position: relative;
        }

        .input-group .form-control {
            padding-left: 45px;
        }

        .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #777;
        }

        .remember-me {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .remember-me input {
            margin-right: 10px;
        }

        .remember-me label {
            color: #555;
            font-weight: normal;
        }

        @media (max-width: 576px) {
            .admin-login-card {
                margin: 0 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Top Navigation Bar -->
    <div class="top-nav">
        <div class="container">
            <div class="top-nav-left">
                <span>Admin Panel</span>
            </div>
            <div class="top-nav-right">
                <a href="{% url 'home' %}" class="sign-in">Back to Store</a>
            </div>
        </div>
    </div>

    <!-- Admin Login Container -->
    <div class="admin-login-container">
        <div class="admin-login-card">
            <div class="admin-login-header">
                <div class="admin-login-logo">
                    <i class="fas fa-shield-alt"></i>
                    <h1>iTechStore</h1>
                </div>
                <div class="admin-login-title">Administrator Login</div>
            </div>
            <div class="admin-login-body">
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert {% if message.tags == 'error' %}alert-danger{% else %}alert-success{% endif %}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}

                {% if form.errors %}
                    <div class="alert alert-danger">
                        Invalid username or password. Please try again.
                    </div>
                {% endif %}

                <form method="post" action="/admin/">
                    {% csrf_token %}
                    <div class="form-group">
                        <label for="username">Username</label>
                        <div class="input-group">
                            <span class="input-icon"><i class="fas fa-user"></i></span>
                            <input type="text" id="username" name="username" class="form-control" placeholder="Enter your username" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <div class="input-group">
                            <span class="input-icon"><i class="fas fa-lock"></i></span>
                            <input type="password" id="password" name="password" class="form-control" placeholder="Enter your password" required>
                        </div>
                    </div>
                    <div class="remember-me">
                        <input type="checkbox" id="remember" name="remember">
                        <label for="remember">Remember me</label>
                    </div>
                    <button type="submit" class="admin-login-button">Sign In</button>

                    <div class="admin-login-footer">
                        <p>Forgot your password? <a href="#">Reset Password</a></p>
                        <p>Return to <a href="{% url 'home' %}">Store</a></p>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="{% static 'js/script.js' %}"></script>
</body>
</html>
