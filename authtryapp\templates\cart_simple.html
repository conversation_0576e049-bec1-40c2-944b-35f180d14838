{% extends 'base_new.html' %}
{% load static %}

{% block title %}Shopping Cart - iTechStore{% endblock %}

{% block extra_css %}
<style>
    .cart-container {
        padding: 40px 0;
        min-height: 100vh;
    }

    .cart-header {
        text-align: center;
        margin-bottom: 40px;
        padding: 30px;
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .cart-item {
        display: flex;
        align-items: center;
        padding: 20px;
        border-bottom: 1px solid #eee;
        background: white;
        margin-bottom: 10px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .cart-item-image {
        width: 80px;
        height: 80px;
        margin-right: 20px;
        border-radius: 8px;
        overflow: hidden;
    }

    .cart-item-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .cart-item-details {
        flex-grow: 1;
    }

    .cart-item-name {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 5px;
    }

    .cart-item-price {
        font-size: 16px;
        color: #28a745;
        font-weight: 600;
    }

    .cart-item-actions {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .quantity-control {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .quantity-btn {
        background: #667eea;
        color: white;
        border: none;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
    }

    .quantity-btn:hover {
        background: #5a6fd8;
    }

    .quantity-btn:disabled {
        background: #ccc;
        cursor: not-allowed;
    }

    .quantity-input {
        width: 50px;
        text-align: center;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 5px;
    }

    .remove-btn {
        background: #dc3545;
        color: white;
        border: none;
        padding: 8px 15px;
        border-radius: 5px;
        cursor: pointer;
    }

    .remove-btn:hover {
        background: #c82333;
    }

    .cart-summary {
        background: white;
        padding: 30px;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        margin-top: 30px;
    }

    .empty-cart {
        text-align: center;
        padding: 80px 40px;
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .empty-cart i {
        font-size: 60px;
        color: #ddd;
        margin-bottom: 20px;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 12px 25px;
        border: none;
        border-radius: 25px;
        text-decoration: none;
        display: inline-block;
        font-weight: 600;
        transition: transform 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        color: white;
        text-decoration: none;
    }
</style>
{% endblock %}

{% block content %}
<section class="cart-container">
    <div class="container">
        {% csrf_token %}
        <div class="cart-header">
            <h1>🛒 Shopping Cart</h1>
            <p>Review and modify your items before checkout</p>
        </div>

        {% if cart and cart.items.all %}
            {% for item in cart.items.all %}
                <div class="cart-item" data-item-id="{{ item.id }}">
                    <div class="cart-item-image">
                        {% if item.product.image %}
                            <img src="{{ item.product.image.url }}" alt="{{ item.product.name }}">
                        {% else %}
                            <img src="https://via.placeholder.com/80x80/667eea/ffffff?text=Product" alt="{{ item.product.name }}">
                        {% endif %}
                    </div>

                    <div class="cart-item-details">
                        <div class="cart-item-name">{{ item.product.name }}</div>
                        <div class="cart-item-price">₨{{ item.product.price|floatformat:2 }}</div>
                    </div>

                    <div class="cart-item-actions">
                        <div class="quantity-control">
                            <button class="quantity-btn decrease" onclick="updateQuantity({{ item.id }}, {{ item.quantity|add:'-1' }})" {% if item.quantity <= 1 %}disabled{% endif %}>-</button>
                            <input type="number" class="quantity-input" value="{{ item.quantity }}" min="1" readonly>
                            <button class="quantity-btn increase" onclick="updateQuantity({{ item.id }}, {{ item.quantity|add:'1' }})">+</button>
                        </div>
                        <button class="remove-btn" onclick="removeItem({{ item.id }})">Remove</button>
                        <div style="margin-left: 15px; font-weight: 600;">₨{{ item.total_price|floatformat:2 }}</div>
                    </div>
                </div>
            {% endfor %}

            <div class="cart-summary">
                <h3>Order Summary</h3>
                <div style="display: flex; justify-content: space-between; margin: 15px 0;">
                    <span>Total Items:</span>
                    <span>{{ cart.total_items }}</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin: 15px 0; font-size: 20px; font-weight: 600;">
                    <span>Total:</span>
                    <span>₨{{ cart.total_price|floatformat:2 }}</span>
                </div>
                <button class="btn-primary" style="width: 100%; margin-top: 20px;">Proceed to Checkout</button>
                <button class="remove-btn" onclick="clearCart()" style="width: 100%; margin-top: 10px;">Clear Cart</button>
            </div>
        {% else %}
            <div class="empty-cart">
                <i class="fas fa-shopping-cart"></i>
                <h2>Your Cart is Empty</h2>
                <p>Looks like you haven't added anything to your cart yet.</p>
                <a href="{% url 'home' %}" class="btn-primary">Start Shopping</a>
            </div>
        {% endif %}
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
function updateQuantity(itemId, newQuantity) {
    if (newQuantity < 1) return;

    fetch('/cart/update-item/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
        },
        body: JSON.stringify({
            item_id: itemId,
            quantity: newQuantity
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error updating quantity: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating quantity');
    });
}

function removeItem(itemId) {
    if (confirm('Remove this item from your cart?')) {
        fetch('/cart/remove-item/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            },
            body: JSON.stringify({
                item_id: itemId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error removing item: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while removing item');
        });
    }
}

function clearCart() {
    if (confirm('Are you sure you want to clear your entire cart?')) {
        fetch('/cart/clear/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error clearing cart: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while clearing cart');
        });
    }
}
</script>
{% endblock %}
