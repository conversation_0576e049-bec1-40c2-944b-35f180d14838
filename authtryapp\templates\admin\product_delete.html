{% extends 'admin/base_admin.html' %}

{% block title %}Delete Product{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="mt-4">Delete Product</h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'admin_dashboard' %}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{% url 'admin_products' %}">Products</a></li>
            <li class="breadcrumb-item active">Delete Product</li>
        </ol>
    </nav>
    
    <div class="card mb-4">
        <div class="card-header bg-danger text-white">
            <i class="fas fa-exclamation-triangle me-1"></i>
            Confirm Deletion
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4 mb-4 mb-md-0">
                    <div class="text-center">
                        {% if product.image %}
                        <img src="{{ product.image.url }}" alt="{{ product.name }}" class="img-fluid rounded mb-3" style="max-height: 200px; max-width: 100%;">
                        {% else %}
                        <div class="bg-light d-flex align-items-center justify-content-center rounded" style="height: 200px;">
                            <i class="fas fa-box fa-4x text-muted"></i>
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-8">
                    <h2 class="mb-3">{{ product.name }}</h2>
                    
                    <div class="alert alert-danger mb-4">
                        <p class="mb-1"><strong>Are you sure you want to delete this product?</strong></p>
                        <p class="mb-0">This action cannot be undone. The product will be permanently removed from the system.</p>
                    </div>
                    
                    <dl class="row mb-4">
                        {% if product.category %}
                        <dt class="col-sm-3">Category</dt>
                        <dd class="col-sm-9">{{ product.category.name }}</dd>
                        {% endif %}
                        
                        <dt class="col-sm-3">Price</dt>
                        <dd class="col-sm-9">${{ product.price|default:"N/A" }}</dd>
                        
                        <dt class="col-sm-3">Stock Status</dt>
                        <dd class="col-sm-9">
                            <span class="badge {% if product.stock_status == 'available' %}bg-success{% elif product.stock_status == 'preorder' %}bg-warning{% else %}bg-danger{% endif %}">
                                {{ product.get_stock_status_display }}
                            </span>
                        </dd>
                    </dl>
                    
                    <form method="post">
                        {% csrf_token %}
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash"></i> Delete Product
                            </button>
                            <a href="{% url 'admin_products' %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 