
from django.contrib import admin
from django.db import models
from .models import (
    Product, Category, Brand, SectionHeading, ProductPromotion,
    HeroBanner, FeaturedProduct, SiteConfiguration, SaleCategory
)

@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ('name', 'brand', 'category', 'price', 'original_price', 'discount_percentage', 'rating', 'stock_status', 'is_sponsored', 'has_active_sales', 'created_at')
    search_fields = ('name', 'description', 'short_specs')
    list_filter = ('brand', 'category', 'stock_status', 'is_sponsored', 'has_gift', 'sale_categories', 'created_at')
    ordering = ('-created_at',)
    list_per_page = 20
    prepopulated_fields = {'slug': ('name',)}
    autocomplete_fields = ('brand', 'category')
    filter_horizontal = ('sale_categories',)
    actions = ['add_to_sale_category', 'remove_from_sale_category']

    def add_to_sale_category(self, request, queryset):
        """Custom admin action to add products to sale categories"""
        from django.shortcuts import render
        from django.http import HttpResponseRedirect
        from django.contrib import messages

        if 'apply' in request.POST:
            sale_category_id = request.POST.get('sale_category')
            if sale_category_id:
                try:
                    sale_category = SaleCategory.objects.get(id=sale_category_id)
                    count = 0
                    for product in queryset:
                        sale_category.products.add(product)
                        count += 1
                    messages.success(request, f'Added {count} products to "{sale_category.name}"')
                except SaleCategory.DoesNotExist:
                    messages.error(request, 'Sale category not found')
            return HttpResponseRedirect(request.get_full_path())

        sale_categories = SaleCategory.objects.filter(is_active=True)
        return render(request, 'admin/add_to_sale_category.html', {
            'products': queryset,
            'sale_categories': sale_categories,
        })

    add_to_sale_category.short_description = "Add selected products to sale category"

    def remove_from_sale_category(self, request, queryset):
        """Custom admin action to remove products from sale categories"""
        from django.shortcuts import render
        from django.http import HttpResponseRedirect
        from django.contrib import messages

        if 'apply' in request.POST:
            sale_category_id = request.POST.get('sale_category')
            if sale_category_id:
                try:
                    sale_category = SaleCategory.objects.get(id=sale_category_id)
                    count = 0
                    for product in queryset:
                        sale_category.products.remove(product)
                        count += 1
                    messages.success(request, f'Removed {count} products from "{sale_category.name}"')
                except SaleCategory.DoesNotExist:
                    messages.error(request, 'Sale category not found')
            return HttpResponseRedirect(request.get_full_path())

        # Get sale categories that have at least one of the selected products
        sale_categories = SaleCategory.objects.filter(products__in=queryset).distinct()
        return render(request, 'admin/remove_from_sale_category.html', {
            'products': queryset,
            'sale_categories': sale_categories,
        })

    remove_from_sale_category.short_description = "Remove selected products from sale category"

    # Add fieldsets for better organization
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'brand', 'category', 'description', 'short_specs', 'image')
        }),
        ('Pricing', {
            'fields': ('price', 'original_price', 'stock_status')
        }),
        ('Sales & Marketing', {
            'fields': ('rating', 'review_count', 'is_sponsored', 'has_gift', 'gift_description'),
            'description': 'Sales-related fields for enhanced product display'
        }),
    )

    def discount_percentage(self, obj):
        """Display discount percentage in admin"""
        if obj.is_on_sale:
            return f"{obj.discount_percentage:.0f}%"
        return "-"
    discount_percentage.short_description = "Discount"

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'created_at')
    search_fields = ('name', 'description')
    prepopulated_fields = {'slug': ('name',)}
    list_per_page = 20


@admin.register(Brand)
class BrandAdmin(admin.ModelAdmin):
    list_display = ('name', 'created_at')
    search_fields = ('name', 'description')
    prepopulated_fields = {'slug': ('name',)}
    list_per_page = 20


@admin.register(SectionHeading)
class SectionHeadingAdmin(admin.ModelAdmin):
    list_display = ('title', 'section_type', 'is_active', 'updated_at')
    list_filter = ('section_type', 'is_active')
    search_fields = ('title', 'subtitle')
    list_editable = ('is_active',)
    list_per_page = 20

    fieldsets = (
        ('Section Information', {
            'fields': ('section_type', 'title', 'subtitle')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
    )


@admin.register(ProductPromotion)
class ProductPromotionAdmin(admin.ModelAdmin):
    list_display = ('product', 'sale_price', 'discount_percentage', 'is_active', 'start_date', 'end_date')
    list_filter = ('is_featured', 'is_best_seller', 'is_new_arrival', 'is_top_deal', 'is_special_offer')
    search_fields = ('product__name',)
    autocomplete_fields = ('product',)
    list_per_page = 20

    fieldsets = (
        ('Product Information', {
            'fields': ('product', 'sale_price', 'discount_percentage')
        }),
        ('Promotion Type', {
            'fields': ('is_featured', 'is_best_seller', 'is_new_arrival', 'is_top_deal', 'is_special_offer')
        }),
        ('Promotion Period', {
            'fields': ('start_date', 'end_date')
        }),
    )


@admin.register(HeroBanner)
class HeroBannerAdmin(admin.ModelAdmin):
    list_display = ('title', 'get_image_info', 'link_type', 'link_target', 'order', 'is_active', 'created_at')
    list_filter = ('is_active', 'link_type', 'width_unit', 'height_unit', 'created_at')
    search_fields = ('title', 'description', 'brand_text', 'link_target')
    list_editable = ('order', 'is_active')
    ordering = ('order', '-created_at')
    list_per_page = 20
    readonly_fields = ('detected_width', 'detected_height', 'detected_aspect_ratio', 'get_image_info', 'get_redirect_url_preview')

    fieldsets = (
        ('Banner Content', {
            'fields': ('title', 'description', 'brand_text'),
            'description': 'Basic banner information and text content'
        }),
        ('Image & Visual Properties', {
            'fields': (
                'image', 'image_alt',
                ('image_width', 'width_unit'),
                ('image_height', 'height_unit'),
                ('background_gradient_start', 'background_gradient_end')
            ),
            'description': 'Upload banner image and set custom dimensions. Leave dimensions blank for auto-sizing.'
        }),
        ('Primary Button Configuration', {
            'fields': (
                'cta_label',
                'link_type',
                'link_target'
            ),
            'description': 'Configure the main call-to-action button. Link target format depends on link type.'
        }),
        ('Secondary Button (Optional)', {
            'fields': (
                'secondary_cta_label',
                'secondary_link_type',
                'secondary_link_target'
            ),
            'classes': ('collapse',),
            'description': 'Optional secondary button. Leave blank to hide.'
        }),
        ('Auto-detected Information', {
            'fields': ('detected_width', 'detected_height', 'detected_aspect_ratio', 'get_redirect_url_preview'),
            'classes': ('collapse',),
            'description': 'Automatically detected image properties and URL preview'
        }),
        ('Display Settings', {
            'fields': ('order', 'is_active'),
            'description': 'Control banner display order and visibility'
        }),
    )

    def get_image_info(self, obj):
        return obj.get_image_info()
    get_image_info.short_description = 'Dimensions'

    def get_redirect_url_preview(self, obj):
        """Show preview of generated URLs"""
        primary_url = obj.get_redirect_url('primary')
        preview = f"Primary: {primary_url}"

        if obj.has_secondary_button():
            secondary_url = obj.get_redirect_url('secondary')
            preview += f" | Secondary: {secondary_url}"

        return preview
    get_redirect_url_preview.short_description = 'URL Preview'

    def save_model(self, request, obj, form, change):
        # Validate dimensions
        if obj.image_width and obj.image_width < 1:
            obj.image_width = None
        if obj.image_height and obj.image_height < 1:
            obj.image_height = None
        super().save_model(request, obj, form, change)

    def get_queryset(self, request):
        return super().get_queryset(request).order_by('order', '-created_at')

    class Media:
        js = ('admin/js/hero_banner_admin.js',)
        css = {
            'all': ('admin/css/hero_banner_admin.css',)
        }


@admin.register(FeaturedProduct)
class FeaturedProductAdmin(admin.ModelAdmin):
    list_display = ('product', 'section', 'display_order', 'is_active', 'created_at')
    list_filter = ('section', 'is_active', 'created_at')
    search_fields = ('product__name',)
    list_editable = ('display_order', 'is_active')
    autocomplete_fields = ('product',)
    ordering = ('section', 'display_order')
    list_per_page = 20

    fieldsets = (
        ('Product Selection', {
            'fields': ('product', 'section')
        }),
        ('Display Settings', {
            'fields': ('display_order', 'is_active')
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('product')


@admin.register(SiteConfiguration)
class SiteConfigurationAdmin(admin.ModelAdmin):
    list_display = ('site_name', 'site_tagline', 'updated_at')

    fieldsets = (
        ('Site Information', {
            'fields': ('site_name', 'site_tagline')
        }),
        ('Hero Section Settings', {
            'fields': (
                'hero_auto_slide_interval',
                ('hero_show_arrows', 'hero_show_dots')
            )
        }),
        ('Product Display Settings', {
            'fields': (
                ('products_per_row_desktop', 'products_per_row_tablet', 'products_per_row_mobile'),
            )
        }),
    )

    def has_add_permission(self, request):
        # Only allow one configuration
        return not SiteConfiguration.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # Don't allow deletion of the configuration
        return False


@admin.register(SaleCategory)
class SaleCategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'sale_type_display', 'badge_preview', 'is_active', 'is_featured', 'is_currently_active', 'start_date', 'end_date', 'product_count')
    search_fields = ('name', 'badge_text', 'description')
    list_filter = ('sale_type', 'is_active', 'is_featured', 'start_date', 'end_date')
    ordering = ('-is_featured', '-start_date')
    list_per_page = 20
    prepopulated_fields = {'slug': ('name',)}
    actions = ['activate_sales', 'deactivate_sales', 'mark_as_featured', 'unmark_as_featured']

    class Media:
        css = {
            'all': ('admin/css/sale_category_admin.css',)
        }
        js = ('admin/js/sale_category_admin.js',)

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'sale_type', 'description')
        }),
        ('Sale Details', {
            'fields': ('discount_percentage', 'discount_amount', 'free_item_description', 'buy_quantity', 'get_quantity'),
            'description': 'Fill in the relevant fields based on the sale type selected above.'
        }),
        ('Display Settings', {
            'fields': ('badge_text', 'badge_color')
        }),
        ('Timing & Status', {
            'fields': ('start_date', 'end_date', 'is_active', 'is_featured')
        }),
    )

    def product_count(self, obj):
        """Show number of products in this sale category"""
        return obj.products.count()
    product_count.short_description = 'Products'

    def is_currently_active(self, obj):
        """Show if sale is currently active"""
        return obj.is_currently_active()
    is_currently_active.boolean = True
    is_currently_active.short_description = 'Currently Active'

    def sale_type_display(self, obj):
        """Display sale type with icon"""
        icons = {
            'percentage': '📊',
            'fixed_amount': '💰',
            'free_item': '🎁',
            'buy_x_get_y': '🔄',
            'bundle': '📦',
            'clearance': '🏷️',
            'flash_sale': '⚡',
            'seasonal': '🌟',
            'gift_with_purchase': '🎀',
            'free_shipping': '🚚',
        }
        icon = icons.get(obj.sale_type, '🏷️')
        return f"{icon} {obj.get_sale_type_display()}"
    sale_type_display.short_description = 'Sale Type'

    def badge_preview(self, obj):
        """Show a preview of the badge"""
        return f'<span style="background-color: {obj.badge_color}; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px; font-weight: bold;">{obj.badge_text}</span>'
    badge_preview.allow_tags = True
    badge_preview.short_description = 'Badge Preview'

    # Custom admin actions
    def activate_sales(self, request, queryset):
        """Activate selected sale categories"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'Successfully activated {updated} sale categories.')
    activate_sales.short_description = "Activate selected sale categories"

    def deactivate_sales(self, request, queryset):
        """Deactivate selected sale categories"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'Successfully deactivated {updated} sale categories.')
    deactivate_sales.short_description = "Deactivate selected sale categories"

    def mark_as_featured(self, request, queryset):
        """Mark selected sale categories as featured"""
        updated = queryset.update(is_featured=True)
        self.message_user(request, f'Successfully marked {updated} sale categories as featured.')
    mark_as_featured.short_description = "Mark as featured"

    def unmark_as_featured(self, request, queryset):
        """Remove featured status from selected sale categories"""
        updated = queryset.update(is_featured=False)
        self.message_user(request, f'Successfully removed featured status from {updated} sale categories.')
    unmark_as_featured.short_description = "Remove featured status"

    def changelist_view(self, request, extra_context=None):
        """Add extra context for the changelist template"""
        extra_context = extra_context or {}

        # Add statistics
        extra_context['active_count'] = SaleCategory.objects.filter(is_active=True).count()
        extra_context['featured_count'] = SaleCategory.objects.filter(is_featured=True).count()
        extra_context['total_products_in_sales'] = SaleCategory.objects.filter(
            is_active=True
        ).aggregate(
            total=models.Count('products', distinct=True)
        )['total'] or 0

        return super().changelist_view(request, extra_context=extra_context)
