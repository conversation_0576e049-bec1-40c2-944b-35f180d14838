{% extends 'admin/base_admin.html' %}

{% block title %}Hero Banners Management{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mt-4 mb-4">
        <h1>🎨 Hero Banners Management</h1>
        <a href="{% url 'admin_hero_banner_add' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add New Banner
        </a>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card bg-gradient-primary text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-images fa-2x me-3"></i>
                        <div>
                            <h5 class="mb-1">{{ banners.count }} Total Banners</h5>
                            <small>Manage your homepage carousel</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card bg-gradient-info text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-cog fa-2x me-3"></i>
                        <div>
                            <h5 class="mb-1">Carousel Settings</h5>
                            <a href="{% url 'admin_site_config' %}" class="text-white text-decoration-none">
                                <small>Configure timing & navigation <i class="fas fa-arrow-right"></i></small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Banners Grid -->
    {% if banners %}
    <div class="row">
        {% for banner in banners %}
        <div class="col-lg-6 col-xl-4 mb-4">
            <div class="card h-100 shadow-sm border-0">
                <!-- Banner Preview -->
                <div class="position-relative">
                    <div class="banner-preview" style="height: 200px; background: linear-gradient(135deg, {{ banner.background_gradient_start }} 0%, {{ banner.background_gradient_end }} 100%); border-radius: 8px 8px 0 0;">
                        <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center text-white p-3">
                            <div class="text-center">
                                {% if banner.brand_text %}
                                <small class="d-block opacity-75 mb-1">{{ banner.brand_text }}</small>
                                {% endif %}
                                <h6 class="mb-2">{{ banner.title|truncatechars:40 }}</h6>
                                {% if banner.image %}
                                <img src="{{ banner.image.url }}" alt="{{ banner.image_alt }}" class="img-fluid mt-2" style="max-height: 60px; max-width: 100px; object-fit: contain;">
                                {% endif %}
                            </div>
                        </div>
                        <!-- Status Badge -->
                        <div class="position-absolute top-0 end-0 p-2">
                            <span class="badge {% if banner.is_active %}bg-success{% else %}bg-secondary{% endif %} px-3">
                                {% if banner.is_active %}
                                <i class="fas fa-eye"></i> Active
                                {% else %}
                                <i class="fas fa-eye-slash"></i> Inactive
                                {% endif %}
                            </span>
                        </div>
                        <!-- Display Order -->
                        <div class="position-absolute bottom-0 start-0 p-2">
                            <span class="badge bg-dark bg-opacity-75">
                                <i class="fas fa-sort-numeric-up"></i> Order: {{ banner.display_order }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Banner Details -->
                <div class="card-body">
                    <h6 class="card-title mb-2">{{ banner.title }}</h6>
                    <p class="card-text text-muted small mb-3">{{ banner.subtitle|truncatechars:80 }}</p>
                    
                    <!-- Button Preview -->
                    <div class="mb-3">
                        <div class="d-flex gap-2 flex-wrap">
                            {% if banner.primary_button_text %}
                            <span class="badge bg-primary">{{ banner.primary_button_text }}</span>
                            {% endif %}
                            {% if banner.secondary_button_text %}
                            <span class="badge bg-outline-primary">{{ banner.secondary_button_text }}</span>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Meta Info -->
                    <div class="row text-muted small mb-3">
                        <div class="col-6">
                            <i class="fas fa-calendar"></i> {{ banner.created_at|date:"M d, Y" }}
                        </div>
                        <div class="col-6">
                            <i class="fas fa-clock"></i> {{ banner.updated_at|date:"H:i" }}
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="btn-group btn-group-sm">
                            <a href="{% url 'admin_hero_banner_edit' banner.id %}" class="btn btn-outline-primary" title="Edit Banner">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            <a href="{% url 'admin_hero_banner_delete' banner.id %}" class="btn btn-outline-danger" title="Delete Banner">
                                <i class="fas fa-trash"></i>
                            </a>
                        </div>
                        <small class="text-muted">ID: {{ banner.id }}</small>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination would go here if needed -->
    
    {% else %}
    <!-- Empty State -->
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-images fa-4x text-muted mb-4"></i>
            <h3>No Hero Banners Yet</h3>
            <p class="text-muted mb-4">Create stunning carousel banners to showcase your featured products and promotions on the homepage.</p>
            <a href="{% url 'admin_hero_banner_add' %}" class="btn btn-primary btn-lg">
                <i class="fas fa-plus"></i> Create Your First Banner
            </a>
        </div>
    </div>
    {% endif %}

    <!-- Help Section -->
    <div class="card mt-4 border-info">
        <div class="card-header bg-info text-white">
            <i class="fas fa-info-circle"></i> Quick Tips
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <h6><i class="fas fa-palette text-info"></i> Design Tips</h6>
                    <ul class="small">
                        <li>Use contrasting gradient colors</li>
                        <li>Keep titles short and impactful</li>
                        <li>Upload high-quality images (800x600px)</li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h6><i class="fas fa-sort text-info"></i> Organization</h6>
                    <ul class="small">
                        <li>Lower display order = appears first</li>
                        <li>Use active/inactive to control visibility</li>
                        <li>Test on different screen sizes</li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h6><i class="fas fa-link text-info"></i> Button Links</h6>
                    <ul class="small">
                        <li>Use #featured-products for product sections</li>
                        <li>Link to specific categories or products</li>
                        <li>External links should start with http://</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.bg-gradient-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.card {
    transition: transform 0.2s ease-in-out;
}
.card:hover {
    transform: translateY(-2px);
}
.banner-preview {
    position: relative;
    overflow: hidden;
}
.badge {
    font-size: 0.75rem;
}
</style>
{% endblock %}
