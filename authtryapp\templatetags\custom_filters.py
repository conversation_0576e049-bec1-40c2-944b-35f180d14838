from django import template

register = template.Library()

@register.filter
def get_item(dictionary, key):
    """
    Custom template filter to access dictionary items by key
    Usage: {{ dictionary|get_item:key }}
    """
    if dictionary is None:
        return None

    # Convert key to int if it's a string representation of an integer
    if isinstance(key, str) and key.isdigit():
        key = int(key)

    return dictionary.get(key)

@register.filter
def sub(value, arg):
    """
    Subtracts the arg from the value
    Usage: {{ value|sub:arg }}
    """
    try:
        return float(value) - float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def process_button_url(url):
    """Process button URLs to handle product links"""
    if not url:
        return '#'

    # Handle product links in format: product/product-name/
    if url.startswith('product/') and url.endswith('/'):
        return f"/{url}"

    # Handle anchor links
    if url.startswith('#'):
        return url

    # Handle full URLs
    if url.startswith('http'):
        return url

    # Default: treat as relative URL
    return f"/{url.lstrip('/')}"

@register.filter
def get_primary_redirect_url(banner):
    """Get primary button redirect URL"""
    try:
        return banner.get_redirect_url('primary')
    except:
        return '#'

@register.filter
def get_secondary_redirect_url(banner):
    """Get secondary button redirect URL"""
    try:
        return banner.get_redirect_url('secondary')
    except:
        return '#'

@register.filter
def get_primary_button_text(banner):
    """Get primary button text"""
    try:
        return banner.get_cta_button_text('primary')
    except:
        return 'Shop Now'

@register.filter
def get_secondary_button_text(banner):
    """Get secondary button text"""
    try:
        return banner.get_cta_button_text('secondary')
    except:
        return 'Learn More'

@register.filter
def has_secondary_button(banner):
    """Check if banner has secondary button"""
    try:
        return banner.has_secondary_button()
    except:
        return False
