{% extends 'base_new.html' %}
{% load static %}

{% block title %}Register - iTechStore{% endblock %}

{% block extra_css %}
<style>
    /* Additional styles for registration page */
    .register-container {
        padding: 60px 0;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: calc(100vh - 300px);
    }
    
    .register-form {
        background-color: white;
        border-radius: 10px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        padding: 40px;
        width: 100%;
        max-width: 500px;
    }
    
    .register-header {
        text-align: center;
        margin-bottom: 30px;
    }
    
    .register-header h1 {
        font-size: 28px;
        margin-bottom: 10px;
        color: #333;
    }
    
    .register-header p {
        color: #777;
        font-size: 16px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #333;
    }
    
    .form-group input {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 16px;
        transition: border-color 0.3s;
    }
    
    .form-group input:focus {
        border-color: #4dabf7;
        outline: none;
    }
    
    .form-row {
        display: flex;
        gap: 20px;
    }
    
    .form-row .form-group {
        flex: 1;
    }
    
    .register-button {
        width: 100%;
        padding: 14px;
        background: linear-gradient(to right, #4dabf7, #0c8599);
        color: white;
        border: none;
        border-radius: 5px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: opacity 0.3s;
    }
    
    .register-button:hover {
        opacity: 0.9;
    }
    
    .register-footer {
        text-align: center;
        margin-top: 25px;
        color: #777;
    }
    
    .register-footer a {
        color: #4dabf7;
        font-weight: 500;
        text-decoration: none;
    }
    
    .register-footer a:hover {
        text-decoration: underline;
    }
    
    .alert {
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
        color: white;
    }
    
    .alert-danger {
        background-color: #f44336;
    }
    
    .alert-success {
        background-color: #4caf50;
    }
    
    .terms {
        margin-top: 20px;
        font-size: 14px;
        color: #777;
        text-align: center;
    }
    
    .terms a {
        color: #4dabf7;
        text-decoration: none;
    }
    
    .terms a:hover {
        text-decoration: underline;
    }
    
    .error-text {
        color: #f44336;
        font-size: 14px;
        margin-top: 5px;
    }
</style>
{% endblock %}

{% block content %}
<!-- Registration Section -->
<section class="register-container">
    <div class="register-form">
        <div class="register-header">
            <h1>Create an Account</h1>
            <p>Join iTechStore for exclusive deals and faster checkout</p>
        </div>
        
        {% if messages %}
            {% for message in messages %}
                <div class="alert {% if message.tags == 'error' %}alert-danger{% else %}alert-success{% endif %}">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
        
        {% if form.errors %}
            <div class="alert alert-danger">
                Please correct the errors below.
            </div>
        {% endif %}
        
        <form method="post" action="{% url 'register' %}">
            {% csrf_token %}
            <div class="form-row">
                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" id="username" name="username" placeholder="Choose a username" required>
                    {% if form.username.errors %}
                        <div class="error-text">{{ form.username.errors.0 }}</div>
                    {% endif %}
                </div>
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" placeholder="Enter your email" required>
                    {% if form.email.errors %}
                        <div class="error-text">{{ form.email.errors.0 }}</div>
                    {% endif %}
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="password1">Password</label>
                    <input type="password" id="password1" name="password1" placeholder="Create a password" required>
                    {% if form.password1.errors %}
                        <div class="error-text">{{ form.password1.errors.0 }}</div>
                    {% endif %}
                </div>
                <div class="form-group">
                    <label for="password2">Confirm Password</label>
                    <input type="password" id="password2" name="password2" placeholder="Confirm your password" required>
                    {% if form.password2.errors %}
                        <div class="error-text">{{ form.password2.errors.0 }}</div>
                    {% endif %}
                </div>
            </div>
            
            <button type="submit" class="register-button">Create Account</button>
            
            <div class="terms">
                By creating an account, you agree to our <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a>.
            </div>
            
            <div class="register-footer">
                <p>Already have an account? <a href="{% url 'login' %}">Sign in</a></p>
            </div>
        </form>
    </div>
</section>
{% endblock %}
