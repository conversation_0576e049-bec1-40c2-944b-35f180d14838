/* Sale Category Admin Styling */

/* Make the Sale Categories section more prominent */
.app-authtryapp .model-salecategory {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    margin-bottom: 10px;
}

.app-authtryapp .model-salecategory a {
    color: white !important;
    font-weight: bold;
    text-decoration: none;
}

.app-authtryapp .model-salecategory .addlink {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    padding: 4px 8px;
    margin-left: 10px;
}

.app-authtryapp .model-salecategory .changelink {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    padding: 4px 8px;
    margin-left: 10px;
}

/* Sale category list styling */
.sale-category-list .field-badge_preview {
    text-align: center;
}

.sale-category-list .field-is_currently_active img {
    width: 16px;
    height: 16px;
}

.sale-category-list .field-sale_type_display {
    font-size: 14px;
}

/* Form styling */
.sale-category-form .form-row {
    margin-bottom: 15px;
}

.sale-category-form .field-badge_color input {
    width: 80px;
    height: 40px;
    border: 2px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
}

.sale-category-form .field-sale_type select {
    min-width: 200px;
}

/* Fieldset styling */
.sale-category-form fieldset {
    border: 1px solid #e1e4e8;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
}

.sale-category-form fieldset h2 {
    background: #f6f8fa;
    margin: -15px -15px 15px -15px;
    padding: 10px 15px;
    border-bottom: 1px solid #e1e4e8;
    border-radius: 6px 6px 0 0;
    font-size: 14px;
    font-weight: 600;
    color: #24292e;
}

/* Help text styling */
.sale-category-form .help {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
    font-style: italic;
}

/* Badge preview in form */
.badge-preview-container {
    margin-top: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.badge-preview-label {
    font-weight: bold;
    margin-bottom: 5px;
    display: block;
}

/* Action buttons styling */
.sale-category-actions {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
}

.sale-category-actions .button {
    margin-right: 10px;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 500;
}

/* Status indicators */
.status-active {
    color: #28a745;
    font-weight: bold;
}

.status-inactive {
    color: #dc3545;
    font-weight: bold;
}

.status-featured {
    color: #ffc107;
    font-weight: bold;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .sale-category-form fieldset {
        padding: 10px;
    }
    
    .sale-category-form fieldset h2 {
        margin: -10px -10px 10px -10px;
        padding: 8px 10px;
    }
}

/* Admin dashboard module styling */
.module.sale-categories {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.module.sale-categories h2 {
    color: white;
}

.module.sale-categories a {
    color: rgba(255, 255, 255, 0.9);
}

.module.sale-categories a:hover {
    color: white;
    text-decoration: underline;
}

/* Make sale categories stand out in the app list */
.app-authtryapp .model-salecategory::before {
    content: "🏷️ ";
    font-size: 16px;
    margin-right: 5px;
}

/* Enhanced table styling for sale categories */
.change-list .sale-category-row {
    transition: background-color 0.2s ease;
}

.change-list .sale-category-row:hover {
    background-color: #f8f9fa;
}

.change-list .sale-category-row.featured {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
}

.change-list .sale-category-row.inactive {
    opacity: 0.6;
}

/* Highlight important fields */
.field-name {
    font-weight: bold;
}

.field-is_featured .true {
    color: #ffc107;
}

.field-is_active .true {
    color: #28a745;
}

.field-is_active .false {
    color: #dc3545;
}
