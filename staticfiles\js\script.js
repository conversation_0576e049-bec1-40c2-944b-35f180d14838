// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Cart functionality
    const addToCartButtons = document.querySelectorAll('.add-to-cart:not(.disabled)');
    const cartCounts = document.querySelectorAll('.cart-count, .floating-cart-count');

    addToCartButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            // Get product ID from data attribute
            const productId = this.dataset.productId;
            if (!productId) {
                console.error('Product ID not found');
                return;
            }

            // Get CSRF token
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            // Send AJAX request to add product to cart
            fetch(`/cart/add/${productId}/`, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': csrfToken
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.status === 'success') {
                    // Update all cart count elements
                    cartCounts.forEach(count => {
                        count.textContent = data.cart_total;
                    });

                    // Animation effect
                    const originalText = button.textContent;
                    const originalColor = button.style.backgroundColor;

                    button.textContent = 'Added!';
                    button.style.backgroundColor = '#28a745';

                    setTimeout(() => {
                        button.textContent = originalText;
                        button.style.backgroundColor = originalColor;
                    }, 1500);
                } else {
                    console.error('Error adding to cart:', data.message);
                }
            })
            .catch(error => {
                console.error('Error adding to cart:', error);
            });
        });
    });

    // Enhanced Navbar Effect
    window.addEventListener('scroll', function() {
        const mainNav = document.querySelector('.main-nav');
        if (window.scrollY > 50) {
            mainNav.style.backgroundColor = 'rgba(255, 255, 255, 1)';
            mainNav.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.1)';
        } else {
            mainNav.style.backgroundColor = 'rgba(255, 255, 255, 0.95)';
            mainNav.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';
        }
    });

    // Hero slider functionality
    const heroSlides = document.querySelectorAll('.hero-slide');
    const heroDots = document.querySelectorAll('.dot');
    const prevBtn = document.querySelector('.prev-btn');
    const nextBtn = document.querySelector('.next-btn');
    let currentSlide = 0;
    let slideInterval;

    // Function to show a specific slide with transition effect
    function showSlide(index) {
        if (index === currentSlide) return; // Don't do anything if it's the same slide

        // Get the transition effect for the next slide
        const nextEffect = heroSlides[index].getAttribute('data-effect') || 'slide';

        // Remove all effect classes from all slides
        heroSlides.forEach(slide => {
            slide.classList.remove('prev', 'slide-up', 'fade-in', 'zoom-out');
        });

        // Apply the appropriate effect class to the current slide before transitioning
        if (nextEffect === 'slide') {
            // For horizontal slide effect
            heroSlides[currentSlide].classList.add('prev');
        } else if (nextEffect === 'slide-up') {
            // For slide up effect, no special class needed for the current slide
        } else if (nextEffect === 'fade') {
            // For fade effect, no special class needed for the current slide
        } else if (nextEffect === 'zoom') {
            // For zoom effect, no special class needed for the current slide
        }

        // Remove active class from all dots
        heroDots.forEach(dot => {
            dot.classList.remove('active');
        });

        // Show the selected slide with a slight delay to ensure CSS transitions work
        setTimeout(() => {
            // Remove active class from current slide
            heroSlides[currentSlide].classList.remove('active');

            // Add active class to the new slide
            heroSlides[index].classList.add('active');

            // After transition completes, clean up classes
            setTimeout(() => {
                heroSlides.forEach((slide, i) => {
                    if (i !== index) {
                        slide.classList.remove('prev', 'active', 'slide-up', 'fade-in', 'zoom-out');
                    }
                });
            }, 1200); // Increased from 800ms to 1200ms for slower transitions

            // Add a subtle animation to the image in the active slide
            const img = heroSlides[index].querySelector('img');
            if (img) {
                // Apply a subtle zoom effect to the image
                img.style.transition = 'transform 15s ease'; // Increased to 15s for slower effect
                img.style.transform = 'scale(1.08)'; // Slightly larger scale for more noticeable effect

                // Reset the transform after the transition
                setTimeout(() => {
                    img.style.transition = 'transform 0.8s ease';
                    img.style.transform = 'scale(1)';
                }, 14500); // Adjusted timing to match the longer transition
            }

            // Slide counter removed

            // Update current slide index
            currentSlide = index;
        }, 50);

        // Add active class to the corresponding dot
        heroDots[index].classList.add('active');

        // Update the transition effect button to show the current effect
        updateEffectButton(nextEffect);
    }

    // Function to update the effect button text
    function updateEffectButton(effect) {
        const effectButton = document.querySelector('.effect-btn');
        if (effectButton) {
            effectButton.textContent = 'Effect: ' + effect.charAt(0).toUpperCase() + effect.slice(1).replace('-', ' ');
        }
    }

    // Function to show the next slide
    function nextSlide() {
        let nextIndex = currentSlide + 1;
        if (nextIndex >= heroSlides.length) {
            nextIndex = 0;
        }
        showSlide(nextIndex);
    }

    // Function to show the previous slide
    function prevSlide() {
        let prevIndex = currentSlide - 1;
        if (prevIndex < 0) {
            prevIndex = heroSlides.length - 1;
        }
        showSlide(prevIndex);
    }

    // Set up event listeners for dots
    if (heroDots.length > 0) {
        heroDots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                showSlide(index);
                resetInterval();
            });
        });
    }

    // Set up event listeners for prev/next buttons
    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            prevSlide();
            resetInterval();
        });
    }

    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            nextSlide();
            resetInterval();
        });
    }

    // Function to reset the interval
    function resetInterval() {
        clearInterval(slideInterval);
        startInterval();
    }

    // Function to start the interval
    function startInterval() {
        slideInterval = setInterval(nextSlide, 12000); // Increased to 12 seconds for slower transitions
    }

    // Start the automatic slideshow if we have slides
    if (heroSlides.length > 0) {
        startInterval();

        // Add swipe functionality for mobile
        let touchStartX = 0;
        let touchEndX = 0;

        const slider = document.querySelector('.hero-slider');

        slider.addEventListener('touchstart', (e) => {
            touchStartX = e.changedTouches[0].screenX;
        }, false);

        slider.addEventListener('touchend', (e) => {
            touchEndX = e.changedTouches[0].screenX;
            handleSwipe();
        }, false);

        function handleSwipe() {
            const swipeThreshold = 50; // Minimum distance for swipe

            if (touchEndX < touchStartX - swipeThreshold) {
                // Swipe left - show next slide
                nextSlide();
                resetInterval();
            } else if (touchEndX > touchStartX + swipeThreshold) {
                // Swipe right - show previous slide
                prevSlide();
                resetInterval();
            }
        }

        // Handle effect button clicks
        const effectButtons = document.querySelectorAll('.effect-options button');
        const effectBtn = document.querySelector('.effect-btn');

        effectButtons.forEach(button => {
            button.addEventListener('click', function() {
                const effect = this.getAttribute('data-effect');

                // Update all slides to use this effect
                heroSlides.forEach(slide => {
                    slide.setAttribute('data-effect', effect);

                    // Remove all effect classes first
                    slide.classList.remove('slide-up', 'fade-in', 'zoom-out');

                    // Add the appropriate class based on the effect
                    if (effect === 'slide-up') {
                        slide.classList.add('slide-up');
                    } else if (effect === 'fade') {
                        slide.classList.add('fade-in');
                    } else if (effect === 'zoom') {
                        slide.classList.add('zoom-out');
                    }
                    // For 'slide', no additional class is needed
                });

                // Update the effect button text
                effectBtn.textContent = 'Effect: ' + effect.charAt(0).toUpperCase() + effect.slice(1).replace('-', ' ');
                effectBtn.setAttribute('data-effect', effect);
            });
        });
    }

    // Search functionality
    const searchForm = document.getElementById('search-form');
    const searchInput = document.getElementById('search-input');
    const searchResults = document.getElementById('search-results');

    // Sample product data (in a real app, this would come from the database)
    const products = [
        { id: 1, name: 'Lenovo Ideapad Slim 5', price: '₨. 79,999', category: 'laptops' },
        { id: 2, name: 'MacBook Air M2', price: '₨. 1,19,999', category: 'laptops' },
        { id: 3, name: 'ASUS ROG Zephyrus', price: '₨. 1,49,999', category: 'gaming' },
        { id: 4, name: 'Dell XPS 13', price: '₨. 1,29,999', category: 'laptops' },
        { id: 5, name: 'iPhone 14 Pro', price: '₨. 1,39,999', category: 'mobile' },
        { id: 6, name: 'Samsung Galaxy S23', price: '₨. 1,09,999', category: 'mobile' },
        { id: 7, name: 'Sony WH-1000XM4', price: '₨. 29,999', category: 'audio' },
        { id: 8, name: 'Amazon Echo Dot', price: '₨. 4,999', category: 'smart-home' },
        { id: 9, name: 'HP Pavilion Gaming', price: '₨. 89,999', category: 'gaming' },
        { id: 10, name: 'iPad Pro', price: '₨. 89,999', category: 'mobile' }
    ];

    // Search function
    function performSearch(query) {
        // Clear previous results
        searchResults.innerHTML = '';

        if (query.length < 2) {
            searchResults.classList.remove('active');
            return;
        }

        // Filter products based on search query
        const filteredProducts = products.filter(product =>
            product.name.toLowerCase().includes(query.toLowerCase()) ||
            product.category.toLowerCase().includes(query.toLowerCase())
        );

        // Display results
        if (filteredProducts.length > 0) {
            filteredProducts.forEach(product => {
                const resultItem = document.createElement('div');
                resultItem.className = 'search-result-item';
                resultItem.innerHTML = `
                    <div class="product-name">${product.name}</div>
                    <div class="product-price">${product.price}</div>
                    <div class="product-category">Category: ${product.category}</div>
                `;

                // Add click event to navigate to product
                resultItem.addEventListener('click', () => {
                    alert(`Navigating to ${product.name} product page`);
                    // In a real app: window.location.href = `/product/${product.id}/`;
                });

                searchResults.appendChild(resultItem);
            });

            searchResults.classList.add('active');
        } else {
            const noResults = document.createElement('div');
            noResults.className = 'search-result-item';
            noResults.textContent = 'No products found';
            searchResults.appendChild(noResults);
            searchResults.classList.add('active');
        }
    }

    // Event listeners for search
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            performSearch(searchInput.value);
        });
    }

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            performSearch(this.value);
        });

        // Close search results when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
                searchResults.classList.remove('active');
            }
        });
    }

    // Mobile search functionality
    const mobileSearchForm = document.getElementById('mobile-search-form');
    const mobileSearchInput = document.getElementById('mobile-search-input');
    const mobileSearchResults = document.getElementById('mobile-search-results');

    // Mobile search function
    function performMobileSearch(query) {
        // Clear previous results
        mobileSearchResults.innerHTML = '';

        if (query.length < 2) {
            mobileSearchResults.classList.remove('active');
            return;
        }

        // Filter products based on search query
        const filteredProducts = products.filter(product =>
            product.name.toLowerCase().includes(query.toLowerCase()) ||
            product.category.toLowerCase().includes(query.toLowerCase())
        );

        // Display results
        if (filteredProducts.length > 0) {
            filteredProducts.forEach(product => {
                const resultItem = document.createElement('div');
                resultItem.className = 'search-result-item';
                resultItem.innerHTML = `
                    <div class="product-name">${product.name}</div>
                    <div class="product-price">${product.price}</div>
                    <div class="product-category">Category: ${product.category}</div>
                `;

                // Add click event to navigate to product
                resultItem.addEventListener('click', () => {
                    alert(`Navigating to ${product.name} product page`);
                    // In a real app: window.location.href = `/product/${product.id}/`;

                    // Close mobile menu
                    document.querySelector('.mobile-menu').classList.remove('active');
                });

                mobileSearchResults.appendChild(resultItem);
            });

            mobileSearchResults.classList.add('active');
        } else {
            const noResults = document.createElement('div');
            noResults.className = 'search-result-item';
            noResults.textContent = 'No products found';
            mobileSearchResults.appendChild(noResults);
            mobileSearchResults.classList.add('active');
        }
    }

    // Event listeners for mobile search
    if (mobileSearchForm) {
        mobileSearchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            performMobileSearch(mobileSearchInput.value);
        });
    }

    if (mobileSearchInput) {
        mobileSearchInput.addEventListener('input', function() {
            performMobileSearch(this.value);
        });

        // Close search results when clicking outside
        document.addEventListener('click', function(e) {
            if (!mobileSearchInput.contains(e.target) && !mobileSearchResults.contains(e.target)) {
                mobileSearchResults.classList.remove('active');
            }
        });
    }

    // Category navigation functionality - Simplified approach
    const categoryLinks = document.querySelectorAll('[data-category]');

    categoryLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const category = this.getAttribute('data-category');

            // Log the category for debugging
            console.log('Navigating to category:', category);

            // Show category selection in a more direct way
            displayCategory(category);
        });
    });

    // Function to navigate to category page
    function displayCategory(category) {
        try {
            // Construct the category URL
            const categorySlug = category.toLowerCase().replace(/\s+/g, '-');
            const categoryUrl = `/category/${categorySlug}/`;

            // Navigate to the category page
            window.location.href = categoryUrl;
        } catch (error) {
            console.error("Error navigating to category:", error);
            alert("Sorry, there was an error navigating to the category. Please try again later.");
        }
    }

    // No longer need the createProductCard function since we're navigating to real category pages

    // Mobile menu functionality
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const mobileMenu = document.querySelector('.mobile-menu');
    const mobileMenuClose = document.querySelector('.mobile-menu-close');
    const mobileDropdowns = document.querySelectorAll('.mobile-dropdown > a');

    // Create overlay element
    const overlay = document.createElement('div');
    overlay.className = 'overlay';
    document.body.appendChild(overlay);

    // Toggle mobile menu
    mobileMenuToggle.addEventListener('click', function() {
        mobileMenu.classList.add('active');
        overlay.classList.add('active');
        document.body.style.overflow = 'hidden'; // Prevent scrolling when menu is open
    });

    // Close mobile menu
    mobileMenuClose.addEventListener('click', function() {
        mobileMenu.classList.remove('active');
        overlay.classList.remove('active');
        document.body.style.overflow = ''; // Restore scrolling
    });

    // Close menu when clicking overlay
    overlay.addEventListener('click', function() {
        mobileMenu.classList.remove('active');
        overlay.classList.remove('active');
        document.body.style.overflow = ''; // Restore scrolling
    });

    // Enhanced Toggle mobile dropdowns with smooth animation
    mobileDropdowns.forEach(dropdown => {
        dropdown.addEventListener('click', function(e) {
            e.preventDefault();
            const parent = this.parentElement;
            const dropdownContent = parent.querySelector('.mobile-dropdown-content');

            // Close all other dropdowns
            document.querySelectorAll('.mobile-dropdown-content.active').forEach(item => {
                if (item !== dropdownContent) {
                    item.classList.remove('active');
                }
            });

            // Toggle current dropdown
            dropdownContent.classList.toggle('active');

            // Toggle icon rotation
            const icon = this.querySelector('i');
            icon.style.transition = 'transform 0.3s';

            if (dropdownContent.classList.contains('active')) {
                icon.style.transform = 'rotate(180deg)';
            } else {
                icon.style.transform = 'rotate(0)';
            }
        });
    });

    // Add event listeners to mobile category links
    const mobileCategoryLinks = document.querySelectorAll('.mobile-dropdown-content a[data-category]');
    mobileCategoryLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const category = this.getAttribute('data-category');

            // Close the mobile menu
            mobileMenu.classList.remove('active');
            overlay.classList.remove('active');
            document.body.style.overflow = '';

            // Navigate to the category
            displayCategory(category);
        });
    });
});
