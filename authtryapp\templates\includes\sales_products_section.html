<!-- Sales Products Section -->
<section class="sales-section" id="sales-products">
    <div class="container">
        <!-- Section Header -->
        <div class="sales-header">
            {% if section_title %}
                <h2 class="sales-title">{{ section_title }}</h2>
            {% else %}
                <h2 class="sales-title">Sales Products</h2>
            {% endif %}
            
            {% if section_subtitle %}
                <p class="sales-subtitle">{{ section_subtitle }}</p>
            {% else %}
                <p class="sales-subtitle">Discover amazing deals on top-quality products</p>
            {% endif %}
        </div>
        
        <!-- Products Grid -->
        {% if sales_products %}
            <div class="sales-products-grid">
                {% for product in sales_products %}
                    {% include 'includes/sales_product_card.html' with product=product %}
                {% endfor %}
            </div>
            
            <!-- View All Button -->
            {% if show_view_all %}
            <div class="sales-view-all" style="text-align: center; margin-top: 40px;">
                <a href="{% url 'sales_products' %}" class="view-all-btn">
                    View All Sales Products
                    <i class="fas fa-arrow-right"></i>
                </a>
            </div>
            {% endif %}
        {% else %}
            <!-- No Products Message -->
            <div class="no-products-message" style="text-align: center; padding: 60px 20px;">
                <i class="fas fa-shopping-bag" style="font-size: 48px; color: #ddd; margin-bottom: 20px;"></i>
                <h3 style="color: #666; margin-bottom: 10px;">No Sales Products Available</h3>
                <p style="color: #999;">Check back soon for amazing deals!</p>
            </div>
        {% endif %}
    </div>
</section>

<style>
/* Additional styles for this section */
.view-all-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.view-all-btn:hover {
    background: #0056b3;
    transform: translateY(-2px);
    text-decoration: none;
    color: white;
}

.no-products-message {
    background: #f8f9fa;
    border-radius: 12px;
    border: 2px dashed #dee2e6;
}
</style>
