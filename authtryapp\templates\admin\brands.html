{% extends 'admin/base_admin.html' %}

{% block title %}Manage Brands{% endblock %}

{% block extra_css %}
<style>
    :root {
        --primary-color: #2563eb;
        --primary-hover: #1d4ed8;
        --secondary-color: #f0f9ff;
        --accent-color: #0284c7;
        --text-dark: #1e293b;
        --text-light: #64748b;
        --bg-light: #f8fafc;
        --bg-white: #ffffff;
        --shadow-sm: 0 1px 2px rgba(0,0,0,0.05);
        --shadow-md: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06);
        --shadow-lg: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05);
        --radius-sm: 0.375rem;
        --radius-md: 0.5rem;
        --radius-lg: 0.75rem;
    }
    
    .brand-logo-preview {
        width: 90px;
        height: 55px;
        object-fit: contain;
        background-color: var(--bg-light);
        border-radius: var(--radius-md);
        padding: 8px;
        border: 1px solid #e2e8f0;
        transition: transform 0.2s;
    }
    
    .brand-logo-preview:hover {
        transform: scale(1.1);
    }
    
    .add-brand-btn {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: var(--radius-md);
        transition: all 0.2s;
        font-weight: 500;
        margin-bottom: 20px;
    }
    
    .add-brand-btn:hover {
        background-color: var(--primary-hover);
        border-color: var(--primary-hover);
        box-shadow: 0 4px 6px rgba(37, 99, 235, 0.1);
        transform: translateY(-2px);
    }
    
    .search-sort-controls {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
    }
    
    .brand-count-badge {
        font-size: 0.8rem;
        background-color: #e9ecef;
        color: var(--text-dark);
        padding: 3px 10px;
        border-radius: 20px;
        margin-left: 8px;
        font-weight: 500;
    }
    
    .product-count {
        font-weight: 600;
        color: var(--text-dark);
    }
    
    .card {
        border: none;
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-md);
        overflow: hidden;
    }
    
    .card-header {
        background: linear-gradient(to right, var(--primary-color), #3b82f6);
        color: white;
        font-weight: 600;
        padding: 1rem 1.25rem;
        border: none;
    }
    
    .table {
        --bs-table-hover-bg: #f1f5f9;
    }
    
    .table-light, .table-light > th, .table-light > td {
        background-color: #f8fafc;
    }
    
    .table th {
        font-weight: 600;
        color: var(--text-dark);
        border-bottom-width: 1px;
    }
    
    .table td {
        vertical-align: middle;
        padding: 0.75rem;
    }
    
    .btn-group .btn {
        border-radius: var(--radius-md);
        font-size: 0.85rem;
        font-weight: 500;
        transition: all 0.2s;
    }
    
    .btn-outline-primary {
        color: var(--primary-color);
        border-color: var(--primary-color);
    }
    
    .btn-outline-primary:hover {
        background-color: var(--primary-color);
        color: white;
        box-shadow: 0 4px 6px rgba(37, 99, 235, 0.1);
    }
    
    .btn-outline-danger {
        color: #ef4444;
        border-color: #ef4444;
    }
    
    .btn-outline-danger:hover {
        background-color: #ef4444;
        color: white;
        box-shadow: 0 4px 6px rgba(239, 68, 68, 0.1);
    }
    
    .breadcrumb {
        background-color: transparent;
        padding: 0;
        margin-bottom: 1.5rem;
    }
    
    .breadcrumb-item a {
        color: var(--primary-color);
        text-decoration: none;
        transition: color 0.2s;
    }
    
    .breadcrumb-item a:hover {
        color: var(--primary-hover);
        text-decoration: underline;
    }
    
    .breadcrumb-item.active {
        color: var(--text-light);
    }
    
    .page-heading {
        color: var(--text-dark);
        font-weight: 700;
        font-size: 1.75rem;
        margin-bottom: 0.75rem;
    }
    
    .search-input {
        border-radius: var(--radius-md);
        border: 1px solid #e2e8f0;
        padding: 0.5rem 1rem;
        transition: all 0.2s;
    }
    
    .search-input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
    }
    
    .search-btn {
        border-top-right-radius: var(--radius-md);
        border-bottom-right-radius: var(--radius-md);
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }
    
    .search-btn:hover {
        background-color: var(--primary-hover);
        border-color: var(--primary-hover);
    }
    
    /* Animations */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .card {
        animation: fadeIn 0.4s ease-out;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4 mt-4">
    <h1 class="page-heading">Manage Brands</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{% url 'admin_dashboard' %}">Dashboard</a></li>
        <li class="breadcrumb-item active">Brands</li>
    </ol>
    
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-trademark me-1"></i>
            Brand Management
        </div>
        <div class="card-body">
            <div class="search-sort-controls">
                <a href="{% url 'admin_brand_add' %}" class="btn add-brand-btn">
                    <i class="fas fa-plus me-1"></i> Add New Brand
                </a>
                
                <div class="d-flex">
                    <div class="input-group" style="width: 300px;">
                        <input type="text" id="brandSearch" class="form-control search-input" placeholder="Search brands...">
                        <button class="btn search-btn" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="brandsTable">
                    <thead class="table-light">
                        <tr>
                            <th style="width: 50px;">ID</th>
                            <th style="width: 100px;">Logo</th>
                            <th>Brand Name</th>
                            <th>Products</th>
                            <th>Description</th>
                            <th style="width: 150px;">Created</th>
                            <th style="width: 180px;">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for brand in brands %}
                        <tr>
                            <td>{{ brand.id }}</td>
                            <td class="text-center">
                                {% if brand.logo %}
                                    <img src="{{ brand.logo.url }}" alt="{{ brand.name }}" class="brand-logo-preview">
                                {% else %}
                                    <span class="text-muted"><i class="fas fa-image"></i> No logo</span>
                                {% endif %}
                            </td>
                            <td><strong>{{ brand.name }}</strong></td>
                            <td><span class="product-count">{{ brand.products.count }}</span> <span class="text-muted">products</span></td>
                            <td>
                                {% if brand.description %}
                                    {{ brand.description|truncatechars:100 }}
                                {% else %}
                                    <span class="text-muted">No description</span>
                                {% endif %}
                            </td>
                            <td>{{ brand.created_at|date:"M d, Y" }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'admin_brand_edit' brand.id %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <a href="{% url 'admin_brand_delete' brand.id %}" class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i> Delete
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center py-5">
                                <div class="text-muted mb-3">No brands found.</div>
                                <a href="{% url 'admin_brand_add' %}" class="btn add-brand-btn">
                                    <i class="fas fa-plus me-1"></i> Add a Brand
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Simple search functionality
    document.getElementById('brandSearch').addEventListener('keyup', function() {
        let input = this.value.toLowerCase();
        let rows = document.querySelectorAll('#brandsTable tbody tr');
        
        rows.forEach(row => {
            let brandName = row.cells[2].textContent.toLowerCase();
            let brandDesc = row.cells[4].textContent.toLowerCase();
            
            if (brandName.includes(input) || brandDesc.includes(input)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });
</script>
{% endblock %} 