/* Product Card Styles */
.product-card {
    background: #ffffff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    border: 1px solid #f1f5f9;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 10px 15px rgba(0,0,0,0.1);
    border-color: #e2e8f0;
}

.product-image {
    height: 220px;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    transition: background 0.3s ease;
}

.product-card:hover .product-image {
    background: #f0f9ff;
}

.product-image img {
    max-height: 180px;
    width: auto;
    max-width: 100%;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-info {
    padding: 22px 25px 25px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    background: linear-gradient(0deg, #f8fafc 0%, #ffffff 100%);
}

.product-brand {
    font-size: 13px;
    color: #0284c7;
    text-transform: uppercase;
    font-weight: 600;
    margin-bottom: 8px;
    letter-spacing: 0.05em;
}

.brand-link {
    color: #0284c7;
    text-decoration: none;
    position: relative;
    transition: color 0.2s;
}

.brand-link:hover {
    color: #2563eb;
}

.brand-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 1px;
    bottom: -2px;
    left: 0;
    background-color: #2563eb;
    transition: width 0.3s;
}

.brand-link:hover::after {
    width: 100%;
}

.product-info h3 {
    font-size: 17px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #1e293b;
    line-height: 1.4;
    min-height: 48px;
}

.product-name-link {
    color: inherit;
    text-decoration: none;
    transition: color 0.2s;
}

.product-name-link:hover {
    color: #2563eb;
}

.starting-from {
    font-size: 12px;
    color: #64748b;
    margin-bottom: 4px;
}

.price {
    font-size: 20px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 20px;
}

.stock-status {
    position: absolute;
    top: 15px;
    right: 15px;
    padding: 6px 12px;
    font-size: 12px;
    font-weight: 600;
    border-radius: 4px;
    text-transform: capitalize;
    letter-spacing: 0.03em;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.stock-status.available {
    background: #10b981;
    color: white;
}

.stock-status.preorder {
    background: #f59e0b;
    color: #1e293b;
}

.stock-status.notavailable {
    background: #ef4444;
    color: white;
}

.product-buttons {
    display: flex;
    gap: 12px;
    margin-top: auto;
}

.add-to-cart {
    flex: 1;
    background: #2563eb;
    color: white;
    border: none;
    padding: 12px 0;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.03em;
}

.add-to-cart:hover {
    background: #1d4ed8;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
}

.add-to-cart.disabled {
    background: #94a3b8;
    cursor: not-allowed;
    opacity: 0.8;
}

.preorder-btn {
    background: #f59e0b;
    color: #1e293b;
}

.preorder-btn:hover {
    background: #d97706;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.2);
}

.view-cart-btn {
    width: 44px;
    height: 44px;
    border: 1px solid #e2e8f0;
    background: #ffffff;
    color: #0284c7;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.2s;
    font-size: 18px;
}

.view-cart-btn:hover {
    background: #f0f9ff;
    color: #2563eb;
    border-color: #bfdbfe;
}
