{% extends 'base_new.html' %}
{% load static %}
{% load custom_filters %}

{% block title %}iTechStore - Your Technology Destination{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/product_card.css' %}">
<link rel="stylesheet" href="{% static 'css/sales_products.css' %}">
<style>
    /* Modern Hero Banner Section */
    .hero-banner {
        position: relative;
        height: 500px;
        margin: 20px 0 60px 0;
        border-radius: 16px;
        overflow: hidden;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .hero-slide {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        transform: translateX(30px);
        transition: all 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        display: flex;
        align-items: center;
    }

    .hero-slide.active {
        opacity: 1;
        transform: translateX(0);
        z-index: 2;
    }

    .hero-slide.fade-out {
        opacity: 0;
        transform: translateX(-30px);
        z-index: 1;
    }

    .hero-content {
        display: flex;
        align-items: center;
        width: 100%;
        height: 100%;
        padding: 0 60px;
        color: white;
    }

    .hero-text {
        flex: 1;
        z-index: 2;
    }

    .hero-brand {
        font-size: 18px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 2px;
        margin-bottom: 10px;
        opacity: 0.9;
    }

    .hero-title {
        font-size: 48px;
        font-weight: 800;
        line-height: 1.1;
        margin-bottom: 20px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .hero-subtitle {
        font-size: 20px;
        font-weight: 400;
        margin-bottom: 30px;
        opacity: 0.9;
        line-height: 1.4;
    }

    .hero-buttons {
        display: flex;
        gap: 20px;
        align-items: center;
    }

    .hero-btn {
        padding: 15px 30px;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        font-size: 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
    }

    .hero-btn.primary {
        background: #fff;
        color: #333;
    }

    .hero-btn.primary:hover {
        background: #f0f0f0;
        transform: translateY(-2px);
    }

    .hero-btn.secondary {
        background: transparent;
        color: white;
        border: 2px solid white;
    }

    .hero-btn.secondary:hover {
        background: white;
        color: #333;
    }

    .hero-image {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
    }

    .hero-image img {
        max-width: 90%;
        max-height: 400px;
        object-fit: contain;
        filter: drop-shadow(0 10px 20px rgba(0,0,0,0.3));
        transition: all 0.3s ease;
    }

    /* Smart Image Display - Auto-detect wide images (like monitors) */
    .hero-image img[src*="5000"],
    .hero-image img[src*="4000"],
    .hero-image img[style*="width: 5000"],
    .hero-image img[style*="width: 4000"] {
        object-fit: contain;
        max-width: 95%;
        max-height: 380px;
        background: rgba(255,255,255,0.05);
        border-radius: 12px;
        padding: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    /* For very wide aspect ratio images (like the Dell monitor example) */
    .hero-image.wide-image img {
        object-fit: contain;
        width: 100%;
        max-width: 100%;
        height: auto;
        max-height: 350px;
        background: rgba(255,255,255,0.08);
        border-radius: 16px;
        padding: 20px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.25);
    }

    /* Manual dimension overrides */
    .hero-image[data-manual-width] img,
    .hero-image[data-manual-height] img {
        object-fit: contain !important;
    }

    /* For square or portrait images - fill the space */
    .hero-image.fill-image img {
        object-fit: cover;
        width: 100%;
        height: 400px;
        max-width: none;
        max-height: none;
        border-radius: 12px;
    }

    /* Auto-sizing based on image natural dimensions using CSS aspect-ratio detection */
    .hero-image img {
        /* Default styling for all images */
        border-radius: 8px;
    }

    /* Image Info Badge */
    .image-info {
        position: absolute;
        top: 10px;
        right: 10px;
        background: rgba(0,0,0,0.7);
        color: white;
        padding: 8px;
        border-radius: 50%;
        font-size: 12px;
        cursor: help;
        opacity: 0.7;
        transition: opacity 0.3s ease;
        z-index: 2;
    }

    .image-info:hover {
        opacity: 1;
    }

    /* Media query for better responsive handling */
    @media (max-width: 768px) {
        .hero-image img,
        .hero-image.wide-image img,
        .hero-image.fill-image img {
            max-height: 250px;
            padding: 10px;
        }

        .image-info {
            top: 5px;
            right: 5px;
            padding: 6px;
            font-size: 10px;
        }

        /* Mobile navigation adjustments */
        .hero-arrows {
            padding: 0 10px;
        }

        .hero-arrow {
            width: 40px;
            height: 40px;
            font-size: 14px;
        }

        .hero-nav {
            bottom: 20px;
            gap: 8px;
        }

        .hero-dot {
            width: 12px;
            height: 12px;
        }

        .hero-dot.active {
            transform: scale(1.2);
        }
    }

    /* Hero Navigation */
    .hero-nav {
        position: absolute;
        bottom: 30px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 12px;
        z-index: 10;
        pointer-events: auto;
    }

    .hero-dot {
        width: 14px;
        height: 14px;
        border-radius: 50%;
        background: rgba(255,255,255,0.5);
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid rgba(255,255,255,0.3);
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    }

    .hero-dot:hover {
        background: rgba(255,255,255,0.8);
        transform: scale(1.1);
    }

    .hero-dot.active {
        background: white;
        transform: scale(1.3);
        border-color: rgba(255,255,255,0.8);
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    }

    .hero-arrows {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: calc(100% - 40px);
        left: 20px;
        right: 20px;
        display: flex;
        justify-content: space-between;
        z-index: 10;
        pointer-events: none;
    }

    .hero-arrow {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: rgba(255,255,255,0.25);
        border: 2px solid rgba(255,255,255,0.4);
        color: white;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        font-size: 18px;
        pointer-events: auto;
        box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        opacity: 1;
    }

    .hero-arrow:hover {
        background: rgba(255,255,255,0.4);
        border-color: rgba(255,255,255,0.6);
        transform: scale(1.1);
        box-shadow: 0 6px 20px rgba(0,0,0,0.4);
    }

    .hero-arrow:active {
        transform: scale(0.95);
    }

    .hero-arrow i {
        pointer-events: none;
        text-shadow: 0 1px 3px rgba(0,0,0,0.5);
    }

    /* Ensure navigation is always visible */
    .hero-arrows,
    .hero-nav {
        opacity: 1 !important;
        visibility: visible !important;
    }

    /* Hide navigation on single slide (commented out for debugging) */
    /*
    .hero-banner[data-slides="1"] .hero-arrows,
    .hero-banner[data-slides="1"] .hero-nav {
        display: none !important;
    }
    */

    /* Force visibility for debugging */
    .hero-banner .hero-arrows {
        display: flex !important;
    }

    .hero-banner .hero-nav {
        display: flex !important;
    }

    /* Ensure arrows are positioned correctly */
    .hero-arrow.prev-hero {
        position: relative;
        left: 0;
    }

    .hero-arrow.next-hero {
        position: relative;
        right: 0;
    }

    /* Additional visibility enhancements */
    .hero-arrow {
        min-width: 50px;
        min-height: 50px;
        position: relative;
    }

    .hero-arrow::before {
        content: '';
        position: absolute;
        top: -5px;
        left: -5px;
        right: -5px;
        bottom: -5px;
        background: rgba(255,0,0,0.2);
        border-radius: 50%;
        z-index: -1;
        /* Debug outline - remove in production */
    }

    /* Enhanced arrow visibility */
    .hero-arrow {
        background: rgba(255,255,255,0.4) !important;
        border: 3px solid rgba(255,255,255,0.7) !important;
        box-shadow: 0 4px 20px rgba(0,0,0,0.4) !important;
    }

    .hero-arrow:hover {
        background: rgba(255,255,255,0.6) !important;
        border-color: rgba(255,255,255,0.9) !important;
        transform: scale(1.15) !important;
    }

    .hero-dot {
        min-width: 14px;
        min-height: 14px;
        position: relative;
    }

    .hero-dot::before {
        content: '';
        position: absolute;
        top: -3px;
        left: -3px;
        right: -3px;
        bottom: -3px;
        background: rgba(0,255,0,0.1);
        border-radius: 50%;
        z-index: -1;
        /* Debug outline - remove in production */
    }

    /* Sales Carousel Section */
    .sales-section {
        margin: 60px 0;
    }

    .sales-header {
        text-align: center;
        margin-bottom: 50px;
    }

    .sales-title {
        font-size: 36px;
        font-weight: 700;
        color: #333;
        margin-bottom: 10px;
    }

    .sales-subtitle {
        font-size: 18px;
        color: #666;
        font-weight: 400;
    }

    /* Product Grid Enhancements */
    .product-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 24px;
        margin-bottom: 50px;
    }

    /* Product Card Styles */
    .product-card {
        background: #fff;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0,0,0,0.08);
        transition: transform 0.3s, box-shadow 0.3s;
        position: relative;
        display: flex;
        flex-direction: column;
        height: 100%;
        padding: 0;
        border: none;
    }

    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0,0,0,0.08);
    }

    .product-image-container {
        position: relative;
        height: 230px;
        padding: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #fff;
    }

    .product-image {
        max-height: 180px;
        width: auto;
        max-width: 100%;
        object-fit: contain;
    }

    .availability-badge {
        position: absolute;
        top: 12px;
        left: 12px;
        padding: 4px 10px;
        font-size: 11px;
        font-weight: 500;
        border-radius: 4px;
        z-index: 2;
        text-transform: capitalize;
    }

    .availability-badge.available {
        background: #004277;
        color: white;
    }

    .availability-badge.pre-order {
        background: #ffc107;
        color: #212529;
    }

    .availability-badge.unavailable {
        background: #dc3545;
        color: white;
    }

    .product-info {
        padding: 16px 24px 24px;
        display: flex;
        flex-direction: column;
        flex-grow: 1;
    }

    .product-brand {
        font-size: 13px;
        color: #6c757d;
        text-transform: uppercase;
        font-weight: 600;
        margin-bottom: 4px;
        letter-spacing: 0.5px;
    }

    .product-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 16px;
        color: #212529;
        line-height: 1.3;
    }

    .starting-from {
        font-size: 11px;
        color: #6c757d;
        margin-bottom: 2px;
    }

    .product-price {
        font-size: 20px;
        font-weight: 700;
        color: #000;
        margin-bottom: 20px;
    }

    .product-buttons {
        margin-top: auto;
        display: flex;
        gap: 10px;
    }

    .add-to-cart {
        flex: 3;
        padding: 10px;
        border: none;
        background: #0070e0;
        color: white;
        border-radius: 6px;
        font-weight: 600;
        cursor: pointer;
        transition: background 0.2s;
        font-size: 14px;
    }

    .add-to-cart:hover {
        background: #0058b0;
    }

    .preorder-btn {
        background: #ffc107;
        color: #212529;
    }

    .preorder-btn:hover {
        background: #e0a800;
    }

    .add-to-cart.disabled {
        background: #6c757d;
        cursor: not-allowed;
        opacity: 0.7;
    }

    .view-cart-btn {
        flex: 1;
        padding: 10px;
        border: 1px solid #e9ecef;
        background: white;
        color: #495057;
        border-radius: 6px;
        font-weight: 600;
        text-align: center;
        text-decoration: none;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .view-cart-btn:hover {
        background: #f8f9fa;
        border-color: #dee2e6;
    }

    /* Product category section styles */
    .product-category-section {
        margin: 50px 0;
    }

    .category-header-wrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .category-title-section {
        text-align: left;
    }

    .category-title {
        font-size: 24px;
        margin-bottom: 5px;
        color: #333;
        font-weight: 600;
    }

    .view-all {
        color: #0070e0;
        text-decoration: none;
        font-weight: 600;
        font-size: 14px;
    }

    .view-all:hover {
        text-decoration: underline;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .hero-banner {
            height: 400px;
            margin: 10px 0 40px 0;
        }

        .hero-content {
            flex-direction: column;
            text-align: center;
            padding: 40px 30px;
        }

        .hero-text {
            margin-bottom: 30px;
        }

        .hero-title {
            font-size: 32px;
        }

        .hero-subtitle {
            font-size: 16px;
        }

        .hero-buttons {
            justify-content: center;
        }

        .hero-image img {
            max-height: 200px;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Modern Hero Banner Section -->
<section class="hero-banner" data-slides="{% if hero_banners %}{{ hero_banners|length }}{% else %}3{% endif %}">
    <div class="container">
        {% if hero_banners %}
            {% for banner in hero_banners %}
            <div class="hero-slide {% if forloop.first %}active{% endif %}"
                 style="background: linear-gradient(135deg, {{ banner.background_gradient_start }} 0%, {{ banner.background_gradient_end }} 100%);">
                <div class="hero-content">
                    <div class="hero-text">
                        {% if banner.brand_text %}
                        <div class="hero-brand">{{ banner.brand_text }}</div>
                        {% endif %}
                        <h1 class="hero-title">{{ banner.title|linebreaks|striptags }}</h1>
                        <p class="hero-subtitle">{{ banner.description }}</p>
                        <div class="hero-buttons">
                            <a href="{{ banner|get_primary_redirect_url }}" class="hero-btn primary" data-banner-id="{{ banner.id }}" data-link-type="{{ banner.link_type }}" data-link-target="{{ banner.link_target }}">{% if banner.cta_label %}{{ banner.cta_label }}{% else %}Shop Now{% endif %}</a>
                            {% if banner.secondary_cta_label and banner.secondary_link_target %}
                            <a href="{{ banner|get_secondary_redirect_url }}" class="hero-btn secondary" data-banner-id="{{ banner.id }}" data-link-type="{{ banner.secondary_link_type }}" data-link-target="{{ banner.secondary_link_target }}">{{ banner.secondary_cta_label }}</a>
                            {% endif %}
                        </div>
                    </div>
                    <div class="hero-image"
                         data-slide="{{ forloop.counter0 }}"
                         data-image-width="{{ banner.image_width }}"
                         data-image-height="{{ banner.image_height }}"
                         data-width-unit="{{ banner.width_unit }}"
                         data-height-unit="{{ banner.height_unit }}">
                        {% if banner.image %}
                        <img src="{{ banner.image.url }}" alt="{% if banner.image_alt %}{{ banner.image_alt }}{% else %}{{ banner.title }}{% endif %}"
                             onload="adjustImageDisplay(this)"
                             {% if banner.image_width %}style="width: {{ banner.image_width }}{{ banner.width_unit|default:'px' }}; {% if banner.image_height %}height: {{ banner.image_height }}{{ banner.height_unit|default:'px' }};{% endif %}"{% endif %}>
                        {% else %}
                        <img src="https://images.unsplash.com/photo-1591488320449-011701bb6704?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                             alt="{{ banner.image_alt }}"
                             onload="adjustImageDisplay(this)">
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <!-- Default slides if no banners in database -->
            <div class="hero-slide active" style="background: linear-gradient(135deg, #8B5CF6 0%, #EC4899 100%);">
                <div class="hero-content">
                    <div class="hero-text">
                        <div class="hero-brand">SAPPHIRE PULSE</div>
                        <h1 class="hero-title">AMD Radeon™ RX<br>9070 XT GPU</h1>
                        <p class="hero-subtitle">Experience next-generation gaming with cutting-edge performance and stunning visuals.</p>
                        <div class="hero-buttons">
                            <a href="#featured-products" class="hero-btn primary">AVAILABLE NOW</a>
                            <a href="#" class="hero-btn secondary">ITECHSTORE</a>
                        </div>
                    </div>
                    <div class="hero-image">
                        <img src="https://images.unsplash.com/photo-1591488320449-011701bb6704?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="AMD GPU">
                    </div>
                </div>
            </div>
            <div class="hero-slide" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="hero-content">
                    <div class="hero-text">
                        <div class="hero-brand">GAMING SERIES</div>
                        <h1 class="hero-title">Ultimate Gaming<br>Experience</h1>
                        <p class="hero-subtitle">High-performance gaming laptops designed for competitive gaming and content creation.</p>
                        <div class="hero-buttons">
                            <a href="#featured-products" class="hero-btn primary">SHOP NOW</a>
                            <a href="#" class="hero-btn secondary">LEARN MORE</a>
                        </div>
                    </div>
                    <div class="hero-image">
                        <img src="https://images.unsplash.com/photo-1593640408182-31c70c8268f5?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Gaming Laptop">
                    </div>
                </div>
            </div>
            <div class="hero-slide" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="hero-content">
                    <div class="hero-text">
                        <div class="hero-brand">SMART TECH</div>
                        <h1 class="hero-title">Connected<br>Lifestyle</h1>
                        <p class="hero-subtitle">Transform your home with intelligent devices that adapt to your daily routine.</p>
                        <div class="hero-buttons">
                            <a href="#featured-products" class="hero-btn primary">EXPLORE</a>
                            <a href="#" class="hero-btn secondary">DISCOVER</a>
                        </div>
                    </div>
                    <div class="hero-image">
                        <img src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Smart Devices">
                    </div>
                </div>
            </div>
        {% endif %}

        <!-- Navigation Arrows -->
        {% if site_config.hero_show_arrows|default:True %}
        <div class="hero-arrows">
            <button class="hero-arrow prev-hero" aria-label="Previous slide" title="Previous slide">
                <i class="fas fa-chevron-left">‹</i>
            </button>
            <button class="hero-arrow next-hero" aria-label="Next slide" title="Next slide">
                <i class="fas fa-chevron-right">›</i>
            </button>
        </div>
        {% endif %}

        <!-- Navigation Dots -->
        {% if site_config.hero_show_dots|default:True %}
        <div class="hero-nav">
            {% if hero_banners %}
                {% for banner in hero_banners %}
                <span class="hero-dot {% if forloop.first %}active{% endif %}" data-slide="{{ forloop.counter0 }}" title="Slide {{ forloop.counter }}"></span>
                {% endfor %}
            {% else %}
                <span class="hero-dot active" data-slide="0" title="Slide 1"></span>
                <span class="hero-dot" data-slide="1" title="Slide 2"></span>
                <span class="hero-dot" data-slide="2" title="Slide 3"></span>
            {% endif %}
        </div>
        {% endif %}
    </div>
</section>

<!-- Sales Products Section -->
{% if sales_products %}
    {% if section_headings.special_offers %}
        {% with title=section_headings.special_offers.title subtitle=section_headings.special_offers.subtitle %}
            {% include 'includes/sales_products_section.html' with sales_products=sales_products section_title=title section_subtitle=subtitle show_view_all=True %}
        {% endwith %}
    {% else %}
        {% include 'includes/sales_products_section.html' with sales_products=sales_products section_title='Special Offers' section_subtitle='Limited time deals on premium products' show_view_all=True %}
    {% endif %}
{% endif %}

<!-- Featured Products Section -->
{% if section_headings.featured_products %}
    {% with title=section_headings.featured_products.title subtitle=section_headings.featured_products.subtitle %}
        {% include 'includes/product_carousel.html' with carousel_id='featured-products' title=title subtitle=subtitle products=featured_products %}
    {% endwith %}
{% else %}
    {% include 'includes/product_carousel.html' with carousel_id='featured-products' title='Featured Products' products=featured_products %}
{% endif %}

<!-- Best Selling Products Section -->
{% if best_selling_products %}
    {% if section_headings.best_selling %}
        {% with title=section_headings.best_selling.title subtitle=section_headings.best_selling.subtitle %}
            {% include 'includes/product_carousel.html' with carousel_id='best-selling-products' title=title subtitle=subtitle products=best_selling_products %}
        {% endwith %}
    {% else %}
        {% include 'includes/product_carousel.html' with carousel_id='best-selling-products' title='Best Selling Products' products=best_selling_products %}
    {% endif %}
{% endif %}

<!-- Top Deals Section -->
{% if top_deals %}
    {% if section_headings.top_deals %}
        {% with title=section_headings.top_deals.title subtitle=section_headings.top_deals.subtitle %}
            {% include 'includes/product_carousel.html' with carousel_id='top-deals' title=title subtitle=subtitle products=top_deals %}
        {% endwith %}
    {% else %}
        {% include 'includes/product_carousel.html' with carousel_id='top-deals' title='Top Deals' products=top_deals %}
    {% endif %}
{% endif %}

<!-- Category Sections with Enhanced Sliders -->
{% for category in categories %}
    {% if category.id in category_products and category_products|get_item:category.id %}
    <section class="product-category-section category-products-container" data-category-id="{{ category.id }}">
        <div class="container">
            <div class="category-header">
                <div class="category-title-section">
                    <h2 class="category-title">{{ category.name }}</h2>
                    <p class="category-subtitle">{{ category.description|default:"Explore our collection" }}</p>
                </div>
                <!-- Slider controls and "See More" button will be added by JavaScript -->
            </div>

            <div class="products-grid sales-products-grid">
                {% for product in category_products|get_item:category.id %}
                    <div class="product-card">
                        {% include 'includes/sales_product_card.html' with product=product %}
                    </div>
                {% endfor %}
            </div>
        </div>
    </section>
    {% endif %}
{% empty %}
    <!-- Default Category Sections if no categories in database -->
    <!-- Laptops Section -->
    <section class="product-category-section category-products-container" data-category-id="laptops">
        <div class="container">
            <div class="category-header">
                <div class="category-title-section">
                    <h2 class="category-title">Laptops</h2>
                    <p class="category-subtitle">High-performance laptops for work and gaming</p>
                </div>
                <!-- Slider controls will be added by JavaScript -->
            </div>

            <div class="products-grid sales-products-grid">
                <!-- Note: These are fallback demo products. In production, use actual product data -->
                <div class="product-card sales-product-card" data-product-id="demo5">
                    <div class="sales-product-image-container">
                        <img src="https://media.itechstore.com.np/media/catalog/product/cache/1/small_image/300x/9df78eab33525d08d6e5fb8d27136e95/l/e/lenovo_thinkpad_x1_carbon_gen_9_black_01.jpg" alt="ThinkPad X1" class="sales-product-image">
                        <div class="sales-product-badges">
                            <span class="availability-badge available">Available</span>
                        </div>
                    </div>
                    <div class="sales-product-info">
                        <div class="sales-product-title-section">
                            <h3 class="sales-product-title">ThinkPad X1 Carbon Gen 9</h3>
                            <p class="sales-product-specs">LENOVO</p>
                        </div>
                        <div class="sales-product-pricing">
                            <div class="sales-product-price">₨. 1,85,000.00</div>
                        </div>
                        <div class="sales-product-actions">
                            <button class="add-to-cart-btn primary" data-product-id="demo5">
                                <i class="fas fa-shopping-cart"></i>
                                Add to Cart
                            </button>
                            <a href="#" class="quick-view-btn" title="View Details">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Add more demo products for slider demonstration -->
                <div class="product-card sales-product-card" data-product-id="demo6">
                    <div class="sales-product-image-container">
                        <img src="https://media.itechstore.com.np/media/catalog/product/cache/1/small_image/300x/9df78eab33525d08d6e5fb8d27136e95/a/s/asus_rog_strix_g15_01.jpg" alt="ASUS ROG Strix" class="sales-product-image">
                        <div class="sales-product-badges">
                            <span class="availability-badge available">Available</span>
                        </div>
                    </div>
                    <div class="sales-product-info">
                        <div class="sales-product-title-section">
                            <h3 class="sales-product-title">ASUS ROG Strix G15</h3>
                            <p class="sales-product-specs">ASUS</p>
                        </div>
                        <div class="sales-product-pricing">
                            <div class="sales-product-price">₨. 1,65,000.00</div>
                        </div>
                        <div class="sales-product-actions">
                            <button class="add-to-cart-btn primary" data-product-id="demo6">
                                <i class="fas fa-shopping-cart"></i>
                                Add to Cart
                            </button>
                            <a href="#" class="quick-view-btn" title="View Details">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Add more products to trigger slider -->
                <div class="product-card sales-product-card" data-product-id="demo7">
                    <div class="sales-product-image-container">
                        <img src="https://media.itechstore.com.np/media/catalog/product/cache/1/small_image/300x/9df78eab33525d08d6e5fb8d27136e95/h/p/hp_pavilion_15_01.jpg" alt="HP Pavilion 15" class="sales-product-image">
                        <div class="sales-product-badges">
                            <span class="availability-badge available">Available</span>
                        </div>
                    </div>
                    <div class="sales-product-info">
                        <div class="sales-product-title-section">
                            <h3 class="sales-product-title">HP Pavilion 15</h3>
                            <p class="sales-product-specs">HP</p>
                        </div>
                        <div class="sales-product-pricing">
                            <div class="sales-product-price">₨. 85,000.00</div>
                        </div>
                        <div class="sales-product-actions">
                            <button class="add-to-cart-btn primary" data-product-id="demo7">
                                <i class="fas fa-shopping-cart"></i>
                                Add to Cart
                            </button>
                            <a href="#" class="quick-view-btn" title="View Details">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="product-card sales-product-card" data-product-id="demo8">
                    <div class="sales-product-image-container">
                        <img src="https://media.itechstore.com.np/media/catalog/product/cache/1/small_image/300x/9df78eab33525d08d6e5fb8d27136e95/d/e/dell_inspiron_15_01.jpg" alt="Dell Inspiron 15" class="sales-product-image">
                        <div class="sales-product-badges">
                            <span class="availability-badge available">Available</span>
                        </div>
                    </div>
                    <div class="sales-product-info">
                        <div class="sales-product-title-section">
                            <h3 class="sales-product-title">Dell Inspiron 15</h3>
                            <p class="sales-product-specs">DELL</p>
                        </div>
                        <div class="sales-product-pricing">
                            <div class="sales-product-price">₨. 75,000.00</div>
                        </div>
                        <div class="sales-product-actions">
                            <button class="add-to-cart-btn primary" data-product-id="demo8">
                                <i class="fas fa-shopping-cart"></i>
                                Add to Cart
                            </button>
                            <a href="#" class="quick-view-btn" title="View Details">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="product-card sales-product-card" data-product-id="demo10">
                    <div class="sales-product-image-container">
                        <img src="https://media.itechstore.com.np/media/catalog/product/cache/1/small_image/300x/9df78eab33525d08d6e5fb8d27136e95/m/a/macbook_air_m2_01.jpg" alt="MacBook Air M2" class="sales-product-image">
                        <div class="sales-product-badges">
                            <span class="availability-badge available">Available</span>
                        </div>
                    </div>
                    <div class="sales-product-info">
                        <div class="sales-product-title-section">
                            <h3 class="sales-product-title">MacBook Air M2</h3>
                            <p class="sales-product-specs">APPLE</p>
                        </div>
                        <div class="sales-product-pricing">
                            <div class="sales-product-price">₨. 1,45,000.00</div>
                        </div>
                        <div class="sales-product-actions">
                            <button class="add-to-cart-btn primary" data-product-id="demo10">
                                <i class="fas fa-shopping-cart"></i>
                                Add to Cart
                            </button>
                            <a href="#" class="quick-view-btn" title="View Details">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Gaming Section -->
    <section class="product-category-section category-products-container" data-category-id="gaming">
        <div class="container">
            <div class="category-header">
                <div class="category-title-section">
                    <h2 class="category-title">Gaming</h2>
                    <p class="category-subtitle">High-performance gaming gear and accessories</p>
                </div>
                <!-- Slider controls will be added by JavaScript -->
            </div>

            <div class="products-grid sales-products-grid">
                <!-- Gaming products -->
                <div class="product-card sales-product-card" data-product-id="demo9">
                    <div class="sales-product-image-container">
                        <img src="https://media.itechstore.com.np/media/catalog/product/cache/1/small_image/300x/9df78eab33525d08d6e5fb8d27136e95/m/s/msi_mag_infinite_s_01.jpg" alt="MSI Infinite" class="sales-product-image">
                        <div class="sales-product-badges">
                            <span class="availability-badge available">Available</span>
                        </div>
                    </div>
                    <div class="sales-product-info">
                        <div class="sales-product-title-section">
                            <h3 class="sales-product-title">MAG Infinite S Desktop</h3>
                            <p class="sales-product-specs">MSI</p>
                        </div>
                        <div class="sales-product-pricing">
                            <div class="sales-product-price">₨. 1,95,000.00</div>
                        </div>
                        <div class="sales-product-actions">
                            <button class="add-to-cart-btn primary" data-product-id="demo9">
                                <i class="fas fa-shopping-cart"></i>
                                Add to Cart
                            </button>
                            <a href="#" class="quick-view-btn" title="View Details">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Add more gaming products for slider -->
                <div class="product-card sales-product-card" data-product-id="demo11">
                    <div class="sales-product-image-container">
                        <img src="https://media.itechstore.com.np/media/catalog/product/cache/1/small_image/300x/9df78eab33525d08d6e5fb8d27136e95/r/a/razer_blade_15_01.jpg" alt="Razer Blade 15" class="sales-product-image">
                        <div class="sales-product-badges">
                            <span class="availability-badge available">Available</span>
                        </div>
                    </div>
                    <div class="sales-product-info">
                        <div class="sales-product-title-section">
                            <h3 class="sales-product-title">Razer Blade 15</h3>
                            <p class="sales-product-specs">RAZER</p>
                        </div>
                        <div class="sales-product-pricing">
                            <div class="sales-product-price">₨. 2,25,000.00</div>
                        </div>
                        <div class="sales-product-actions">
                            <button class="add-to-cart-btn primary" data-product-id="demo11">
                                <i class="fas fa-shopping-cart"></i>
                                Add to Cart
                            </button>
                            <a href="#" class="quick-view-btn" title="View Details">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="product-card sales-product-card" data-product-id="demo12">
                    <div class="sales-product-image-container">
                        <img src="https://media.itechstore.com.np/media/catalog/product/cache/1/small_image/300x/9df78eab33525d08d6e5fb8d27136e95/a/l/alienware_aurora_r13_01.jpg" alt="Alienware Aurora" class="sales-product-image">
                        <div class="sales-product-badges">
                            <span class="availability-badge available">Available</span>
                        </div>
                    </div>
                    <div class="sales-product-info">
                        <div class="sales-product-title-section">
                            <h3 class="sales-product-title">Alienware Aurora R13</h3>
                            <p class="sales-product-specs">DELL</p>
                        </div>
                        <div class="sales-product-pricing">
                            <div class="sales-product-price">₨. 2,85,000.00</div>
                        </div>
                        <div class="sales-product-actions">
                            <button class="add-to-cart-btn primary" data-product-id="demo12">
                                <i class="fas fa-shopping-cart"></i>
                                Add to Cart
                            </button>
                            <a href="#" class="quick-view-btn" title="View Details">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="product-card sales-product-card" data-product-id="demo13">
                    <div class="sales-product-image-container">
                        <img src="https://media.itechstore.com.np/media/catalog/product/cache/1/small_image/300x/9df78eab33525d08d6e5fb8d27136e95/l/o/logitech_g_pro_x_01.jpg" alt="Logitech G Pro X" class="sales-product-image">
                        <div class="sales-product-badges">
                            <span class="availability-badge available">Available</span>
                        </div>
                    </div>
                    <div class="sales-product-info">
                        <div class="sales-product-title-section">
                            <h3 class="sales-product-title">Logitech G Pro X Headset</h3>
                            <p class="sales-product-specs">LOGITECH</p>
                        </div>
                        <div class="sales-product-pricing">
                            <div class="sales-product-price">₨. 15,000.00</div>
                        </div>
                        <div class="sales-product-actions">
                            <button class="add-to-cart-btn primary" data-product-id="demo13">
                                <i class="fas fa-shopping-cart"></i>
                                Add to Cart
                            </button>
                            <a href="#" class="quick-view-btn" title="View Details">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="product-card sales-product-card" data-product-id="demo14">
                    <div class="sales-product-image-container">
                        <img src="https://media.itechstore.com.np/media/catalog/product/cache/1/small_image/300x/9df78eab33525d08d6e5fb8d27136e95/r/a/razer_deathadder_v3_01.jpg" alt="Razer DeathAdder V3" class="sales-product-image">
                        <div class="sales-product-badges">
                            <span class="availability-badge available">Available</span>
                        </div>
                    </div>
                    <div class="sales-product-info">
                        <div class="sales-product-title-section">
                            <h3 class="sales-product-title">Razer DeathAdder V3</h3>
                            <p class="sales-product-specs">RAZER</p>
                        </div>
                        <div class="sales-product-pricing">
                            <div class="sales-product-price">₨. 8,500.00</div>
                        </div>
                        <div class="sales-product-actions">
                            <button class="add-to-cart-btn primary" data-product-id="demo14">
                                <i class="fas fa-shopping-cart"></i>
                                Add to Cart
                            </button>
                            <a href="#" class="quick-view-btn" title="View Details">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
{% endfor %}

{% block extra_js %}
<script>
// Modern Hero Carousel Functionality
document.addEventListener('DOMContentLoaded', function() {
    const heroSlides = document.querySelectorAll('.hero-slide');
    const heroDots = document.querySelectorAll('.hero-dot');
    const prevBtn = document.querySelector('.prev-hero');
    const nextBtn = document.querySelector('.next-hero');
    let currentSlide = 0;
    let slideInterval;

    // Initialize carousel
    function initCarousel() {
        if (heroSlides.length > 0) {
            showSlide(0);
            // Only start auto-slide if there are multiple slides
            if (heroSlides.length > 1) {
                startAutoSlide();
            }
        }
    }

    // Show specific slide with smooth animation
    function showSlide(index) {
        // Add fade-out class to current active slide
        const currentActiveSlide = document.querySelector('.hero-slide.active');
        if (currentActiveSlide) {
            currentActiveSlide.classList.add('fade-out');
            currentActiveSlide.classList.remove('active');
        }

        // Update dots
        heroDots.forEach(dot => dot.classList.remove('active'));

        // Show new slide after a brief delay for smooth transition
        setTimeout(() => {
            // Remove fade-out from all slides
            heroSlides.forEach(slide => {
                slide.classList.remove('active', 'fade-out');
            });

            // Show current slide
            if (heroSlides[index]) {
                heroSlides[index].classList.add('active');
                heroDots[index].classList.add('active');
            }
            currentSlide = index;
        }, 100);
    }

    // Next slide
    function nextSlide() {
        const next = (currentSlide + 1) % heroSlides.length;
        showSlide(next);
    }

    // Previous slide
    function prevSlide() {
        const prev = (currentSlide - 1 + heroSlides.length) % heroSlides.length;
        showSlide(prev);
    }

    // Auto slide functionality
    function startAutoSlide() {
        const interval = {{ site_config.hero_auto_slide_interval|default:5000 }};
        slideInterval = setInterval(nextSlide, interval);
    }

    function stopAutoSlide() {
        clearInterval(slideInterval);
    }

    // Event listeners
    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            stopAutoSlide();
            nextSlide();
            startAutoSlide();
        });
    }

    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            stopAutoSlide();
            prevSlide();
            startAutoSlide();
        });
    }

    // Dot navigation
    heroDots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
            stopAutoSlide();
            showSlide(index);
            startAutoSlide();
        });
    });

    // Pause on hover
    const heroSection = document.querySelector('.hero-banner');
    if (heroSection) {
        heroSection.addEventListener('mouseenter', stopAutoSlide);
        heroSection.addEventListener('mouseleave', startAutoSlide);
    }

    // Initialize
    initCarousel();

    // Debug: Log navigation elements
    console.log('Hero slides found:', heroSlides.length);
    console.log('Hero dots found:', heroDots.length);
    console.log('Previous button:', prevBtn ? 'Found' : 'Not found');
    console.log('Next button:', nextBtn ? 'Found' : 'Not found');

    // Enhanced debugging for arrows
    if (prevBtn) {
        console.log('Previous button position:', prevBtn.getBoundingClientRect());
        prevBtn.style.border = '3px solid red';
    }
    if (nextBtn) {
        console.log('Next button position:', nextBtn.getBoundingClientRect());
        nextBtn.style.border = '3px solid blue';
    }

    // Force show navigation for debugging
    if (heroSlides.length > 1) {
        const arrowsContainer = document.querySelector('.hero-arrows');
        const dotsContainer = document.querySelector('.hero-nav');

        if (arrowsContainer) {
            arrowsContainer.style.display = 'flex';
            arrowsContainer.style.opacity = '1';
            arrowsContainer.style.visibility = 'visible';
            console.log('Arrows container forced visible');
        }

        if (dotsContainer) {
            dotsContainer.style.display = 'flex';
            dotsContainer.style.opacity = '1';
            dotsContainer.style.visibility = 'visible';
            console.log('Dots container forced visible');
        }
    }

    // Smart Image Display Adjustment Function
    window.adjustImageDisplay = function(img) {
        const container = img.closest('.hero-image');
        const manualWidth = container.dataset.manualWidth;
        const manualHeight = container.dataset.manualHeight;
        const coverage = container.dataset.coverage || 90;

        // Use manual dimensions if provided, otherwise use natural dimensions
        const effectiveWidth = manualWidth ? parseInt(manualWidth) : img.naturalWidth;
        const effectiveHeight = manualHeight ? parseInt(manualHeight) : img.naturalHeight;
        const aspectRatio = effectiveWidth / effectiveHeight;

        // Apply coverage setting
        img.style.maxWidth = `${coverage}%`;

        // Remove any existing classes
        container.classList.remove('wide-image', 'fill-image');

        // Auto-detect and apply appropriate class based on aspect ratio
        if (aspectRatio > 1.8) {
            // Very wide images (like monitors, laptops) - show full product
            container.classList.add('wide-image');
            console.log(`Wide image detected: ${effectiveWidth}x${effectiveHeight} (ratio: ${aspectRatio.toFixed(2)}) Coverage: ${coverage}%`);
        } else if (aspectRatio < 1.2) {
            // Square or portrait images - fill the space
            container.classList.add('fill-image');
            console.log(`Portrait/Square image detected: ${effectiveWidth}x${effectiveHeight} (ratio: ${aspectRatio.toFixed(2)}) Coverage: ${coverage}%`);
        } else {
            // Standard landscape images - use default contain
            console.log(`Standard image detected: ${effectiveWidth}x${effectiveHeight} (ratio: ${aspectRatio.toFixed(2)}) Coverage: ${coverage}%`);
        }

        // Apply manual dimensions if specified
        if (manualWidth || manualHeight) {
            if (manualWidth) {
                img.style.width = `${manualWidth}px`;
            }
            if (manualHeight) {
                img.style.height = `${manualHeight}px`;
            }
            console.log(`Manual dimensions applied: ${manualWidth || 'auto'}x${manualHeight || 'auto'}`);
        }
    };

    // Enhanced Hero button click handlers with admin panel integration
    const heroButtons = document.querySelectorAll('.hero-btn');
    heroButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            const linkType = this.getAttribute('data-link-type');
            const linkTarget = this.getAttribute('data-link-target');
            const bannerId = this.getAttribute('data-banner-id');

            console.log(`🎯 Hero Button Clicked:`, {
                href,
                linkType,
                linkTarget,
                bannerId,
                buttonText: this.textContent.trim()
            });

            // Handle anchor links (page sections)
            if (linkType === 'anchor' || href.startsWith('#')) {
                e.preventDefault();
                const targetId = href.replace('#', '');
                const targetElement = document.getElementById(targetId) || document.querySelector(`.${targetId}`);

                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                    console.log(`✅ Scrolled to section: ${targetId}`);
                } else {
                    console.warn(`⚠️ Target element not found: ${targetId}`);
                    // Fallback: try to find common sections
                    const fallbackTargets = [
                        'featured-products',
                        'sales-section',
                        'product-category-section'
                    ];

                    for (const fallback of fallbackTargets) {
                        const fallbackElement = document.getElementById(fallback) || document.querySelector(`.${fallback}`);
                        if (fallbackElement) {
                            fallbackElement.scrollIntoView({ behavior: 'smooth' });
                            console.log(`✅ Fallback scroll to: ${fallback}`);
                            break;
                        }
                    }
                }
                return;
            }

            // Handle external URLs
            if (linkType === 'custom' && (href.startsWith('http') || href.startsWith('//'))) {
                // Let the browser handle external links normally
                console.log(`🌐 External link: ${href}`);
                return;
            }

            // Handle product and category links
            if (linkType === 'product' || linkType === 'category') {
                // Let the browser handle these links normally
                console.log(`🔗 ${linkType} link: ${href}`);
                return;
            }

            // Handle relative URLs and other cases
            if (href && href !== '#') {
                console.log(`📄 Relative/Other link: ${href}`);
                return;
            }

            // Fallback for empty or invalid links
            e.preventDefault();
            console.warn(`⚠️ Invalid or empty link, using fallback behavior`);
            const featuredSection = document.querySelector('#featured-products') || document.querySelector('.sales-section');
            if (featuredSection) {
                featuredSection.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });

    // Real Add to Cart functionality for home page
    console.log('🛒 Home Page Cart System Initialized');

    const addToCartButtons = document.querySelectorAll('.add-to-cart-btn:not(.disabled)');
    const cartCounts = document.querySelectorAll('.cart-count, .floating-cart-count');

    addToCartButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            // Get product ID from data attribute
            const productId = this.dataset.productId || this.getAttribute('data-product-id');
            if (!productId) {
                console.error('Product ID not found');
                return;
            }

            // Show immediate loading state
            const originalText = button.innerHTML;
            const originalBg = button.style.background;

            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';
            button.disabled = true;
            button.style.background = '#6c757d';

            // Get CSRF token
            let csrfToken = '';
            const metaToken = document.querySelector('meta[name="csrf-token"]');
            const inputToken = document.querySelector('[name="csrfmiddlewaretoken"]');

            if (metaToken) {
                csrfToken = metaToken.getAttribute('content');
            } else if (inputToken) {
                csrfToken = inputToken.value;
            }

            // Send AJAX request to add product to cart
            fetch(`/cart/add/${productId}/`, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': csrfToken,
                    'Content-Type': 'application/x-www-form-urlencoded',
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.status === 'success') {
                    // Update all cart count elements with animation
                    cartCounts.forEach(count => {
                        const oldCount = parseInt(count.textContent) || 0;
                        const newCount = data.cart_total;

                        // Animate count change
                        count.style.transform = 'scale(1.3)';
                        count.style.color = '#28a745';
                        count.textContent = newCount;

                        setTimeout(() => {
                            count.style.transform = 'scale(1)';
                            count.style.color = '';
                        }, 300);
                    });

                    // Success animation for button
                    button.innerHTML = '<i class="fas fa-check"></i> Added!';
                    button.style.background = '#28a745';

                    // Show floating success message
                    showFloatingMessage('Product added to cart!', 'success');

                    // Reset button after delay
                    setTimeout(() => {
                        button.innerHTML = originalText;
                        button.style.background = originalBg;
                        button.disabled = false;
                    }, 2000);
                } else {
                    // Error state
                    button.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Error';
                    button.style.background = '#dc3545';

                    showFloatingMessage(data.message || 'Error adding to cart', 'error');

                    setTimeout(() => {
                        button.innerHTML = originalText;
                        button.style.background = originalBg;
                        button.disabled = false;
                    }, 2000);
                }
            })
            .catch(error => {
                console.error('Error adding to cart:', error);

                // Error state
                button.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Error';
                button.style.background = '#dc3545';

                showFloatingMessage('Network error. Please try again.', 'error');

                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.style.background = originalBg;
                    button.disabled = false;
                }, 2000);
            });
        });
    });

    // Floating message function for home page
    function showFloatingMessage(message, type = 'success') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `floating-message floating-message-${type}`;
        messageDiv.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
            ${message}
        `;

        // Add styles
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            padding: 15px 25px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            animation: slideInFromRight 0.3s ease-out;
            max-width: 300px;
            background: ${type === 'success' ? 'linear-gradient(135deg, #28a745 0%, #20c997 100%)' : 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)'};
        `;

        document.body.appendChild(messageDiv);

        setTimeout(() => {
            messageDiv.style.animation = 'slideOutToRight 0.3s ease-in';
            setTimeout(() => messageDiv.remove(), 300);
        }, 3000);
    }

    // Add CSS animations for floating messages
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInFromRight {
            0% { opacity: 0; transform: translateX(100px); }
            100% { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideOutToRight {
            0% { opacity: 1; transform: translateX(0); }
            100% { opacity: 0; transform: translateX(100px); }
        }

        .cart-count, .floating-cart-count {
            transition: all 0.3s ease;
        }
    `;
    document.head.appendChild(style);
});
</script>
{% endblock %}
{% endblock %}
