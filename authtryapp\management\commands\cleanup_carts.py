from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from authtryapp.models import Cart

class Command(BaseCommand):
    help = 'Cleanup old anonymous carts'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=30,
            help='Delete anonymous carts older than this many days'
        )

    def handle(self, *args, **options):
        days = options['days']
        cutoff_date = timezone.now() - timedelta(days=days)
        
        # Get old anonymous carts
        old_carts = Cart.objects.filter(
            user__isnull=True,  # Anonymous carts only
            updated_at__lt=cutoff_date  # Older than cutoff date
        )
        
        count = old_carts.count()
        old_carts.delete()
        
        self.stdout.write(
            self.style.SUCCESS(f'Successfully deleted {count} old anonymous carts')
        )
