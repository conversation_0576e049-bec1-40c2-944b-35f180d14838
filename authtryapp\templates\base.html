{% load static %} 
<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TechShop - {% block title %}{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{% static 'css/custom.css' %}" rel="stylesheet">
</head>

<body>

    <!-- Theme Toggle Button -->
    <div class="dropdown position-fixed bottom-0 end-0 mb-3 me-3 bd-mode-toggle">
        <button class="btn btn-bd-primary py-2 dropdown-toggle d-flex align-items-center" 
                id="bd-theme" type="button" aria-expanded="false" 
                data-bs-toggle="dropdown" aria-label="Toggle theme (auto)">
            <svg class="bi my-1 theme-icon-active" width="1em" height="1em"><use href="#circle-half"></use></svg>
            <span class="visually-hidden" id="bd-theme-text">Toggle theme</span>
        </button>
        <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="bd-theme-text">
            <li>
                <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="light" aria-pressed="false">
                    <svg class="bi me-2 opacity-50" width="1em" height="1em"><use href="#sun-fill"></use></svg>
                    Light
                </button>
            </li>
            <li>
                <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="dark" aria-pressed="false">
                    <svg class="bi me-2 opacity-50" width="1em" height="1em"><use href="#moon-stars-fill"></use></svg>
                    Dark
                </button>
            </li>
            <li>
                <button type="button" class="dropdown-item d-flex align-items-center active" data-bs-theme-value="auto" aria-pressed="true">
                    <svg class="bi me-2 opacity-50" width="1em" height="1em"><use href="#circle-half"></use></svg>
                    Auto
                </button>
            </li>
        </ul>
    </div>

    <!-- Main Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark"><nav class="navbar navbar-expand-lg navbar-dark bg-dark">
      <div class="container-fluid">
          <a class="navbar-brand" href="#">TechShop</a>
          <button class="navbar-toggler" type="button" data-bs-toggle="offcanvas" data-bs-target="#offcanvasNavbar"
              aria-controls="offcanvasNavbar">
              <span class="navbar-toggler-icon"></span>
          </button>
          <div class="offcanvas offcanvas-end text-bg-dark" id="offcanvasNavbar" aria-labelledby="offcanvasNavbarLabel">
              <div class="offcanvas-header">
                  <h5 class="offcanvas-title" id="offcanvasNavbarLabel">Menu</h5>
                  <button type="button" class="btn-close btn-close-white" data-bs-dismiss="offcanvas" aria-label="Close"></button>
              </div>
              <div class="offcanvas-body">
                  <ul class="navbar-nav justify-content-end flex-grow-1 pe-3">
                      <li class="nav-item"><a class="nav-link active" href="#">Home</a></li>

                      {% if user.is_authenticated %}
                          <!-- If the user is logged in, show the username -->
                          <li class="nav-item">
                              <span class="nav-link welcome-message">Welcome, {{ user.username }}!</span>
                          </li>
                          <li class="nav-item">
                              <a class="nav-link" href="/profile">Profile</a>
                          </li>
                          <li class="nav-item">
                              <a class="nav-link" href="{% url 'logout' %}">Logout</a>
                          </li>
                          {% if user.is_staff %}
                          <li class="nav-item"><a class="nav-link" href="{% url 'admin:index' %}">Admin Panel</a></li>
                      {% endif %}

                      {% else %}
                          <!-- If no user is logged in, show Login link -->
                          <li class="nav-item">
                              <a class="nav-link" href="{% url 'login' %}">Login</a>
                          </li>
                      {% endif %}

                      <!-- Dropdown -->
                     
                      {% comment %} <li class="nav-item dropdown"> 
                          <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                              Dropdown
                          </a>
                          <ul class="dropdown-menu">
                              <li><a class="dropdown-item" href="#">Action</a></li>
                              <li><a class="dropdown-item" href="#">Another action</a></li>
                              <li><hr class="dropdown-divider"></li>
                              <li><a class="dropdown-item" href="#">Something else here</a></li>
                          </ul>
                      </li>
                  </ul>
               </div> {% endcomment %}
          </div>
      </div>
  </nav>

       



                        
                    <form class="d-flex mt-3 mt-lg-0" role="search">
                        <input class="form-control me-2" type="search" placeholder="Search" aria-label="Search">
                        <button class="btn btn-outline-success" type="submit">Search</button>
                    </form>
                </div>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    
    </div>

    <!-- Page Content -->
    <div class="container py-4">
        {% block content %}{% endblock %}
    </div>

    <!-- Footer -->
    <footer class="bg-light text-dark py-4 border-top">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5 class="text-orange">TechShop</h5>
                    <p>Your one-stop shop for tech gadgets.</p>
                </div>
                <div class="col-md-4">
                    <h5 class="text-orange">Quick Links</h5>
                    <ul class="list-unstyled">
                        <li><a href="{% url 'home' %}" class="text-dark">Home</a></li>
                        <li><a href="#" class="text-dark">Shop</a></li>
                        <li><a href="#" class="text-dark">Cart</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5 class="text-orange">Contact</h5>
                    <p>Email: <EMAIL></p>
                    <p>Phone: (*************</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap and Custom Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{% static 'js/main.js' %}"></script>
</body>

</html>
