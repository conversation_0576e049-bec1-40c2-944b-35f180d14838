import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'authtry.settings')
django.setup()

from authtryapp.models import SectionHeading

def create_default_section_headings():
    """Create default section headings if they don't exist"""
    
    # Define default headings
    default_headings = [
        {
            'section_type': 'featured_products',
            'title': 'Featured Products',
            'subtitle': 'Discover our handpicked selection of premium tech products',
            'is_active': True
        },
        {
            'section_type': 'best_selling',
            'title': 'Best Selling Products',
            'subtitle': 'Our most popular products loved by customers',
            'is_active': True
        },
        {
            'section_type': 'new_arrivals',
            'title': 'New Arrivals',
            'subtitle': 'The latest tech additions to our collection',
            'is_active': True
        },
        {
            'section_type': 'top_deals',
            'title': 'Top Deals',
            'subtitle': 'Limited-time offers on premium tech products',
            'is_active': True
        },
        {
            'section_type': 'special_offers',
            'title': 'Special Offers',
            'subtitle': 'Exclusive deals and bundles for our customers',
            'is_active': True
        },
        {
            'section_type': 'clearance',
            'title': 'Clearance Sale',
            'subtitle': 'Last chance to grab these products at unbeatable prices',
            'is_active': True
        }
    ]
    
    # Create headings if they don't exist
    for heading_data in default_headings:
        section_type = heading_data['section_type']
        if not SectionHeading.objects.filter(section_type=section_type).exists():
            SectionHeading.objects.create(**heading_data)
            print(f"Created section heading: {heading_data['title']}")
        else:
            print(f"Section heading '{heading_data['title']}' already exists")

if __name__ == '__main__':
    print("Creating default section headings...")
    create_default_section_headings()
    print("Done!")
