{% extends 'admin/base_admin.html' %}

{% block title %}Promotions for {{ product.name }}{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="mt-4">Promotions for {{ product.name }}</h1>
    
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'admin_dashboard' %}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{% url 'admin_products' %}">Products</a></li>
            <li class="breadcrumb-item"><a href="{% url 'admin_product_edit' product.id %}">{{ product.name }}</a></li>
            <li class="breadcrumb-item active">Promotions</li>
        </ol>
    </nav>
    
    {% if messages %}
    <div class="row">
        <div class="col-12">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-info-circle me-1"></i>
                    Product Information
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            {% if product.image %}
                            <img src="{{ product.image.url }}" alt="{{ product.name }}" class="img-fluid rounded">
                            {% else %}
                            <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 150px;">
                                <i class="fas fa-image fa-3x text-muted"></i>
                            </div>
                            {% endif %}
                        </div>
                        <div class="col-md-8">
                            <h5>{{ product.name }}</h5>
                            <p class="mb-1"><strong>Price:</strong> ₨. {{ product.price|floatformat:2 }}</p>
                            <p class="mb-1"><strong>Category:</strong> {{ product.category.name|default:"None" }}</p>
                            <p class="mb-1"><strong>Brand:</strong> {{ product.brand.name|default:"None" }}</p>
                            <p class="mb-1"><strong>Stock Status:</strong> 
                                {% if product.stock_status == 'available' %}
                                <span class="badge bg-success">Available</span>
                                {% elif product.stock_status == 'preorder' %}
                                <span class="badge bg-warning text-dark">Pre-order</span>
                                {% else %}
                                <span class="badge bg-danger">Not Available</span>
                                {% endif %}
                            </p>
                            <a href="{% url 'admin_product_edit' product.id %}" class="btn btn-sm btn-primary mt-2">
                                <i class="fas fa-edit"></i> Edit Product
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-tags me-1"></i>
            Active Promotions
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>Sale Price</th>
                            <th>Discount</th>
                            <th>Promotion Type</th>
                            <th>Period</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for promo in promotions %}
                        <tr>
                            <td>₨. {{ promo.sale_price|floatformat:2 }}</td>
                            <td>
                                {% if promo.discount_percentage %}
                                {{ promo.discount_percentage|floatformat:0 }}%
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>
                                {% if promo.is_featured %}<span class="badge bg-primary">Featured</span>{% endif %}
                                {% if promo.is_best_seller %}<span class="badge bg-success">Best Seller</span>{% endif %}
                                {% if promo.is_new_arrival %}<span class="badge bg-info">New Arrival</span>{% endif %}
                                {% if promo.is_top_deal %}<span class="badge bg-danger">Top Deal</span>{% endif %}
                            </td>
                            <td>
                                {{ promo.start_date|date:"M d, Y" }}
                                {% if promo.end_date %}
                                - {{ promo.end_date|date:"M d, Y" }}
                                {% else %}
                                - Ongoing
                                {% endif %}
                            </td>
                            <td>
                                {% if promo.is_active %}
                                <span class="badge bg-success">Active</span>
                                {% else %}
                                <span class="badge bg-secondary">Inactive</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{% url 'admin_promotion_edit' promo.id %}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                                <a href="{% url 'admin_promotion_delete' promo.id %}" class="btn btn-danger btn-sm">
                                    <i class="fas fa-trash"></i> Delete
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">No promotions found for this product. Create one below.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-plus-circle me-1"></i>
            Add New Promotion for {{ product.name }}
        </div>
        <div class="card-body">
            <form method="post" action="{% url 'admin_product_promotions' product.id %}">
                {% csrf_token %}
                
                {% if form.non_field_errors %}
                <div class="alert alert-danger">
                    {% for error in form.non_field_errors %}
                    {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
                
                {{ form.product.as_hidden }}
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.sale_price.id_for_label }}" class="form-label">Sale Price*</label>
                            {{ form.sale_price }}
                            {% if form.sale_price.errors %}
                            <div class="text-danger">
                                {% for error in form.sale_price.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.discount_percentage.id_for_label }}" class="form-label">Discount Percentage</label>
                            {{ form.discount_percentage }}
                            <small class="form-text text-muted">Leave blank to calculate automatically</small>
                            {% if form.discount_percentage.errors %}
                            <div class="text-danger">
                                {% for error in form.discount_percentage.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.start_date.id_for_label }}" class="form-label">Start Date*</label>
                            {{ form.start_date }}
                            {% if form.start_date.errors %}
                            <div class="text-danger">
                                {% for error in form.start_date.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.end_date.id_for_label }}" class="form-label">End Date</label>
                            {{ form.end_date }}
                            <small class="form-text text-muted">Leave blank for ongoing promotion</small>
                            {% if form.end_date.errors %}
                            <div class="text-danger">
                                {% for error in form.end_date.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-12">
                        <label class="form-label">Promotion Type</label>
                        <div class="form-check">
                            {{ form.is_featured }}
                            <label class="form-check-label" for="{{ form.is_featured.id_for_label }}">
                                Featured Product
                            </label>
                        </div>
                        <div class="form-check">
                            {{ form.is_best_seller }}
                            <label class="form-check-label" for="{{ form.is_best_seller.id_for_label }}">
                                Best Seller
                            </label>
                        </div>
                        <div class="form-check">
                            {{ form.is_new_arrival }}
                            <label class="form-check-label" for="{{ form.is_new_arrival.id_for_label }}">
                                New Arrival
                            </label>
                        </div>
                        <div class="form-check">
                            {{ form.is_top_deal }}
                            <label class="form-check-label" for="{{ form.is_top_deal.id_for_label }}">
                                Top Deal
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-12">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> Save Promotion
                        </button>
                        <a href="{% url 'admin_product_edit' product.id %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Product
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Format datetime-local inputs to match Django's format
        const datetimeInputs = document.querySelectorAll('input[type="datetime-local"]');
        datetimeInputs.forEach(input => {
            if (input.value) {
                // Convert Django datetime format to HTML datetime-local format
                const djangoDateTime = input.value;
                if (djangoDateTime.includes('+')) {
                    // Remove timezone info if present
                    const localDateTime = djangoDateTime.split('+')[0];
                    input.value = localDateTime;
                }
            }
        });
    });
</script>
{% endblock %}
