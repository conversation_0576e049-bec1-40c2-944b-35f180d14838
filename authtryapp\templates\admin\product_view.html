{% extends 'admin/base_admin.html' %}

{% block title %}{{ product.name }} - Details{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mt-4 mb-3">
        <div>
            <h1>{{ product.name }}</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'admin_dashboard' %}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'admin_products' %}">Products</a></li>
                    {% if product.category %}
                    <li class="breadcrumb-item"><a href="{% url 'admin_products_by_category' product.category.id %}">{{ product.category.name }}</a></li>
                    {% endif %}
                    <li class="breadcrumb-item active">{{ product.name }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{% url 'admin_product_edit' product.id %}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit Product
            </a>
            <a href="{% url 'admin_product_delete' product.id %}" class="btn btn-outline-danger ms-2">
                <i class="fas fa-trash"></i> Delete
            </a>
        </div>
    </div>
    
    <div class="row">
        <!-- Product Image -->
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-image me-1"></i>
                    Product Image
                </div>
                <div class="card-body text-center">
                    {% if product.image %}
                    <img src="{{ product.image.url }}" alt="{{ product.name }}" class="img-fluid rounded" style="max-height: 300px;">
                    {% else %}
                    <div class="bg-light d-flex align-items-center justify-content-center rounded" style="height: 300px;">
                        <i class="fas fa-box fa-5x text-muted"></i>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Product Details -->
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-info-circle me-1"></i>
                    Product Details
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted small">Name</label>
                            <p class="font-weight-bold">{{ product.name }}</p>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted small">Category</label>
                            <p>
                                {% if product.category %}
                                <a href="{% url 'admin_products_by_category' product.category.id %}">
                                    {{ product.category.name }}
                                </a>
                                {% else %}
                                <span class="text-muted">No category assigned</span>
                                {% endif %}
                            </p>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted small">Price</label>
                            <p>${{ product.price|default:"N/A" }}</p>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted small">Stock Status</label>
                            <p>
                                <span class="badge {% if product.stock_status == 'available' %}bg-success{% elif product.stock_status == 'preorder' %}bg-warning text-dark{% else %}bg-danger{% endif %}">
                                    {{ product.get_stock_status_display }}
                                </span>
                            </p>
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label class="form-label text-muted small">Description</label>
                            <div class="p-3 bg-light rounded">
                                {% if product.description %}
                                <p class="mb-0">{{ product.description|linebreaks }}</p>
                                {% else %}
                                <p class="text-muted mb-0"><em>No description provided</em></p>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted small">Created On</label>
                            <p>{{ product.created_at|date:"F j, Y, g:i a" }}</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Frontend Preview -->
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-eye me-1"></i>
                    Frontend Preview
                </div>
                <div class="card-body">
                    <div class="product-card" style="max-width: 300px; margin: 0 auto;">
                        <div class="card h-100 shadow-sm">
                            {% if product.image %}
                            <img src="{{ product.image.url }}" class="card-img-top" alt="{{ product.name }}" style="height: 200px; object-fit: cover;">
                            {% endif %}
                            
                            <!-- Stock Status Badge -->
                            <div class="position-absolute top-0 start-0 m-3">
                                <span class="badge {% if product.stock_status == 'available' %}bg-success{% elif product.stock_status == 'preorder' %}bg-warning{% else %}bg-danger{% endif %}">
                                    {{ product.get_stock_status_display }}
                                </span>
                            </div>
                            
                            <div class="card-body d-flex flex-column">
                                <h5 class="card-title">{{ product.name }}</h5>
                                <p class="card-text text-muted small">{{ product.description|truncatewords:15 }}</p>
                                <div class="mt-auto">
                                    <p class="h5 mb-3">${{ product.price|floatformat:2 }}</p>
                                    <button class="btn btn-primary w-100" disabled>
                                        Add to Cart
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Related Products by Category -->
    {% if product.category %}
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-th-list me-1"></i>
            Other Products in {{ product.category.name }}
        </div>
        <div class="card-body">
            <div class="row">
                {% for related_product in product.category.products.all %}
                {% if related_product.id != product.id %}
                <div class="col-md-3 col-sm-6 mb-3">
                    <a href="{% url 'admin_product_view' related_product.id %}" class="text-decoration-none text-reset">
                        <div class="card h-100 shadow-sm">
                            <div class="d-flex align-items-center p-3">
                                {% if related_product.image %}
                                <img src="{{ related_product.image.url }}" alt="{{ related_product.name }}" class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                {% else %}
                                <div class="bg-light d-flex align-items-center justify-content-center rounded-circle me-3" style="width: 50px; height: 50px;">
                                    <i class="fas fa-box text-muted"></i>
                                </div>
                                {% endif %}
                                <div>
                                    <h6 class="mb-0">{{ related_product.name }}</h6>
                                    <small class="text-muted">${{ related_product.price|default:"N/A" }}</small>
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
                {% endif %}
                {% endfor %}
                
                {% if product.category.products.count <= 1 %}
                <div class="col-12">
                    <p class="text-muted text-center my-3">No other products in this category</p>
                </div>
                {% endif %}
            </div>
            
            <div class="text-end mt-3">
                <a href="{% url 'admin_products_by_category' product.category.id %}" class="btn btn-outline-primary">
                    View All Products in {{ product.category.name }} <i class="fas fa-arrow-right ms-1"></i>
                </a>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_css %}
<style>
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}
.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}
</style>
{% endblock %} 