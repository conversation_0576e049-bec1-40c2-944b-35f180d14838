{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>Admin Login - iTechStore</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            background-color: #f5f7fa;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            font-family: 'Roboto', sans-serif;
        }

        .admin-login-container {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 40px 0;
        }

        .admin-login-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 450px;
            overflow: hidden;
        }

        .admin-login-header {
            background-color: #1a1a1a;
            color: white;
            padding: 30px;
            text-align: center;
        }

        .admin-login-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }

        .admin-login-logo i {
            font-size: 2.5rem;
            margin-right: 10px;
        }

        .admin-login-title {
            font-size: 1.8rem;
            margin: 0;
            font-weight: 500;
        }

        .admin-login-subtitle {
            color: #cccccc;
            margin: 10px 0 0;
            font-weight: 300;
        }

        .admin-login-body {
            padding: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #666;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            border-color: #007bff;
            outline: none;
        }

        .admin-login-button {
            width: 100%;
            padding: 12px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .admin-login-button:hover {
            background-color: #0056b3;
        }

        .remember-me {
            display: flex;
            align-items: center;
            margin: 15px 0;
        }

        .remember-me input {
            margin-right: 8px;
        }

        .error-message {
            background-color: #dc3545;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 0.9rem;
        }

        .messages {
            margin-bottom: 20px;
        }

        .messages .success {
            background-color: #28a745;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
        }

        .messages .error {
            background-color: #dc3545;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="admin-login-container">
        <div class="admin-login-card">
            <div class="admin-login-header">
                <div class="admin-login-logo">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h1 class="admin-login-title">Admin Login</h1>
                <p class="admin-login-subtitle">Enter your credentials to access the admin panel</p>
            </div>
            <div class="admin-login-body">
                {% if messages %}
                <div class="messages">
                    {% for message in messages %}
                    <div class="{{ message.tags }}">
                        {{ message }}
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                <form method="post">
                    {% csrf_token %}
                    <div class="form-group">
                        <label for="id_username">Username</label>
                        {{ form.username }}
                    </div>
                    <div class="form-group">
                        <label for="id_password">Password</label>
                        {{ form.password }}
                    </div>
                    <div class="remember-me">
                        <input type="checkbox" id="remember" name="remember">
                        <label for="remember">Remember me</label>
                    </div>
                    <button type="submit" class="admin-login-button">Login</button>
                </form>
            </div>
        </div>
    </div>
</body>
</html>
