{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>My Profile - iTechStore</title>
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/sales_products.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        /* Additional styles for profile page */
        .profile-container {
            padding: 60px 0;
        }

        .profile-header {
            background-color: #f5f7fa;
            padding: 40px 0;
            margin-bottom: 40px;
            border-radius: 10px;
        }

        .profile-header-content {
            display: flex;
            align-items: center;
            gap: 30px;
        }

        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #4dabf7;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: white;
            flex-shrink: 0;
        }

        .profile-info h1 {
            font-size: 32px;
            margin-bottom: 10px;
            color: #333;
        }

        .profile-info p {
            color: #777;
            margin-bottom: 15px;
        }

        .profile-stats {
            display: flex;
            gap: 30px;
            margin-top: 20px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #4dabf7;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #777;
            font-size: 14px;
        }

        .profile-content {
            display: grid;
            grid-template-columns: 1fr 3fr;
            gap: 30px;
        }

        .profile-sidebar {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            padding: 20px;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: 10px;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            border-radius: 5px;
            color: #333;
            font-weight: 500;
            transition: background-color 0.3s;
        }

        .sidebar-menu a:hover {
            background-color: #f5f7fa;
        }

        .sidebar-menu a.active {
            background-color: #4dabf7;
            color: white;
        }

        .sidebar-menu i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .profile-main {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            padding: 30px;
        }

        .section-title {
            font-size: 20px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
            color: #333;
        }

        .order-list {
            margin-bottom: 40px;
        }

        .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }

        .order-info h3 {
            font-size: 16px;
            margin-bottom: 5px;
        }

        .order-info p {
            color: #777;
            font-size: 14px;
        }

        .order-status {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-delivered {
            background-color: #e8f5e9;
            color: #4caf50;
        }

        .status-processing {
            background-color: #fff3e0;
            color: #ff9800;
        }

        .status-cancelled {
            background-color: #ffebee;
            color: #f44336;
        }

        .view-btn {
            padding: 8px 15px;
            background-color: #f5f7fa;
            color: #333;
            border-radius: 5px;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.3s;
        }

        .view-btn:hover {
            background-color: #e9ecef;
        }

        .wishlist-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
        }

        .wishlist-item {
            border: 1px solid #eee;
            border-radius: 5px;
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .wishlist-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .wishlist-image {
            height: 150px;
            overflow: hidden;
        }

        .wishlist-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .wishlist-info {
            padding: 15px;
        }

        .wishlist-info h3 {
            font-size: 16px;
            margin-bottom: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .wishlist-price {
            color: #4dabf7;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .wishlist-actions {
            display: flex;
            gap: 10px;
        }

        .wishlist-btn {
            flex: 1;
            padding: 8px 0;
            text-align: center;
            border-radius: 3px;
            font-size: 12px;
            font-weight: 500;
        }

        .add-cart-btn {
            background-color: #4dabf7;
            color: white;
        }

        .remove-btn {
            background-color: #f5f5f5;
            color: #777;
        }

        @media (max-width: 992px) {
            .profile-content {
                grid-template-columns: 1fr;
            }

            .profile-sidebar {
                margin-bottom: 30px;
            }
        }

        @media (max-width: 768px) {
            .profile-header-content {
                flex-direction: column;
                text-align: center;
            }

            .profile-stats {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Top Navigation Bar -->
    <div class="top-nav">
        <div class="container">
            <div class="top-nav-left">
                <span>NPR</span>
                <i class="fas fa-chevron-down"></i>
            </div>
            <div class="top-nav-right">
                {% if user.is_authenticated %}
                    <span>Welcome, {{ user.username }}</span>
                    <a href="{% url 'logout' %}" class="sign-in">Logout</a>
                {% else %}
                    <a href="{% url 'register' %}" class="create-account">Create an account</a>
                    <a href="{% url 'login' %}" class="sign-in">Sign in</a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Main Navigation -->
    <nav class="main-nav">
        <div class="container">
            <div class="logo">
                <a href="{% url 'home' %}">
                    <h1><span class="i-text">i</span><span class="tech-text">Tech</span><span class="store-text">Store</span></h1>
                    <p class="tagline">Your Technology Destination</p>
                </a>
            </div>
            <div class="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </div>
            <div class="nav-links">
                <ul>
                    <li><a href="{% url 'home' %}">Home</a></li>
                    <li class="dropdown">
                        <a href="#">Shop <i class="fas fa-chevron-down"></i></a>
                        <div class="dropdown-content">
                            <a href="#">Laptops</a>
                            <a href="#">Smartphones</a>
                            <a href="#">Tablets</a>
                            <a href="#">Accessories</a>
                        </div>
                    </li>
                    <li><a href="#">Brands</a></li>
                    <li class="dropdown">
                        <a href="#">Blogs <i class="fas fa-chevron-down"></i></a>
                        <div class="dropdown-content">
                            <a href="#">Tech News</a>
                            <a href="#">Reviews</a>
                            <a href="#">Tutorials</a>
                        </div>
                    </li>
                    <li><a href="#">PC BLD</a></li>
                </ul>
            </div>
            <div class="nav-icons">
                <a href="#" class="search-icon"><i class="fas fa-search"></i></a>
                <a href="{% url 'cart_summary' %}" class="cart-icon">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count">{{ cart.total_items|default:"0" }}</span>
                </a>
            </div>
        </div>
    </nav>

    <!-- Profile Section -->
    <section class="profile-container">
        <div class="container">
            <div class="profile-header">
                <div class="container">
                    <div class="profile-header-content">
                        <div class="profile-avatar">
                            {% if user.first_name %}
                                {{ user.first_name|first|upper }}{{ user.last_name|first|upper }}
                            {% else %}
                                {{ user.username|first|upper }}
                            {% endif %}
                        </div>
                        <div class="profile-info">
                            <h1>{{ user.first_name }} {{ user.last_name }}</h1>
                            <p>{{ user.email }}</p>
                            <p>Member since {{ user.date_joined|date:"F Y" }}</p>

                            <div class="profile-stats">
                                <div class="stat-item">
                                    <div class="stat-value">0</div>
                                    <div class="stat-label">Orders</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">0</div>
                                    <div class="stat-label">Reviews</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">0</div>
                                    <div class="stat-label">Wishlist</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="profile-content">
                <div class="profile-sidebar">
                    <ul class="sidebar-menu">
                        <li><a href="#" class="active"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                        <li><a href="#"><i class="fas fa-shopping-bag"></i> Orders</a></li>
                        <li><a href="#"><i class="fas fa-heart"></i> Wishlist</a></li>
                        <li><a href="#"><i class="fas fa-star"></i> Reviews</a></li>
                        <li><a href="#"><i class="fas fa-user-edit"></i> Edit Profile</a></li>
                        <li><a href="#"><i class="fas fa-map-marker-alt"></i> Addresses</a></li>
                        <li><a href="#"><i class="fas fa-credit-card"></i> Payment Methods</a></li>
                        <li><a href="{% url 'logout' %}"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                    </ul>
                </div>

                <div class="profile-main">
                    <h2 class="section-title">Recent Orders</h2>
                    <div class="order-list">
                        <!-- Sample orders - would be replaced with real data -->
                        <div class="order-item">
                            <div class="order-info">
                                <h3>Order #12345</h3>
                                <p>Placed on May 15, 2023</p>
                            </div>
                            <span class="order-status status-delivered">Delivered</span>
                            <span class="order-price">$1,299.99</span>
                            <a href="#" class="view-btn">View Order</a>
                        </div>
                        <div class="order-item">
                            <div class="order-info">
                                <h3>Order #12346</h3>
                                <p>Placed on June 2, 2023</p>
                            </div>
                            <span class="order-status status-processing">Processing</span>
                            <span class="order-price">$799.99</span>
                            <a href="#" class="view-btn">View Order</a>
                        </div>
                    </div>

                    <h2 class="section-title">Wishlist</h2>
                    <div class="sales-products-grid">
                        <!-- Sample wishlist items - would be replaced with real data -->
                        <div class="sales-product-card" data-product-id="wishlist1">
                            <div class="sales-product-image-container">
                                <img src="https://www.apple.com/v/macbook-air-m2/b/images/overview/hero/hero_mba_m2__ejbs627dj7ee_large.jpg" alt="MacBook Air" class="sales-product-image">
                                <div class="sales-product-badges">
                                    <span class="availability-badge available">Available</span>
                                </div>
                            </div>
                            <div class="sales-product-info">
                                <div class="sales-product-title-section">
                                    <h3 class="sales-product-title">MacBook Air M2</h3>
                                    <p class="sales-product-specs">APPLE</p>
                                </div>
                                <div class="sales-product-pricing">
                                    <div class="sales-product-price">$1,199.99</div>
                                </div>
                                <div class="sales-product-actions">
                                    <button class="add-to-cart-btn primary" data-product-id="wishlist1">
                                        <i class="fas fa-shopping-cart"></i>
                                        Add to Cart
                                    </button>
                                    <button class="quick-view-btn" title="Remove from Wishlist" style="background: #dc3545;">
                                        <i class="fas fa-heart-broken"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="sales-product-card" data-product-id="wishlist2">
                            <div class="sales-product-image-container">
                                <img src="https://www.dell.com/content/dam/web-resources/cross-regional/consumer/notebook/xps-13-9315/blue/xps-13-9315-cnb-00000ff090-blue-fpr-rf-800x550.png" alt="Dell XPS" class="sales-product-image">
                                <div class="sales-product-badges">
                                    <span class="availability-badge available">Available</span>
                                </div>
                            </div>
                            <div class="sales-product-info">
                                <div class="sales-product-title-section">
                                    <h3 class="sales-product-title">Dell XPS 13</h3>
                                    <p class="sales-product-specs">DELL</p>
                                </div>
                                <div class="sales-product-pricing">
                                    <div class="sales-product-price">$1,299.99</div>
                                </div>
                                <div class="sales-product-actions">
                                    <button class="add-to-cart-btn primary" data-product-id="wishlist2">
                                        <i class="fas fa-shopping-cart"></i>
                                        Add to Cart
                                    </button>
                                    <button class="quick-view-btn" title="Remove from Wishlist" style="background: #dc3545;">
                                        <i class="fas fa-heart-broken"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-columns">
                <div class="footer-column">
                    <h3>Shop</h3>
                    <ul>
                        <li><a href="#">Laptops</a></li>
                        <li><a href="#">Smartphones</a></li>
                        <li><a href="#">Tablets</a></li>
                        <li><a href="#">Accessories</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Support</h3>
                    <ul>
                        <li><a href="#">Contact Us</a></li>
                        <li><a href="#">FAQs</a></li>
                        <li><a href="#">Shipping</a></li>
                        <li><a href="#">Returns</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Company</h3>
                    <ul>
                        <li><a href="#">About Us</a></li>
                        <li><a href="#">Careers</a></li>
                        <li><a href="#">Blog</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Connect</h3>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                    <div class="newsletter">
                        <h4>Subscribe to our newsletter</h4>
                        <form>
                            <input type="email" placeholder="Your email address">
                            <button type="submit">Subscribe</button>
                        </form>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 iTechStore. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="{% static 'js/script.js' %}"></script>
</body>
</html>
