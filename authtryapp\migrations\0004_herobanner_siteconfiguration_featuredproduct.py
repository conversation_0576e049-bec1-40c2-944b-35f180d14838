# Generated by Django 5.0.6 on 2025-05-25 03:12

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('authtryapp', '0003_sectionheading_productpromotion'),
    ]

    operations = [
        migrations.CreateModel(
            name='HeroBanner',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Main title for the banner', max_length=100)),
                ('subtitle', models.CharField(help_text='Subtitle or description', max_length=200)),
                ('brand_text', models.CharField(blank=True, help_text='Brand text (e.g., SAPPHIRE PULSE)', max_length=50, null=True)),
                ('primary_button_text', models.CharField(default='SHOP NOW', max_length=30)),
                ('primary_button_url', models.Char<PERSON>ield(blank=True, help_text='URL or anchor link (e.g., #featured-products)', max_length=200, null=True)),
                ('secondary_button_text', models.CharField(default='LEARN MORE', max_length=30)),
                ('secondary_button_url', models.CharField(blank=True, max_length=200, null=True)),
                ('background_gradient_start', models.CharField(default='#8B5CF6', help_text='Hex color code', max_length=7)),
                ('background_gradient_end', models.CharField(default='#EC4899', help_text='Hex color code', max_length=7)),
                ('image', models.ImageField(help_text='Banner image (recommended: 800x600px)', upload_to='hero_banners/')),
                ('image_alt', models.CharField(help_text='Alt text for the image', max_length=100)),
                ('is_active', models.BooleanField(default=True)),
                ('display_order', models.PositiveIntegerField(default=0, help_text='Order of display (lower numbers first)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Hero Banner',
                'verbose_name_plural': 'Hero Banners',
                'ordering': ['display_order', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SiteConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('hero_auto_slide_interval', models.PositiveIntegerField(default=5000, help_text='Auto slide interval in milliseconds (5000 = 5 seconds)')),
                ('hero_show_arrows', models.BooleanField(default=True)),
                ('hero_show_dots', models.BooleanField(default=True)),
                ('products_per_row_desktop', models.PositiveIntegerField(default=4)),
                ('products_per_row_tablet', models.PositiveIntegerField(default=2)),
                ('products_per_row_mobile', models.PositiveIntegerField(default=1)),
                ('site_name', models.CharField(default='iTechStore', max_length=100)),
                ('site_tagline', models.CharField(default='Your Technology Destination', max_length=200)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Site Configuration',
                'verbose_name_plural': 'Site Configuration',
            },
        ),
        migrations.CreateModel(
            name='FeaturedProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('section', models.CharField(choices=[('hero', 'Hero Section'), ('featured', 'Featured Products'), ('best_selling', 'Best Selling'), ('new_arrivals', 'New Arrivals'), ('top_deals', 'Top Deals')], max_length=20)),
                ('display_order', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='featured_in', to='authtryapp.product')),
            ],
            options={
                'verbose_name': 'Featured Product',
                'verbose_name_plural': 'Featured Products',
                'ordering': ['section', 'display_order'],
                'unique_together': {('product', 'section')},
            },
        ),
    ]
