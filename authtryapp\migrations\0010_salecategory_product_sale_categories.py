# Generated by Django 5.0.6 on 2025-05-26 11:04

import django.core.validators
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('authtryapp', '0009_add_special_offer_promotion'),
    ]

    operations = [
        migrations.CreateModel(
            name='SaleCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text="Sale category name (e.g., '10% Off', 'Free Headphones')", max_length=100)),
                ('slug', models.SlugField(blank=True, max_length=100, unique=True)),
                ('sale_type', models.CharField(choices=[('percentage', 'Percentage Discount'), ('fixed_amount', 'Fixed Amount Off'), ('free_item', 'Free Item with Purchase'), ('buy_x_get_y', 'Buy X Get Y Free'), ('bundle', 'Bundle Deal'), ('clearance', 'Clearance Sale'), ('flash_sale', 'Flash Sale'), ('seasonal', 'Seasonal Sale'), ('gift_with_purchase', 'Gift with Purchase'), ('free_shipping', 'Free Shipping')], default='percentage', max_length=20)),
                ('discount_percentage', models.DecimalField(blank=True, decimal_places=2, help_text='Discount percentage (0-100)', max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('discount_amount', models.DecimalField(blank=True, decimal_places=2, help_text='Fixed discount amount', max_digits=10, null=True)),
                ('free_item_description', models.CharField(blank=True, help_text="Description of free item (e.g., 'Free wireless mouse')", max_length=200)),
                ('buy_quantity', models.PositiveIntegerField(blank=True, help_text='Buy X quantity (for Buy X Get Y deals)', null=True)),
                ('get_quantity', models.PositiveIntegerField(blank=True, help_text='Get Y quantity free (for Buy X Get Y deals)', null=True)),
                ('badge_text', models.CharField(help_text='Text to show on product badge', max_length=50)),
                ('badge_color', models.CharField(choices=[('#dc3545', 'Red'), ('#fd7e14', 'Orange'), ('#ffc107', 'Yellow'), ('#28a745', 'Green'), ('#007bff', 'Blue'), ('#6f42c1', 'Purple'), ('#e83e8c', 'Pink'), ('#17a2b8', 'Teal'), ('#6c757d', 'Gray'), ('#343a40', 'Dark')], default='#dc3545', max_length=7)),
                ('description', models.TextField(blank=True, help_text='Detailed description of the sale')),
                ('start_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('end_date', models.DateTimeField(blank=True, help_text='Leave blank for ongoing sale', null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('is_featured', models.BooleanField(default=False, help_text='Show prominently on homepage')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Sale Category',
                'verbose_name_plural': 'Sale Categories',
                'ordering': ['-is_featured', '-start_date'],
            },
        ),
        migrations.AddField(
            model_name='product',
            name='sale_categories',
            field=models.ManyToManyField(blank=True, help_text='Sale categories this product belongs to', related_name='products', to='authtryapp.salecategory'),
        ),
    ]
