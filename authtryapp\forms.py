from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.models import User
from django.utils import timezone
from .models import Product, Category, Brand, ProductPromotion

class RegistrationForm(UserCreationForm):
    email = forms.EmailField(required=True, widget=forms.EmailInput(attrs={'class': 'form-control'}))
    first_name = forms.CharField(max_length=30, required=True, widget=forms.TextInput(attrs={'class': 'form-control'}))
    last_name = forms.CharField(max_length=30, required=True, widget=forms.TextInput(attrs={'class': 'form-control'}))

    class Meta:
        model = User
        fields = ('username', 'first_name', 'last_name', 'email', 'password1', 'password2')

    def __init__(self, *args, **kwargs):
        super(RegistrationForm, self).__init__(*args, **kwargs)
        # Add Bootstrap classes to form fields
        self.fields['username'].widget.attrs.update({'class': 'form-control'})
        self.fields['password1'].widget.attrs.update({'class': 'form-control'})
        self.fields['password2'].widget.attrs.update({'class': 'form-control'})

        # Customize help texts
        self.fields['username'].help_text = 'Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.'
        self.fields['password1'].help_text = 'Your password must contain at least 8 characters and can\'t be entirely numeric.'
        self.fields['password2'].help_text = 'Enter the same password as before, for verification.'

class ProductForm(forms.ModelForm):
    category = forms.ModelChoiceField(
        queryset=Category.objects.all(),
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )

    brand = forms.ModelChoiceField(
        queryset=Brand.objects.all().order_by('name'),
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )

    class Meta:
        model = Product
        fields = ['name', 'category', 'brand', 'price', 'description', 'image', 'stock_status']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'price': forms.NumberInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'image': forms.FileInput(attrs={'class': 'form-control'}),
            'stock_status': forms.Select(attrs={'class': 'form-control'})
        }

    def clean_price(self):
        price = self.cleaned_data.get('price')
        if price is not None and price < 0:
            raise forms.ValidationError("Price cannot be negative")
        return price

    def clean_image(self):
        image = self.cleaned_data.get('image')
        if image:
            if image.size > 2 * 1024 * 1024:  # 2MB limit
                raise forms.ValidationError("Image file too large ( > 2MB )")
        return image

class CategoryForm(forms.ModelForm):
    class Meta:
        model = Category
        fields = ['name', 'description', 'image']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter category name'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Enter category description'
            }),
            'image': forms.FileInput(attrs={
                'class': 'form-control'
            })
        }

    def clean_name(self):
        name = self.cleaned_data.get('name')
        if len(name) < 2:
            raise forms.ValidationError("Category name must be at least 2 characters long")
        # Check if category with this name already exists (for new categories)
        if not self.instance.pk and Category.objects.filter(name=name).exists():
            raise forms.ValidationError("A category with this name already exists")
        return name

    def clean_image(self):
        image = self.cleaned_data.get('image')
        if image:
            if image.size > 2 * 1024 * 1024:  # 2MB limit
                raise forms.ValidationError("Image file too large ( > 2MB )")
            # Check file extension
            valid_extensions = ['jpg', 'jpeg', 'png', 'gif']
            import os
            ext = os.path.splitext(image.name)[1][1:].lower()
            if ext not in valid_extensions:
                raise forms.ValidationError(f"Unsupported file extension. Use {', '.join(valid_extensions)}")
        return image

class BrandForm(forms.ModelForm):
    """Form for Brand model"""
    class Meta:
        model = Brand
        fields = ['name', 'description', 'logo']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter brand name'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'placeholder': 'Enter brand description', 'rows': 4}),
            'logo': forms.FileInput(attrs={'class': 'form-control'}),
        }


class ProductPromotionForm(forms.ModelForm):
    """Form for Product Promotion model"""

    start_date = forms.DateTimeField(
        required=True,
        widget=forms.DateTimeInput(attrs={
            'class': 'form-control',
            'type': 'datetime-local'
        }),
        initial=timezone.now
    )

    end_date = forms.DateTimeField(
        required=False,
        widget=forms.DateTimeInput(attrs={
            'class': 'form-control',
            'type': 'datetime-local'
        })
    )

    class Meta:
        model = ProductPromotion
        fields = [
            'product', 'sale_price', 'discount_percentage',
            'is_featured', 'is_best_seller', 'is_new_arrival', 'is_top_deal', 'is_special_offer',
            'start_date', 'end_date'
        ]
        widgets = {
            'product': forms.Select(attrs={'class': 'form-control'}),
            'sale_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'discount_percentage': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'is_featured': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_best_seller': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_new_arrival': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_top_deal': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_special_offer': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def clean(self):
        cleaned_data = super().clean()
        sale_price = cleaned_data.get('sale_price')
        product = cleaned_data.get('product')
        discount_percentage = cleaned_data.get('discount_percentage')
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        # Validate sale price
        if product and sale_price:
            if sale_price >= product.price:
                self.add_error('sale_price', 'Sale price must be less than the original price')

        # Validate discount percentage
        if discount_percentage is not None:
            if discount_percentage <= 0 or discount_percentage >= 100:
                self.add_error('discount_percentage', 'Discount percentage must be between 0 and 100')

        # Validate dates
        if start_date and end_date and start_date >= end_date:
            self.add_error('end_date', 'End date must be after start date')

        # Ensure at least one promotion type is selected
        promotion_types = [
            cleaned_data.get('is_featured'),
            cleaned_data.get('is_best_seller'),
            cleaned_data.get('is_new_arrival'),
            cleaned_data.get('is_top_deal'),
            cleaned_data.get('is_special_offer')
        ]

        if not any(promotion_types):
            self.add_error(None, 'At least one promotion type must be selected')

        return cleaned_data
