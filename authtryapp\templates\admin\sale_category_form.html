{% extends 'admin/base_admin.html' %}
{% load static %}

{% block title %}{{ action }} Sale Category{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'admin/css/sales_management_admin.css' %}">
<style>
.sale-form-page {
    padding: 20px;
    max-width: 1000px;
    margin: 0 auto;
}

.form-container {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.form-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    background: #fafbfc;
}

.form-section h3 {
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #667eea;
    font-size: 18px;
    font-weight: 600;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.form-group {
    flex: 1;
    min-width: 250px;
}

.form-group.full-width {
    flex: 100%;
}

.form-group.half-width {
    flex: 0 0 calc(50% - 10px);
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: #667eea;
    outline: none;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 10px;
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.checkbox-item input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.badge-preview-container {
    margin-top: 15px;
    padding: 15px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    border: 2px solid #e9ecef;
}

.badge-preview-label {
    font-weight: bold;
    margin-bottom: 8px;
    display: block;
    color: #495057;
}

.badge-preview {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    color: white;
}

.sale-type-helper {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border: 2px solid #2196f3;
    border-radius: 8px;
    padding: 12px;
    margin-top: 10px;
    font-size: 13px;
    color: #1565c0;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 2px solid #e9ecef;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    color: white;
    text-decoration: none;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    color: white;
    text-decoration: none;
}

.field-highlight {
    border-color: #667eea !important;
    background-color: #f8f9ff !important;
    box-shadow: 0 0 10px rgba(102, 126, 234, 0.3) !important;
}

.color-picker-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.color-picker {
    width: 60px !important;
    height: 40px !important;
    border: 3px solid #ddd !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
}

.color-picker:hover {
    border-color: #667eea !important;
    transform: scale(1.05) !important;
}

.priority-selector {
    display: flex;
    gap: 10px;
    align-items: center;
}

.priority-option {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 12px;
    border: 2px solid #dee2e6;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.priority-option:hover {
    border-color: #667eea;
    background: #f8f9ff;
}

.priority-option input[type="radio"] {
    margin: 0;
    width: auto;
}

.priority-stars {
    display: flex;
    gap: 2px;
}

.priority-star {
    width: 12px;
    height: 12px;
    background: #ffc107;
    border-radius: 50%;
}
</style>
{% endblock %}

{% block content %}
<div class="sale-form-page">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{{ action }} Sale Category</h1>
        <a href="{% url 'admin_sales_categories' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i>
            Back to Sales
        </a>
    </div>

    <div class="form-container">
        <form method="post" id="saleForm">
            {% csrf_token %}
            
            <!-- Basic Information -->
            <div class="form-section">
                <h3>📝 Basic Information</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label for="name">Sale Name *</label>
                        <input type="text" id="name" name="name" value="{% if sale %}{{ sale.name }}{% endif %}" required>
                    </div>
                    <div class="form-group">
                        <label for="sale_type">Sale Type *</label>
                        <select id="sale_type" name="sale_type" required>
                            <option value="">Select Sale Type</option>
                            {% for value, label in sale_type_choices %}
                            <option value="{{ value }}" {% if sale and sale.sale_type == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                            {% endfor %}
                        </select>
                        <div class="sale-type-helper" id="saleTypeHelper">
                            Select a sale type to see relevant configuration fields
                        </div>
                    </div>
                </div>
                
                <div class="form-group full-width">
                    <label for="description">Description</label>
                    <textarea id="description" name="description" placeholder="Describe this sale...">{% if sale %}{{ sale.description }}{% endif %}</textarea>
                </div>
            </div>

            <!-- Badge Configuration -->
            <div class="form-section">
                <h3>🏷️ Badge Configuration</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label for="badge_text">Badge Text *</label>
                        <input type="text" id="badge_text" name="badge_text" value="{% if sale %}{{ sale.badge_text }}{% endif %}" required>
                    </div>
                    <div class="form-group">
                        <label for="badge_color">Badge Color</label>
                        <div class="color-picker-container">
                            <input type="color" id="badge_color" name="badge_color" value="{% if sale %}{{ sale.badge_color }}{% else %}#dc3545{% endif %}" class="color-picker">
                            <span id="colorValue">{% if sale %}{{ sale.badge_color }}{% else %}#dc3545{% endif %}</span>
                        </div>
                    </div>
                </div>
                
                <div class="badge-preview-container">
                    <span class="badge-preview-label">🎨 Live Badge Preview:</span>
                    <span class="badge-preview" id="badgePreview">SAMPLE BADGE</span>
                </div>
            </div>

            <!-- Sale Configuration -->
            <div class="form-section">
                <h3>⚙️ Sale Configuration</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label for="discount_percentage">Discount Percentage (%)</label>
                        <input type="number" id="discount_percentage" name="discount_percentage" 
                               min="0" max="100" step="0.01" 
                               value="{% if sale and sale.discount_percentage %}{{ sale.discount_percentage }}{% endif %}">
                    </div>
                    <div class="form-group">
                        <label for="discount_amount">Discount Amount ($)</label>
                        <input type="number" id="discount_amount" name="discount_amount" 
                               min="0" step="0.01" 
                               value="{% if sale and sale.discount_amount %}{{ sale.discount_amount }}{% endif %}">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="minimum_purchase_amount">Minimum Purchase Amount ($)</label>
                        <input type="number" id="minimum_purchase_amount" name="minimum_purchase_amount" 
                               min="0" step="0.01" 
                               value="{% if sale and sale.minimum_purchase_amount %}{{ sale.minimum_purchase_amount }}{% endif %}">
                    </div>
                    <div class="form-group">
                        <label for="maximum_discount_amount">Maximum Discount Amount ($)</label>
                        <input type="number" id="maximum_discount_amount" name="maximum_discount_amount" 
                               min="0" step="0.01" 
                               value="{% if sale and sale.maximum_discount_amount %}{{ sale.maximum_discount_amount }}{% endif %}">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="free_item_description">Free Item Description</label>
                        <input type="text" id="free_item_description" name="free_item_description" 
                               value="{% if sale %}{{ sale.free_item_description }}{% endif %}">
                    </div>
                    <div class="form-group">
                        <label for="free_item_value">Free Item Value ($)</label>
                        <input type="number" id="free_item_value" name="free_item_value" 
                               min="0" step="0.01" 
                               value="{% if sale and sale.free_item_value %}{{ sale.free_item_value }}{% endif %}">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="buy_quantity">Buy Quantity</label>
                        <input type="number" id="buy_quantity" name="buy_quantity" 
                               min="1" 
                               value="{% if sale and sale.buy_quantity %}{{ sale.buy_quantity }}{% endif %}">
                    </div>
                    <div class="form-group">
                        <label for="get_quantity">Get Quantity (Free)</label>
                        <input type="number" id="get_quantity" name="get_quantity" 
                               min="1" 
                               value="{% if sale and sale.get_quantity %}{{ sale.get_quantity }}{% endif %}">
                    </div>
                </div>
            </div>

            <!-- Settings & Limits -->
            <div class="form-section">
                <h3>🎛️ Settings & Limits</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label for="priority">Priority Level</label>
                        <div class="priority-selector">
                            {% for value, label in priority_choices %}
                            <div class="priority-option">
                                <input type="radio" id="priority_{{ value }}" name="priority" value="{{ value }}" 
                                       {% if sale and sale.priority == value %}checked{% elif not sale and value == 2 %}checked{% endif %}>
                                <label for="priority_{{ value }}">{{ value }}</label>
                                <div class="priority-stars">
                                    {% for i in "12345" %}
                                    {% if forloop.counter <= value %}
                                    <div class="priority-star"></div>
                                    {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="coupon_code">Coupon Code</label>
                        <input type="text" id="coupon_code" name="coupon_code" 
                               value="{% if sale %}{{ sale.coupon_code }}{% endif %}">
                    </div>
                    <div class="form-group">
                        <label for="end_date">End Date</label>
                        <input type="datetime-local" id="end_date" name="end_date" 
                               value="{% if sale and sale.end_date %}{{ sale.end_date|date:'Y-m-d\TH:i' }}{% endif %}">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="max_total_uses">Maximum Total Uses</label>
                        <input type="number" id="max_total_uses" name="max_total_uses" 
                               min="1" 
                               value="{% if sale and sale.max_total_uses %}{{ sale.max_total_uses }}{% endif %}">
                    </div>
                    <div class="form-group">
                        <label for="max_uses_per_customer">Maximum Uses Per Customer</label>
                        <input type="number" id="max_uses_per_customer" name="max_uses_per_customer" 
                               min="1" 
                               value="{% if sale and sale.max_uses_per_customer %}{{ sale.max_uses_per_customer }}{% endif %}">
                    </div>
                </div>
                
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="is_active" name="is_active" 
                               {% if not sale or sale.is_active %}checked{% endif %}>
                        <label for="is_active">Active</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="is_featured" name="is_featured" 
                               {% if sale and sale.is_featured %}checked{% endif %}>
                        <label for="is_featured">Featured</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="is_member_only" name="is_member_only" 
                               {% if sale and sale.is_member_only %}checked{% endif %}>
                        <label for="is_member_only">Members Only</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="is_first_time_buyer_only" name="is_first_time_buyer_only" 
                               {% if sale and sale.is_first_time_buyer_only %}checked{% endif %}>
                        <label for="is_first_time_buyer_only">First Time Buyers Only</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="is_stackable" name="is_stackable" 
                               {% if sale and sale.is_stackable %}checked{% endif %}>
                        <label for="is_stackable">Stackable with Other Sales</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="requires_coupon_code" name="requires_coupon_code" 
                               {% if sale and sale.requires_coupon_code %}checked{% endif %}>
                        <label for="requires_coupon_code">Requires Coupon Code</label>
                    </div>
                </div>
            </div>

            <div class="form-actions">
                <a href="{% url 'admin_sales_categories' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i>
                    Cancel
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    {{ action }} Sale Category
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'admin/js/sales_management_admin.js' %}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize badge preview
    updateBadgePreview();
    
    // Update badge preview on input change
    document.getElementById('badge_text').addEventListener('input', updateBadgePreview);
    document.getElementById('badge_color').addEventListener('input', updateBadgePreview);
    
    // Update color value display
    document.getElementById('badge_color').addEventListener('input', function() {
        document.getElementById('colorValue').textContent = this.value;
    });
    
    // Sale type helper
    document.getElementById('sale_type').addEventListener('change', updateSaleTypeHelper);
    updateSaleTypeHelper(); // Initial call
    
    function updateBadgePreview() {
        const text = document.getElementById('badge_text').value || 'SAMPLE BADGE';
        const color = document.getElementById('badge_color').value || '#dc3545';
        const preview = document.getElementById('badgePreview');
        
        preview.style.backgroundColor = color;
        preview.textContent = text;
    }
    
    function updateSaleTypeHelper() {
        const saleType = document.getElementById('sale_type').value;
        const helper = document.getElementById('saleTypeHelper');
        
        const helpTexts = {
            'percentage': 'Fill in the "Discount percentage" field (e.g., 10 for 10% off)',
            'fixed_amount': 'Fill in the "Discount amount" field (e.g., 100 for $100 off)',
            'free_item': 'Fill in the "Free item description" field (e.g., "Free wireless mouse")',
            'buy_x_get_y': 'Fill in both "Buy quantity" and "Get quantity" fields (e.g., Buy 2, Get 1)',
            'bundle': 'Use "Discount percentage" for bundle savings',
            'clearance': 'Use "Discount percentage" for clearance discount',
            'flash_sale': 'Use "Discount percentage" and set an end date for urgency',
            'seasonal': 'Use "Discount percentage" for seasonal discount',
            'gift_with_purchase': 'Fill in "Free item description" for the gift',
            'free_shipping': 'No additional fields needed - just activate the sale',
            'loyalty_discount': 'Use "Discount percentage" and check "Member only"',
            'student_discount': 'Use "Discount percentage" and add verification requirements',
            'bulk_discount': 'Use "Discount percentage" and set "Minimum purchase amount"',
            'referral_bonus': 'Use "Discount amount" or "Discount percentage"',
            'first_time_buyer': 'Use "Discount percentage" and check "First time buyer only"'
        };
        
        helper.innerHTML = `<strong>💡 Configuration Tip:</strong> ${helpTexts[saleType] || 'Select a sale type to see relevant fields'}`;
        
        // Highlight relevant fields
        highlightRelevantFields(saleType);
    }
    
    function highlightRelevantFields(saleType) {
        // Remove all highlights first
        document.querySelectorAll('.field-highlight').forEach(field => {
            field.classList.remove('field-highlight');
        });
        
        const fieldMappings = {
            'percentage': ['discount_percentage', 'maximum_discount_amount'],
            'fixed_amount': ['discount_amount'],
            'free_item': ['free_item_description', 'free_item_value'],
            'buy_x_get_y': ['buy_quantity', 'get_quantity'],
            'bundle': ['discount_percentage', 'minimum_purchase_amount'],
            'clearance': ['discount_percentage'],
            'flash_sale': ['discount_percentage', 'end_date'],
            'seasonal': ['discount_percentage'],
            'gift_with_purchase': ['free_item_description', 'free_item_value'],
            'free_shipping': [],
            'loyalty_discount': ['discount_percentage', 'is_member_only'],
            'student_discount': ['discount_percentage'],
            'bulk_discount': ['discount_percentage', 'minimum_purchase_amount'],
            'referral_bonus': ['discount_percentage', 'discount_amount'],
            'first_time_buyer': ['discount_percentage', 'is_first_time_buyer_only']
        };
        
        const relevantFields = fieldMappings[saleType] || [];
        relevantFields.forEach(fieldName => {
            const field = document.getElementById(fieldName);
            if (field) {
                field.classList.add('field-highlight');
            }
        });
    }
});
</script>
{% endblock %}
