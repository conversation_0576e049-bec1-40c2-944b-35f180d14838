{% extends 'admin/base_admin.html' %}

{% block title %}Delete Brand{% endblock %}

{% block extra_css %}
<style>
    :root {
        --primary-color: #2563eb;
        --primary-hover: #1d4ed8;
        --secondary-color: #f0f9ff;
        --accent-color: #0284c7;
        --text-dark: #1e293b;
        --text-light: #64748b;
        --bg-light: #f8fafc;
        --bg-white: #ffffff;
        --shadow-sm: 0 1px 2px rgba(0,0,0,0.05);
        --shadow-md: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06);
        --shadow-lg: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05);
        --radius-sm: 0.375rem;
        --radius-md: 0.5rem;
        --radius-lg: 0.75rem;
        --danger-color: #ef4444;
        --danger-hover: #dc2626;
        --warning-color: #f59e0b;
    }
    
    .brand-logo {
        max-width: 180px;
        max-height: 100px;
        object-fit: contain;
        margin-bottom: 20px;
        border-radius: var(--radius-md);
        padding: 15px;
        background-color: var(--bg-white);
        box-shadow: var(--shadow-sm);
        border: 1px solid #e2e8f0;
    }
    
    .warning-icon {
        font-size: 3rem;
        color: var(--danger-color);
        margin-bottom: 20px;
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.6;
        }
    }
    
    .delete-warning {
        color: var(--danger-color);
        font-weight: 700;
        font-size: 1.25rem;
        margin-bottom: 15px;
        letter-spacing: -0.01em;
    }
    
    .product-count {
        font-weight: 600;
        color: var(--text-dark);
    }
    
    .card {
        border: none;
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-md);
        overflow: hidden;
        animation: fadeIn 0.5s ease-out;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .card-header {
        background: linear-gradient(to right, var(--danger-color), #f87171);
        color: white;
        font-weight: 600;
        padding: 1rem 1.25rem;
        border: none;
    }
    
    .card-body {
        padding: 2rem;
    }
    
    .confirmation-card {
        border: none;
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-sm);
        background-color: var(--bg-white);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .confirmation-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-md);
    }
    
    .card-title {
        font-weight: 700;
        color: var(--text-dark);
        font-size: 1.5rem;
        margin-bottom: 15px;
    }
    
    .card-text {
        color: var(--text-light);
        line-height: 1.6;
    }
    
    .alert-warning {
        background-color: #fff7ed;
        border-left: 4px solid var(--warning-color);
        border-top: 1px solid #fef3c7;
        border-right: 1px solid #fef3c7;
        border-bottom: 1px solid #fef3c7;
        color: #854d0e;
        border-radius: var(--radius-md);
    }
    
    .alert-info {
        background-color: #f0f9ff;
        border-left: 4px solid var(--accent-color);
        border-top: 1px solid #e0f2fe;
        border-right: 1px solid #e0f2fe;
        border-bottom: 1px solid #e0f2fe;
        color: #075985;
        border-radius: var(--radius-md);
    }
    
    .btn {
        padding: 0.7rem 1.5rem;
        border-radius: var(--radius-md);
        font-weight: 500;
        transition: all 0.2s;
    }
    
    .btn-secondary {
        background-color: #94a3b8;
        border-color: #94a3b8;
        color: white;
    }
    
    .btn-secondary:hover {
        background-color: #64748b;
        border-color: #64748b;
        box-shadow: 0 4px 6px rgba(100, 116, 139, 0.1);
    }
    
    .btn-danger {
        background-color: var(--danger-color);
        border-color: var(--danger-color);
        color: white;
    }
    
    .btn-danger:hover {
        background-color: var(--danger-hover);
        border-color: var(--danger-hover);
        box-shadow: 0 4px 6px rgba(239, 68, 68, 0.2);
        transform: translateY(-2px);
    }
    
    .breadcrumb {
        background-color: transparent;
        padding: 0;
        margin-bottom: 1.5rem;
    }
    
    .breadcrumb-item a {
        color: var(--primary-color);
        text-decoration: none;
        transition: color 0.2s;
    }
    
    .breadcrumb-item a:hover {
        color: var(--primary-hover);
        text-decoration: underline;
    }
    
    .breadcrumb-item.active {
        color: var(--text-light);
    }
    
    .page-heading {
        color: var(--text-dark);
        font-weight: 700;
        font-size: 1.75rem;
        margin-bottom: 0.75rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="page-heading">Delete Brand</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{% url 'admin_dashboard' %}">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="{% url 'admin_brands' %}">Brands</a></li>
        <li class="breadcrumb-item active">Delete Brand</li>
    </ol>
    
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-trash me-1"></i>
            Confirm Brand Deletion
        </div>
        <div class="card-body">
            <div class="text-center mb-4">
                <i class="fas fa-exclamation-triangle warning-icon"></i>
                <h5 class="delete-warning">Are you sure you want to delete this brand?</h5>
                <p class="text-muted">This action cannot be undone. All products associated with this brand will remain but lose their brand association.</p>
            </div>
            
            <div class="row justify-content-center mb-4">
                <div class="col-lg-6">
                    <div class="card confirmation-card">
                        <div class="card-body text-center">
                            {% if brand.logo %}
                                <img src="{{ brand.logo.url }}" alt="{{ brand.name }}" class="brand-logo">
                            {% else %}
                                <div class="text-muted mb-3"><i class="fas fa-image fa-3x"></i></div>
                            {% endif %}
                            <h4 class="card-title">{{ brand.name }}</h4>
                            {% if brand.description %}
                                <p class="card-text">{{ brand.description|truncatechars:150 }}</p>
                            {% endif %}
                            
                            {% with product_count=brand.products.count %}
                                {% if product_count > 0 %}
                                    <div class="alert alert-warning mt-3">
                                        <i class="fas fa-exclamation-circle me-2"></i>
                                        This brand has <span class="product-count">{{ product_count }}</span> products associated with it.
                                    </div>
                                {% else %}
                                    <div class="alert alert-info mt-3">
                                        <i class="fas fa-info-circle me-2"></i>
                                        This brand has no products associated with it.
                                    </div>
                                {% endif %}
                            {% endwith %}
                        </div>
                    </div>
                </div>
            </div>
            
            <form method="post" class="text-center">
                {% csrf_token %}
                <div class="d-flex justify-content-center gap-3">
                    <a href="{% url 'admin_brands' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Cancel
                    </a>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i> Delete Brand
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %} 