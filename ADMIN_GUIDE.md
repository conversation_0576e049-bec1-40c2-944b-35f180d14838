# 🎛️ iTechStore Admin Panel Guide

## 🔐 **Accessing the Admin Panel**

### **URL**: `http://localhost:8000/django-admin/`

### **Login Requirements**:
- Superuser account (created with `python manage.py createsuperuser`)
- Username and password

---

## 🎨 **Managing Hero Carousel Banners**

### **1. Hero Banners Section**
Navigate to: **AUTHTRYAPP** → **Hero Banners**

#### **Adding a New Banner**:
1. Click **"Add Hero Banner"**
2. Fill in the form:

**Banner Content**:
- **Title**: Main headline (e.g., "AMD Radeon™ RX 9070 XT GPU")
- **Subtitle**: Description text
- **Brand Text**: Small brand label (e.g., "SAPPHIRE PULSE")

**Button Configuration**:
- **Primary Button Text**: (e.g., "AVAILABLE NOW")
- **Primary Button URL**: Link or anchor (e.g., "#featured-products")
- **Secondary Button Text**: (e.g., "LEARN MORE")
- **Secondary Button URL**: Link destination

**Visual Design**:
- **Background Gradient Start**: Hex color (e.g., #8B5CF6)
- **Background Gradient End**: Hex color (e.g., #EC4899)
- **Image**: Upload banner image (recommended: 800x600px)
- **Image Alt**: Alt text for accessibility

**Display Settings**:
- **Is Active**: ✅ Check to show on website
- **Display Order**: Number (lower = appears first)

#### **Managing Existing Banners**:
- **Edit**: Click on banner title to modify
- **Reorder**: Change display order numbers
- **Activate/Deactivate**: Toggle "Is Active" checkbox
- **Delete**: Use action dropdown

---

## ⚙️ **Site Configuration**

Navigate to: **AUTHTRYAPP** → **Site Configuration**

### **Hero Section Settings**:
- **Auto Slide Interval**: Milliseconds (5000 = 5 seconds)
- **Show Arrows**: ✅ Display navigation arrows
- **Show Dots**: ✅ Display navigation dots

### **Product Display Settings**:
- **Products Per Row Desktop**: Number of products (default: 4)
- **Products Per Row Tablet**: Number of products (default: 2)
- **Products Per Row Mobile**: Number of products (default: 1)

### **Site Information**:
- **Site Name**: Website name (e.g., "iTechStore")
- **Site Tagline**: Subtitle (e.g., "Your Technology Destination")

---

## 🏷️ **Managing Featured Products**

Navigate to: **AUTHTRYAPP** → **Featured Products**

### **Adding Products to Sections**:
1. Click **"Add Featured Product"**
2. Select:
   - **Product**: Choose from dropdown
   - **Section**: Select where to display (Featured Products, Best Selling, etc.)
   - **Display Order**: Position in section
   - **Is Active**: ✅ Show on website

### **Available Sections**:
- **Hero Section**: Products in hero area
- **Featured Products**: Main featured section
- **Best Selling**: Best sellers carousel
- **New Arrivals**: Latest products
- **Top Deals**: Special offers

---

## 📝 **Section Headings**

Navigate to: **AUTHTRYAPP** → **Section Headings**

### **Customizing Section Titles**:
- **Section Type**: Choose section to customize
- **Title**: Main heading text
- **Subtitle**: Optional description
- **Is Active**: ✅ Show custom heading

---

## 🎯 **Quick Actions**

### **To Update Carousel Immediately**:
1. Go to **Hero Banners**
2. Edit existing banner or add new one
3. Save changes
4. Refresh website to see updates

### **To Change Carousel Speed**:
1. Go to **Site Configuration**
2. Modify **Hero Auto Slide Interval**
3. Save (e.g., 3000 = 3 seconds, 7000 = 7 seconds)

### **To Hide/Show Navigation**:
1. Go to **Site Configuration**
2. Toggle **Show Arrows** or **Show Dots**
3. Save changes

---

## 🔄 **Real-time Updates**

✅ **All changes are immediate** - no server restart required
✅ **Fallback system** - if no banners exist, default slides show
✅ **Mobile responsive** - all settings work across devices
✅ **SEO friendly** - proper alt texts and semantic HTML

---

## 🆘 **Troubleshooting**

### **Can't Access Admin**:
- Ensure you're using `/django-admin/` URL
- Check superuser credentials
- Verify server is running

### **Changes Not Showing**:
- Clear browser cache (Ctrl+F5)
- Check "Is Active" checkbox
- Verify display order numbers

### **Images Not Loading**:
- Check image file size (recommended < 2MB)
- Ensure proper file permissions
- Verify MEDIA_URL settings

---

## 📞 **Support**

For technical issues:
1. Check Django admin logs
2. Verify database migrations are applied
3. Ensure all required fields are filled
4. Contact system administrator if needed
