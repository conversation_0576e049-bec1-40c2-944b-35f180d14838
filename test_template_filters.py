#!/usr/bin/env python
"""
Test script to verify template filters are working correctly
"""
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'authtry.settings')
django.setup()

from authtryapp.models import HeroBanner
from authtryapp.templatetags.custom_filters import (
    get_primary_redirect_url,
    get_secondary_redirect_url,
    get_primary_button_text,
    get_secondary_button_text
)

def test_template_filters():
    print("🧪 Testing Template Filters...")
    print("=" * 50)
    
    # Get a test banner
    banner = HeroBanner.objects.filter(is_active=True).first()
    
    if not banner:
        print("❌ No active banners found for testing")
        return False
    
    print(f"📋 Testing with banner: {banner.title}")
    print(f"   Link Type: {banner.link_type}")
    print(f"   Link Target: {banner.link_target}")
    
    # Test primary filters
    print("\n🎯 Primary Button Filters:")
    try:
        primary_url = get_primary_redirect_url(banner)
        primary_text = get_primary_button_text(banner)
        print(f"   ✅ Primary URL: {primary_url}")
        print(f"   ✅ Primary Text: {primary_text}")
    except Exception as e:
        print(f"   ❌ Primary filters failed: {e}")
        return False
    
    # Test secondary filters
    print("\n🎯 Secondary Button Filters:")
    try:
        secondary_url = get_secondary_redirect_url(banner)
        secondary_text = get_secondary_button_text(banner)
        print(f"   ✅ Secondary URL: {secondary_url}")
        print(f"   ✅ Secondary Text: {secondary_text}")
        print(f"   ✅ Has Secondary: {banner.has_secondary_button()}")
    except Exception as e:
        print(f"   ❌ Secondary filters failed: {e}")
        return False
    
    # Test different banner types
    print("\n🔗 Testing Different Link Types:")
    link_types = ['product', 'category', 'anchor', 'custom']
    
    for link_type in link_types:
        test_banner = HeroBanner.objects.filter(link_type=link_type, is_active=True).first()
        if test_banner:
            url = get_primary_redirect_url(test_banner)
            text = get_primary_button_text(test_banner)
            print(f"   {link_type.title()}: {test_banner.title} → {url} ({text})")
    
    return True

def test_template_rendering():
    print("\n🎨 Testing Template Rendering...")
    print("=" * 50)
    
    from django.template import Template, Context
    from django.template.loader import get_template
    
    # Test a simple template with our filters
    template_string = """
    {% load custom_filters %}
    {% for banner in banners %}
        <a href="{{ banner|get_primary_redirect_url }}">{{ banner|get_primary_button_text }}</a>
        {% if banner.has_secondary_button %}
            <a href="{{ banner|get_secondary_redirect_url }}">{{ banner|get_secondary_button_text }}</a>
        {% endif %}
    {% endfor %}
    """
    
    try:
        template = Template(template_string)
        banners = HeroBanner.objects.filter(is_active=True)[:3]
        context = Context({'banners': banners})
        rendered = template.render(context)
        
        print("✅ Template rendered successfully!")
        print("📄 Sample output:")
        print(rendered.strip())
        return True
        
    except Exception as e:
        print(f"❌ Template rendering failed: {e}")
        return False

if __name__ == "__main__":
    filters_ok = test_template_filters()
    template_ok = test_template_rendering()
    
    print("\n" + "=" * 50)
    if filters_ok and template_ok:
        print("🎉 All template filters working correctly!")
        print("✅ Template syntax error should be resolved")
        print("💡 You can now visit the homepage without errors")
    else:
        print("❌ Some issues found - check the errors above")
    
    print("\n📝 Next Steps:")
    print("1. Visit the homepage to verify it loads")
    print("2. Check that all buttons work correctly")
    print("3. Test navigation arrows and dots")
    print("4. Verify all link types redirect properly")
