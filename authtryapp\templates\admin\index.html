{% extends "admin/index.html" %}
{% load i18n static %}

{% block extrahead %}
{{ block.super }}
<style>
    .sale-categories-dashboard {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 12px;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }
    
    .sale-categories-dashboard h2 {
        color: white;
        margin: 0 0 15px 0;
        font-size: 24px;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .sale-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
    }
    
    .stat-card {
        background: rgba(255, 255, 255, 0.1);
        padding: 15px;
        border-radius: 8px;
        text-align: center;
        backdrop-filter: blur(10px);
    }
    
    .stat-number {
        font-size: 28px;
        font-weight: bold;
        display: block;
        margin-bottom: 5px;
    }
    
    .stat-label {
        font-size: 12px;
        opacity: 0.9;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .sale-actions {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }
    
    .sale-action-btn {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        padding: 10px 16px;
        border-radius: 6px;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.2s;
        border: 1px solid rgba(255, 255, 255, 0.3);
    }
    
    .sale-action-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        color: white;
        text-decoration: none;
        transform: translateY(-1px);
    }
    
    .recent-sales {
        background: white;
        border-radius: 8px;
        padding: 15px;
        margin-top: 15px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .recent-sales h3 {
        margin: 0 0 10px 0;
        color: #333;
        font-size: 16px;
    }
    
    .recent-sale-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #eee;
    }
    
    .recent-sale-item:last-child {
        border-bottom: none;
    }
    
    .sale-badge-mini {
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 10px;
        font-weight: bold;
        color: white;
    }
    
    .sale-status {
        font-size: 12px;
        padding: 2px 6px;
        border-radius: 3px;
    }
    
    .status-active {
        background: #d4edda;
        color: #155724;
    }
    
    .status-inactive {
        background: #f8d7da;
        color: #721c24;
    }
</style>
{% endblock %}

{% block content %}
<div class="sale-categories-dashboard">
    <h2>
        🏷️ Sale Categories Dashboard
    </h2>
    
    <div class="sale-stats">
        <div class="stat-card">
            <span class="stat-number" id="total-sales">{{ total_sales|default:"0" }}</span>
            <span class="stat-label">Total Categories</span>
        </div>
        <div class="stat-card">
            <span class="stat-number" id="active-sales">{{ active_sales|default:"0" }}</span>
            <span class="stat-label">Active Sales</span>
        </div>
        <div class="stat-card">
            <span class="stat-number" id="featured-sales">{{ featured_sales|default:"0" }}</span>
            <span class="stat-label">Featured</span>
        </div>
        <div class="stat-card">
            <span class="stat-number" id="products-on-sale">{{ products_on_sale|default:"0" }}</span>
            <span class="stat-label">Products on Sale</span>
        </div>
    </div>
    
    <div class="sale-actions">
        <a href="{% url 'admin:authtryapp_salecategory_changelist' %}" class="sale-action-btn">
            📋 Manage Sale Categories
        </a>
        <a href="{% url 'admin:authtryapp_salecategory_add' %}" class="sale-action-btn">
            ➕ Create New Sale
        </a>
        <a href="{% url 'admin:authtryapp_product_changelist' %}" class="sale-action-btn">
            📦 Manage Products
        </a>
        <a href="/sales/" target="_blank" class="sale-action-btn">
            👁️ View Sales Page
        </a>
    </div>
    
    <div class="recent-sales">
        <h3>Recent Sale Categories</h3>
        <div id="recent-sales-list">
            <!-- Will be populated by JavaScript -->
            <div style="text-align: center; color: #666; padding: 20px;">
                Loading recent sales...
            </div>
        </div>
    </div>
</div>

{{ block.super }}
{% endblock %}

{% block extrajs %}
{{ block.super }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load sale statistics
    loadSaleStatistics();
    loadRecentSales();
    
    function loadSaleStatistics() {
        // This would typically fetch from an API endpoint
        // For now, we'll use placeholder data
        fetch('/admin/api/sale-stats/')
            .then(response => response.json())
            .then(data => {
                document.getElementById('total-sales').textContent = data.total || '10';
                document.getElementById('active-sales').textContent = data.active || '8';
                document.getElementById('featured-sales').textContent = data.featured || '3';
                document.getElementById('products-on-sale').textContent = data.products_on_sale || '25';
            })
            .catch(error => {
                console.log('Using default statistics');
                // Keep default values
            });
    }
    
    function loadRecentSales() {
        // Mock recent sales data
        const recentSales = [
            { name: '25% Off Laptops', badge_text: '25% OFF', badge_color: '#fd7e14', is_active: true },
            { name: 'Free Wireless Mouse', badge_text: 'FREE MOUSE', badge_color: '#28a745', is_active: true },
            { name: 'Flash Sale - 50% Off', badge_text: 'FLASH SALE', badge_color: '#e83e8c', is_active: true },
            { name: 'Buy 2 Get 1 Free', badge_text: 'BUY 2 GET 1 FREE', badge_color: '#6f42c1', is_active: false },
            { name: 'Save $100', badge_text: 'SAVE $100', badge_color: '#007bff', is_active: true }
        ];
        
        const container = document.getElementById('recent-sales-list');
        container.innerHTML = '';
        
        recentSales.forEach(sale => {
            const item = document.createElement('div');
            item.className = 'recent-sale-item';
            
            const statusClass = sale.is_active ? 'status-active' : 'status-inactive';
            const statusText = sale.is_active ? 'Active' : 'Inactive';
            
            item.innerHTML = `
                <div>
                    <strong>${sale.name}</strong>
                    <span class="sale-badge-mini" style="background-color: ${sale.badge_color}; margin-left: 8px;">
                        ${sale.badge_text}
                    </span>
                </div>
                <span class="sale-status ${statusClass}">${statusText}</span>
            `;
            
            container.appendChild(item);
        });
    }
    
    // Add some animation to the stats
    const statNumbers = document.querySelectorAll('.stat-number');
    statNumbers.forEach(stat => {
        const finalValue = parseInt(stat.textContent);
        let currentValue = 0;
        const increment = Math.ceil(finalValue / 20);
        
        const timer = setInterval(() => {
            currentValue += increment;
            if (currentValue >= finalValue) {
                currentValue = finalValue;
                clearInterval(timer);
            }
            stat.textContent = currentValue;
        }, 50);
    });
});
</script>
{% endblock %}
