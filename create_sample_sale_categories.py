#!/usr/bin/env python
"""
Script to create sample sale categories
"""
import os
import sys
import django
from decimal import Decimal
from datetime import timedelta

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'authtry.settings')
django.setup()

from authtryapp.models import SaleCategory, Product
from django.utils import timezone

def create_sample_sale_categories():
    print("🏷️ Creating Sample Sale Categories...")
    
    # Sample sale categories data
    sale_categories = [
        {
            'name': '10% Off Electronics',
            'sale_type': 'percentage',
            'badge_text': '10% OFF',
            'badge_color': '#dc3545',  # Red
            'discount_percentage': Decimal('10.00'),
            'description': 'Get 10% off on all electronics',
            'is_featured': True,
        },
        {
            'name': '25% Off Laptops',
            'sale_type': 'percentage',
            'badge_text': '25% OFF',
            'badge_color': '#fd7e14',  # Orange
            'discount_percentage': Decimal('25.00'),
            'description': 'Special discount on laptops',
            'is_featured': True,
        },
        {
            'name': 'Free Wireless Mouse',
            'sale_type': 'free_item',
            'badge_text': 'FREE MOUSE',
            'badge_color': '#28a745',  # Green
            'free_item_description': 'Free wireless mouse with laptop purchase',
            'description': 'Get a free wireless mouse with any laptop purchase',
            'is_featured': False,
        },
        {
            'name': 'Buy 2 Get 1 Free',
            'sale_type': 'buy_x_get_y',
            'badge_text': 'BUY 2 GET 1 FREE',
            'badge_color': '#6f42c1',  # Purple
            'buy_quantity': 2,
            'get_quantity': 1,
            'description': 'Buy 2 accessories, get 1 free',
            'is_featured': False,
        },
        {
            'name': 'Flash Sale - 50% Off',
            'sale_type': 'flash_sale',
            'badge_text': 'FLASH SALE',
            'badge_color': '#e83e8c',  # Pink
            'discount_percentage': Decimal('50.00'),
            'description': 'Limited time flash sale - 50% off selected items',
            'is_featured': True,
            'end_date': timezone.now() + timedelta(days=3),  # Ends in 3 days
        },
        {
            'name': 'Save $100',
            'sale_type': 'fixed_amount',
            'badge_text': 'SAVE $100',
            'badge_color': '#007bff',  # Blue
            'discount_amount': Decimal('100.00'),
            'description': 'Save $100 on premium products',
            'is_featured': False,
        },
        {
            'name': 'Free Shipping',
            'sale_type': 'free_shipping',
            'badge_text': 'FREE SHIPPING',
            'badge_color': '#17a2b8',  # Teal
            'description': 'Free shipping on all orders',
            'is_featured': False,
        },
        {
            'name': 'Clearance Sale',
            'sale_type': 'clearance',
            'badge_text': 'CLEARANCE',
            'badge_color': '#6c757d',  # Gray
            'discount_percentage': Decimal('30.00'),
            'description': 'Clearance sale - up to 30% off',
            'is_featured': False,
        },
        {
            'name': 'Gift with Purchase',
            'sale_type': 'gift_with_purchase',
            'badge_text': 'FREE GIFT',
            'badge_color': '#ffc107',  # Yellow
            'free_item_description': 'Free laptop bag',
            'description': 'Free laptop bag with any laptop purchase over $500',
            'is_featured': False,
        },
        {
            'name': 'Bundle Deal',
            'sale_type': 'bundle',
            'badge_text': 'BUNDLE DEAL',
            'badge_color': '#343a40',  # Dark
            'discount_percentage': Decimal('15.00'),
            'description': 'Save 15% when you buy laptop + accessories bundle',
            'is_featured': False,
        }
    ]
    
    created_count = 0
    
    for sale_data in sale_categories:
        # Check if sale category already exists
        if not SaleCategory.objects.filter(name=sale_data['name']).exists():
            sale = SaleCategory.objects.create(**sale_data)
            created_count += 1
            print(f"✅ Created: {sale.name} ({sale.badge_text})")
        else:
            print(f"⚠️ Already exists: {sale_data['name']}")
    
    print(f"\n🎉 Created {created_count} new sale categories!")
    
    # Show summary
    total_sales = SaleCategory.objects.count()
    active_sales = SaleCategory.objects.filter(is_active=True).count()
    featured_sales = SaleCategory.objects.filter(is_featured=True).count()
    
    print(f"\n📊 Summary:")
    print(f"   Total sale categories: {total_sales}")
    print(f"   Active sale categories: {active_sales}")
    print(f"   Featured sale categories: {featured_sales}")

def assign_products_to_sales():
    """Assign some products to sale categories for demonstration"""
    print("\n🔗 Assigning Products to Sale Categories...")
    
    # Get some products and sale categories
    products = Product.objects.all()[:10]  # First 10 products
    
    if not products.exists():
        print("⚠️ No products found. Please create some products first.")
        return
    
    # Assign products to different sales
    assignments = [
        ('10% Off Electronics', products[:3]),
        ('25% Off Laptops', products[1:4]),
        ('Free Wireless Mouse', products[2:5]),
        ('Flash Sale - 50% Off', products[0:2]),
        ('Save $100', products[3:6]),
    ]
    
    for sale_name, product_list in assignments:
        try:
            sale = SaleCategory.objects.get(name=sale_name)
            for product in product_list:
                sale.products.add(product)
            print(f"✅ Assigned {len(product_list)} products to '{sale_name}'")
        except SaleCategory.DoesNotExist:
            print(f"⚠️ Sale category '{sale_name}' not found")
    
    print("🎉 Product assignments completed!")

if __name__ == '__main__':
    create_sample_sale_categories()
    assign_products_to_sales()
    
    print("\n" + "="*60)
    print("🚀 Sample sale categories created successfully!")
    print("💡 You can now:")
    print("   1. Visit the Django admin to manage sale categories")
    print("   2. Use the management command: python manage.py manage_sales")
    print("   3. View products with sale badges on the website")
    print("="*60)
