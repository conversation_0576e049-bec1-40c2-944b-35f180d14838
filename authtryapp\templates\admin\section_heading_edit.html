{% extends 'admin/base_admin.html' %}

{% block title %}Edit Section Heading{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="mt-4">Edit Section Heading</h1>
    
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'admin_dashboard' %}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{% url 'admin_section_headings' %}">Section Headings</a></li>
            <li class="breadcrumb-item active">Edit</li>
        </ol>
    </nav>
    
    {% if messages %}
    <div class="row">
        <div class="col-12">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-edit me-1"></i>
            Edit Section Heading
        </div>
        <div class="card-body">
            <form method="post" action="{% url 'admin_section_heading_edit' heading.id %}">
                {% csrf_token %}
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="section_type" class="form-label">Section Type*</label>
                            <input type="text" value="{{ heading.get_section_type_display }}" class="form-control" readonly>
                            <input type="hidden" name="section_type" value="{{ heading.section_type }}">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="title" class="form-label">Title*</label>
                            <input type="text" name="title" id="title" class="form-control" value="{{ heading.title }}" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="subtitle" class="form-label">Subtitle</label>
                            <input type="text" name="subtitle" id="subtitle" class="form-control" value="{{ heading.subtitle|default:'' }}">
                        </div>
                    </div>
                </div>
                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" name="is_active" id="is_active" {% if heading.is_active %}checked{% endif %}>
                    <label class="form-check-label" for="is_active">
                        Active
                    </label>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Update Section Heading
                </button>
                <a href="{% url 'admin_section_headings' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Section Headings
                </a>
            </form>
        </div>
    </div>
</div>
{% endblock %}
