from django.urls import path
from django.conf import settings
from django.conf.urls.static import static
from django.shortcuts import render
from .views import home, login_view, logout_view, register_view, profile_view, category_view, product_detail, brand_list, brand_detail, sales_products_view, all_products_view, all_categories_view
from . import admin_views
from . import cart_views

def admin_help_view(request):
    return render(request, 'admin_help.html')


urlpatterns = [
    # Main pages
    path('', home, name='home'),  # Home page
    path('login/', login_view, name='login'),  # Login page
    path('logout/', logout_view, name='logout'),  # Logout action
    path('register/', register_view, name='register'),  # Registration page
    path('profile/', profile_view, name='profile'),  # User profile page
    path('admin-help/', admin_help_view, name='admin_help'),  # Admin help guide
    path('category/<slug:category_slug>/', category_view, name='category_view'),  # Category page
    path('product/<slug:product_slug>/', product_detail, name='product_detail'),
    path('products/<slug:product_slug>/', product_detail, name='product_detail_alt'),  # Alternative product URL

    # Brand pages
    path('brands/', brand_list, name='brand_list'),  # All brands
    path('brand/<slug:brand_slug>/', brand_detail, name='brand_detail'),  # Brand detail page

    # Sales products page
    path('sales/', sales_products_view, name='sales_products'),  # Sales products page

    # All products and categories pages
    path('products/', all_products_view, name='all_products'),  # All products page
    path('categories/', all_categories_view, name='all_categories'),  # All categories page

    # Admin routes
    path('admin/logout/', admin_views.admin_logout, name='admin_logout'),  # Admin logout
    path('admin/dashboard/', admin_views.admin_dashboard, name='admin_dashboard'),  # Admin dashboard
    path('admin/products/', admin_views.admin_products, name='admin_products'),
    path('admin/products/filter/<int:category_id>/', admin_views.admin_products_by_category, name='admin_products_by_category'),
    path('admin/products/add/', admin_views.admin_product_add, name='admin_product_add'),
    path('admin/products/edit/<int:product_id>/', admin_views.admin_product_edit, name='admin_product_edit'),
    path('admin/products/delete/<int:product_id>/', admin_views.admin_product_delete, name='admin_product_delete'),
    path('admin/products/view/<int:product_id>/', admin_views.admin_product_view, name='admin_product_view'),
    path('admin/users/', admin_views.admin_users, name='admin_users'),
    path('admin/users/edit/<int:user_id>/', admin_views.admin_user_edit, name='admin_user_edit'),
    path('admin/users/delete/<int:user_id>/', admin_views.admin_user_delete, name='admin_user_delete'),
    path('admin/users/view/<int:user_id>/', admin_views.admin_user_view, name='admin_user_view'),
    path('admin/orders/', admin_views.admin_orders, name='admin_orders'),
    path('admin/categories/', admin_views.admin_categories, name='admin_categories'),
    path('admin/categories/add/', admin_views.admin_category_add, name='admin_category_add'),
    path('admin/categories/edit/<int:category_id>/', admin_views.admin_category_edit, name='admin_category_edit'),
    path('admin/categories/delete/<int:category_id>/', admin_views.admin_category_delete, name='admin_category_delete'),

    # Admin Brand routes
    path('admin/brands/', admin_views.admin_brands, name='admin_brands'),
    path('admin/brands/add/', admin_views.admin_brand_add, name='admin_brand_add'),
    path('admin/brands/edit/<int:brand_id>/', admin_views.admin_brand_edit, name='admin_brand_edit'),
    path('admin/brands/delete/<int:brand_id>/', admin_views.admin_brand_delete, name='admin_brand_delete'),

    # Admin Section Headings routes
    path('admin/section-headings/', admin_views.admin_section_headings, name='admin_section_headings'),
    path('admin/section-headings/edit/<int:heading_id>/', admin_views.admin_section_heading_edit, name='admin_section_heading_edit'),

    # Admin Hero Banner routes
    path('admin/hero-banners/', admin_views.admin_hero_banners, name='admin_hero_banners'),
    path('admin/hero-banners/add/', admin_views.admin_hero_banner_add, name='admin_hero_banner_add'),
    path('admin/hero-banners/edit/<int:banner_id>/', admin_views.admin_hero_banner_edit, name='admin_hero_banner_edit'),
    path('admin/hero-banners/delete/<int:banner_id>/', admin_views.admin_hero_banner_delete, name='admin_hero_banner_delete'),

    # Admin Site Configuration routes
    path('admin/site-config/', admin_views.admin_site_config, name='admin_site_config'),

    # Admin Promotions routes
    path('admin/promotions/', admin_views.admin_promotions, name='admin_promotions'),
    path('admin/promotions/edit/<int:promotion_id>/', admin_views.admin_promotion_edit, name='admin_promotion_edit'),
    path('admin/promotions/delete/<int:promotion_id>/', admin_views.admin_promotion_delete, name='admin_promotion_delete'),
    path('admin/products/<int:product_id>/promotions/', admin_views.admin_product_promotions, name='admin_product_promotions'),

    path('admin/settings/', admin_views.admin_settings, name='admin_settings'),

    # Sales Management URLs
    path('admin/sales/', admin_views.admin_sales_dashboard, name='admin_sales_dashboard'),
    path('admin/sales/categories/', admin_views.admin_sales_categories, name='admin_sales_categories'),
    path('admin/sales/categories/add/', admin_views.admin_sale_category_add, name='admin_sale_category_add'),
    path('admin/sales/categories/edit/<int:sale_id>/', admin_views.admin_sale_category_edit, name='admin_sale_category_edit'),
    path('admin/sales/categories/delete/<int:sale_id>/', admin_views.admin_sale_category_delete, name='admin_sale_category_delete'),
    path('admin/sales/categories/<int:sale_id>/products/', admin_views.admin_sale_category_products, name='admin_sale_category_products'),

    # Cart URLs
    path('cart/', cart_views.cart_summary, name='cart_summary'),  # Cart summary page
    path('cart/add/<int:product_id>/', cart_views.add_to_cart, name='add_to_cart'),  # Add to cart

    # AJAX Cart URLs
    path('cart/update-item/', cart_views.update_cart_item, name='update_cart_item'),  # Update cart item via AJAX
    path('cart/remove-item/', cart_views.remove_from_cart, name='remove_from_cart'),  # Remove from cart via AJAX
    path('cart/clear/', cart_views.clear_cart, name='clear_cart'),  # Clear cart via AJAX

    # Legacy Cart URLs (for form submissions)
    path('cart/update/<int:item_id>/', cart_views.update_cart, name='update_cart_legacy'),  # Update cart item
    path('cart/remove/<int:item_id>/', cart_views.remove_from_cart_legacy, name='remove_from_cart_legacy'),  # Remove from cart
]
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
