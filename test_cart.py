#!/usr/bin/env python
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'authtry.settings')
django.setup()

from authtryapp.models import Product, Category, Cart, CartItem
from django.contrib.auth.models import User
from decimal import Decimal

def create_test_data():
    print("Creating test data...")
    
    # Create a test category
    category, created = Category.objects.get_or_create(
        name="Electronics",
        defaults={
            'description': 'Electronic devices and gadgets',
            'slug': 'electronics'
        }
    )
    print(f"Category: {category.name} ({'created' if created else 'exists'})")
    
    # Create test products
    products_data = [
        {
            'name': 'iPhone 15 Pro',
            'price': Decimal('999.99'),
            'description': 'Latest iPhone with advanced features',
            'stock_status': 'available'
        },
        {
            'name': 'Samsung Galaxy S24',
            'price': Decimal('899.99'),
            'description': 'Premium Android smartphone',
            'stock_status': 'available'
        },
        {
            'name': 'MacBook Pro M3',
            'price': Decimal('1999.99'),
            'description': 'Powerful laptop for professionals',
            'stock_status': 'available'
        }
    ]
    
    products = []
    for product_data in products_data:
        product, created = Product.objects.get_or_create(
            name=product_data['name'],
            defaults={
                'price': product_data['price'],
                'description': product_data['description'],
                'category': category,
                'stock_status': product_data['stock_status'],
                'slug': product_data['name'].lower().replace(' ', '-')
            }
        )
        products.append(product)
        print(f"Product: {product.name} - ₨{product.price} ({'created' if created else 'exists'})")
    
    # Create a test cart with items
    cart, created = Cart.objects.get_or_create(
        session_id='test-session-123',
        defaults={'user': None}
    )
    print(f"Cart: {cart.id} ({'created' if created else 'exists'})")
    
    # Add items to cart
    for i, product in enumerate(products[:2]):  # Add first 2 products
        cart_item, created = CartItem.objects.get_or_create(
            cart=cart,
            product=product,
            defaults={'quantity': i + 1}  # Different quantities
        )
        if not created:
            cart_item.quantity = i + 1
            cart_item.save()
        print(f"Cart Item: {cart_item.quantity}x {product.name} ({'created' if created else 'updated'})")
    
    print(f"\nCart Summary:")
    print(f"Total Items: {cart.total_items}")
    print(f"Total Price: ₨{cart.total_price}")
    
    return cart

if __name__ == '__main__':
    cart = create_test_data()
    print(f"\nTest cart created with session_id: {cart.session_id}")
    print("You can now test the cart functionality!")
